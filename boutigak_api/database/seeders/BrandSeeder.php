<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BrandSeeder extends Seeder
{
    public function run()
    {
        // Supprimer toutes les marques existantes
        Brand::truncate();

        // Liste des marques par catégorie (title_en)
        $brandMap = [
            'Electronics' => ['Samsung', 'Apple', 'Sony', 'LG', 'Panasonic'],
            'Smartphones' => ['Samsung', 'Apple', 'Huawei', 'Xiaomi', 'OnePlus'],
            'TVs' => ['Samsung', 'Sony', 'LG', 'TCL', 'Hisense'],
            'Cameras' => ['Canon', 'Nikon', 'Sony', 'Fujifilm', 'Olympus'],
            'Computers' => ['Dell', 'HP', 'Lenovo', 'Asus', 'Acer'],
            'Audio' => ['Bose', 'Sony', 'JBL', 'Sennheiser', 'Beats'],
            'Wearables' => ['Apple', 'Fitbit', '<PERSON>armin', '<PERSON>', '<PERSON>mi'],
            'Drones' => ['DJ<PERSON>', 'Parrot', 'Autel', '<PERSON>y<PERSON>', 'Holy Stone'],
            'Accessories' => ['Anker', 'Belkin', 'Logitech', 'Ugreen', 'Baseus'],

            'Home Appliances' => ['Whirlpool', 'Bosch', 'LG', 'Samsung', 'Electrolux'],
            'Kitchen' => ['KitchenAid', 'Tefal', 'Kenwood', 'Philips', 'Braun'],
            'Cleaning' => ['Dyson', 'iRobot', 'Hoover', 'Shark', 'Miele'],
            'Laundry' => ['Samsung', 'LG', 'Whirlpool', 'Bosch', 'AEG'],

            'Books' => ['Penguin', 'HarperCollins', 'Simon & Schuster', 'Hachette', 'Scholastic'],
            'Fiction' => ['Penguin', 'Vintage', 'Bloomsbury', 'Orbit', 'Tor'],
            'Non-fiction' => ['HarperOne', 'Simon & Schuster', 'Hachette', 'Penguin Random House', 'Macmillan'],
            'Educational' => ['Pearson', 'McGraw-Hill', 'Oxford', 'Cambridge', 'Houghton Mifflin'],
            'Comics' => ['Marvel', 'DC Comics', 'Dark Horse', 'Image Comics', 'Viz Media'],
            'Children' => ['Usborne', 'Scholastic', 'Ladybird', 'Disney Books', 'Nosy Crow'],

            'Toys' => ['Lego', 'Hasbro', 'Mattel', 'Playmobil', 'Fisher-Price'],
            'Outdoor' => ['Nerf', 'Step2', 'Little Tikes', 'Intex', 'Zuru'],
            'Puzzles' => ['Ravensburger', 'Clementoni', 'Educa', 'Melissa & Doug', 'Janod'],
            'Action Figures' => ['Hasbro', 'Bandai', 'Mattel', 'NECA', 'McFarlane Toys'],

            'Sports & Outdoors' => ['Nike', 'Adidas', 'Under Armour', 'Puma', 'Decathlon'],
            'Fitness' => ['Bowflex', 'NordicTrack', 'Peloton', 'ProForm', 'Technogym'],

            'Health & Wellness' => ['Philips', 'Omron', 'Braun', 'Theragun', 'Garmin'],

            'Beauty & Personal Care' => ['L\'Oréal', 'Nivea', 'Dove', 'Estée Lauder', 'Sephora'],
            'Skin Care' => ['Neutrogena', 'CeraVe', 'La Roche-Posay', 'Olay', 'The Ordinary'],
            'Perfumes' => ['Chanel', 'Dior', 'Armani', 'YSL', 'Versace'],
            'Makeup' => ['MAC', 'Maybelline', 'NARS', 'Fenty Beauty', 'Too Faced'],

            'Furniture' => ['Ikea', 'Wayfair', 'Ashley Furniture', 'West Elm', 'Herman Miller'],
            'Living Room' => ['Ikea', 'La-Z-Boy', 'West Elm', 'CB2', 'Joybird'],
            'Bedroom' => ['Ikea', 'Saatva', 'Emma', 'Casper', 'Wayfair'],

            'Jewelry' => ['Tiffany & Co.', 'Cartier', 'Pandora', 'Swarovski', 'Chopard'],
            'Necklaces' => ['Pandora', 'Tiffany', 'Swarovski', 'Chopard', 'Van Cleef & Arpels'],
            'Rings' => ['Cartier', 'Pandora', 'Tiffany', 'Bvlgari', 'De Beers'],

            // IMMOBILIER —> Unknown
            'Real Estate' => ['Unknown', 'Unknown', 'Unknown', 'Unknown', 'Unknown'],
            'Apartment' => ['Unknown', 'Unknown', 'Unknown', 'Unknown', 'Unknown'],
            'House' => ['Unknown', 'Unknown', 'Unknown', 'Unknown', 'Unknown'],
            'Commercial' => ['Unknown', 'Unknown', 'Unknown', 'Unknown', 'Unknown'],
            'Land' => ['Unknown', 'Unknown', 'Unknown', 'Unknown', 'Unknown'],

            'Automotive' => ['Toyota', 'Ford', 'Honda', 'BMW', 'Tesla'],
            'Sedan' => ['Toyota', 'Hyundai', 'Honda', 'Chevrolet', 'Kia'],
            'SUV' => ['Ford', 'Jeep', 'Toyota', 'BMW', 'Land Rover'],
            'Truck' => ['Ford', 'Ram', 'Chevrolet', 'GMC', 'Toyota'],
            'Motorcycle' => ['Yamaha', 'Honda', 'Harley-Davidson', 'Suzuki', 'Kawasaki'],
            'Parts & Accessories' => ['Bosch', 'Denso', 'ACDelco', 'Magna', 'Valeo'],
            'Electric Vehicles' => ['Tesla', 'BYD', 'Rivian', 'NIO', 'Lucid'],

            'Fashion' => ['Zara', 'H&M', 'Uniqlo', 'Gucci', 'Louis Vuitton'],

            'Clothing' => ['Nike', 'Adidas', 'Puma', 'Levi\'s', 'Zara'],
            'Shoes' => ['Nike', 'Adidas', 'Reebok', 'Skechers', 'Converse'],
            'Accessories' => ['Ray-Ban', 'Fossil', 'Michael Kors', 'Oakley', 'Gucci'],
            'Watches' => ['Rolex', 'Fossil', 'Casio', 'Tag Heuer', 'Swatch'],
            'Bags' => ['Michael Kors', 'Coach', 'Kate Spade', 'Furla', 'Guess'],

            'Pets' => ['Purina', 'Pedigree', 'Whiskas', 'Royal Canin', 'Hill\'s'],
            'Food & Beverages' => ['Coca-Cola', 'Pepsi', 'Nestlé', 'Unilever', 'Danone'],
            'Video Games' => ['PlayStation', 'Xbox', 'Nintendo', 'EA Sports', 'Ubisoft'],
            'Musical Instruments' => ['Yamaha', 'Fender', 'Gibson', 'Roland', 'Korg'],
            'Baby Products' => ['Pampers', 'Huggies', 'Chicco', 'Philips Avent', 'Johnson\'s'],
            'Tools & DIY' => ['Bosch', 'Black+Decker', 'Makita', 'DeWalt', 'Ryobi'],
            'Office Supplies' => ['Staples', '3M', 'Bic', 'Pilot', 'Canon'],
            'Home Decor' => ['Ikea', 'Wayfair', 'Zara Home', 'H&M Home', 'Urban Outfitters'],
            'Art & Collectibles' => ['Funko', 'Topps', 'Panini', 'Sideshow', 'Kidrobot'],
        ];

        DB::transaction(function () use ($brandMap) {
            $categories = Category::all();
            $existing = [];

            foreach ($categories as $category) {
                $title = $category->title_en;

                if (isset($brandMap[$title])) {
                    foreach ($brandMap[$title] as $brandName) {
                        $key = $brandName . '_' . $category->id;

                        if (in_array($key, $existing)) {
                            continue;
                        }

                        Brand::create([
                            'name' => $brandName,
                            'category_id' => $category->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);

                        $existing[] = $key;
                    }
                }
            }
        });

        Log::info('✅ Brands seeded successfully: ' . Brand::count() . ' brands created.');
    }
}
