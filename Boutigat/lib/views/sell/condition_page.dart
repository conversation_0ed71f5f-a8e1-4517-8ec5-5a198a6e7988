import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';

class ConditionPage extends StatelessWidget {
  final ItemController itemController = Get.find<ItemController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Condition'),
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: ListView.separated(
        itemCount: ItemCondition.values.length,
        itemBuilder: (context, index) {
          var condition = ItemCondition.values[index];
          return RadioListTile<ItemCondition>(
            title: Text(condition.name),
            value: condition,
            groupValue: itemController.condition.value.toEnum(),
            onChanged: (ItemCondition? newValue) {
              if (newValue != null) {
                itemController.condition.value = newValue.name;
                Navigator.pop(context);
              }
            },
            controlAffinity: ListTileControlAffinity.trailing, 
            dense: true,  
          );
        },
        separatorBuilder: (context, index) => Divider(),  
      ),
    );
  }
}

enum ItemCondition { newWithTags, newWithoutTags, veryGood, good, satisfactory }

extension ItemConditionExtension on String {
  ItemCondition toEnum() {
    switch (this) {
      case 'New with tags':
        return ItemCondition.newWithTags;
      case 'New without tags':
        return ItemCondition.newWithoutTags;
      case 'Very good':
        return ItemCondition.veryGood;
      case 'Good':
        return ItemCondition.good;
      case 'Satisfactory':
        return ItemCondition.satisfactory;
      default:
        return ItemCondition.satisfactory;
    }
  }

  String get name {
    switch (this.toEnum()) {
      case ItemCondition.newWithTags:
        return 'New with tags';
      case ItemCondition.newWithoutTags:
        return 'New without tags';
      case ItemCondition.veryGood:
        return 'Very good';
      case ItemCondition.good:
        return 'Good';
      case ItemCondition.satisfactory:
        return 'Satisfactory';
      default:
        return 'Unknown';
    }
  }
}
