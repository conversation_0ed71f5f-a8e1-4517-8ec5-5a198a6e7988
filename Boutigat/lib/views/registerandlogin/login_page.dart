import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/views/registerandlogin/forgetpassword_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/login_controller.dart';
import 'registerandlogin_widgets.dart';
import 'package:boutigak/views/widgets/customlogo_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';








class LoginPage extends StatelessWidget {












  const LoginPage({super.key});






  @override





  Widget build(BuildContext context) {





    ScreenUtil.init(context,); 


    final LoginController controller = Get.put(LoginController());



    double screenWidth = MediaQuery.of(context).size.width;




    double fieldHeight = MediaQuery.of(context).size.height * 0.08;




    double sidePadding = screenWidth * 0.0407;
    


    double verticalPadding = screenWidth * 0.02;




    return Scaffold(










       appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface, 
        elevation: 0, 
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.primary),
          onPressed: () {
            Get.back();
          },
        ),
      ),










      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            SizedBox(height: MediaQuery.of(context).size.height * 0.01),
            Padding(
               padding:  EdgeInsets.symmetric(horizontal: sidePadding),
               child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                 children: [
                   Text(
                              'login'.tr,
                              style: TextStyle(fontSize: 24.sp, fontWeight: AppFontWeights.bold),
                            ),
                 ],
               ),
             ),
              SizedBox(height: verticalPadding),
                    Padding(
                      padding:  EdgeInsets.symmetric(horizontal: sidePadding),
                      child: Row(
                        children: [
                          Text(
                              'dont_have_account'.tr,
                              style: TextStyle(
                                color: AppColors.onSurface,
                                
                                
                                fontSize:14.sp,
                              )),
                              SizedBox(width: 4.w),
                          TextLink(
                            text: "register".tr,
                            routeName: '/register',
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: verticalPadding),
            const CustomLogoWidget(),
            SizedBox(height: verticalPadding),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: sidePadding),
              child: Form(
                child: Column(
                  children: [





                    CustomTextFormField(
  hintText: 'phone_number'.tr,
  value: controller.phoneNumber,
  errorText: controller.phoneNumberError,
  width: screenWidth * 0.9184,
  prefixIcon: Icons.phone,
  inputFormatters: [
    FilteringTextInputFormatter.digitsOnly,
    LengthLimitingTextInputFormatter(8),
  ],
),




                    SizedBox(height: verticalPadding),







                    CustomTextFormField(
                      hintText: 'password'.tr,
                      value: controller.password,
                      errorText: controller.passwordError,
                      obscureText: true,
                      width: screenWidth * 0.9184,
                       prefixIcon: Icons.lock,
                      
                    ),








                    SizedBox(height: verticalPadding),










                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () {
                           
                            Get.to(() => ForgotPasswordPage());
                          },
                          child:  Text(
                            'forgot_password'.tr,
                            style: TextStyle(color: AppColors.primary),
                          ),
                        ),
                      ],
                    ),





                    SizedBox(height: verticalPadding),






                    Obx(() {
                      if (controller.isLoading.value) {
                        return SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primary),
                          ),
                        );
                      } else {
                        return CustomButton(
                          text: "login".tr,
                          onPressed: () {
                            if (controller.validateForm()) {
                              controller.login();
                            } else {
                              Get.snackbar("Error",
                                  "Please correct the errors in the form.",
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: AppColors.error,
                                  colorText: AppColors.onError);
                            }
                          },
                        );
                      }
                    }),






                    SizedBox(height: verticalPadding),





                   
                  ],


                ),


              ),



            ),


          ],


        ),


      ),



    );



  }






}
