import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
//import 'package:google_fonts/google_fonts.dart';
class AppColors {
  static const Color primary = Color(0xFF027782);
  static const Color primaryVariant = Color.fromARGB(255, 27, 186, 200);
  static const Color background = Color(0xFFF2F2F7);
  static const Color surface = Color.fromARGB(255, 255, 255, 255);
  static const Color error = Color(0xFFF44336);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFF000000);
  static const Color onBackground = Color(0xFF000000);
  static const Color onSurface = Color(0xFF000000);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color disabled = Color(0xFF565656);
  static const Color divider = Color(0xFFC6C6C9);
}
class DarkAppColors {
  static const Color primary = Color(0xFF027782);
  static const Color primaryVariant = Color.fromARGB(255, 27, 186, 200);
  static const Color background = Color(0xFF1C1C1E);
  static const Color surface = Color(0xFF1E1E1E);
  static const Color error = Color(0xFFCF6679);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFffffff);
  static const Color onBackground = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFFFFFFFF);
  static const Color onError = Color(0xFF000000);
  static const Color disabled = Color(0xFF3A3A3A);
  static const Color divider = Color(0xFF2C2C2C);
}
class AppTextSizes {
  static const double bodyText = 16.0;
  static const double subtitle = 14.0;
  static const double heading = 20.0;
  static const double subHeading = 18.0;
  static const double largeHeading = 24.0;
  static const double bodySmall = 12.0;
}
class AppFontWeights {
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
}
// Thème clair
final ThemeData lightTheme = ThemeData(
  colorScheme: const ColorScheme.light(
    primary: AppColors.primary,
    surface: AppColors.surface,
    background: AppColors.background ,
    error: AppColors.error,
    onPrimary: AppColors.onPrimary,
    onSecondary: AppColors.onSecondary,
    onSurface: AppColors.onSurface,
    onError: AppColors.onError,
  ),
  textTheme: GoogleFonts.robotoTextTheme(),
  dividerColor: AppColors.divider,
  disabledColor: AppColors.disabled,
  visualDensity: VisualDensity.adaptivePlatformDensity,
  textSelectionTheme: const TextSelectionThemeData(
    cursorColor: AppColors.primary,
  ),
  radioTheme: RadioThemeData(
    fillColor: WidgetStateProperty.all(AppColors.primary),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    labelStyle: TextStyle(color: AppColors.primary),
  ),
);

// Thème sombre
final ThemeData darkTheme = ThemeData(
  colorScheme: const ColorScheme.dark(
    primary: DarkAppColors.primary,
    surface: DarkAppColors.surface,
    error: DarkAppColors.error,
    onPrimary: DarkAppColors.onPrimary,
    onSecondary: DarkAppColors.onSecondary,
    onSurface: DarkAppColors.onSurface,
    onError: DarkAppColors.onError,
  ),
  textTheme: GoogleFonts.robotoTextTheme(ThemeData.dark().textTheme),
  dividerColor: DarkAppColors.divider,
  disabledColor: const Color.fromARGB(255, 152, 152, 152),
  visualDensity: VisualDensity.adaptivePlatformDensity,
  textSelectionTheme: const TextSelectionThemeData(
    cursorColor: DarkAppColors.primary,
  ),
  radioTheme: RadioThemeData(
    fillColor: WidgetStateProperty.all(DarkAppColors.primary),
  ),
  inputDecorationTheme: const InputDecorationTheme(
    labelStyle: TextStyle(color: DarkAppColors.primary),
  ),
 // textTheme: GoogleFonts.interTextTheme(),
);

class ThemeNotifier with ChangeNotifier {
  ThemeData _themeData;
  bool _isDarkTheme;

  ThemeNotifier(this._themeData, this._isDarkTheme);

  ThemeData get themeData => _themeData;
  bool get isDarkTheme => _isDarkTheme;

  void toggleTheme() {
    _isDarkTheme = !_isDarkTheme;
    _themeData = _isDarkTheme ? darkTheme : lightTheme;
    notifyListeners();
  }
}