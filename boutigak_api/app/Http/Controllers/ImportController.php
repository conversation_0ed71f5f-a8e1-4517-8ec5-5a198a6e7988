<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Imports\BrandImport;
use App\Imports\CategoryImport;
use Maatwebsite\Excel\Facades\Excel;

class ImportController extends Controller
{
    public function importBrandsView(): \Illuminate\View\View
    {
        return view('backoffice.brand.import-excel');
    }

    public function importBrands(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        $file = $request->file('file');
        $data = Excel::toArray([], $file);

        if (empty($data) || empty($data[0])) {
            return redirect()->back()->with('error', 'The file is empty or invalid.');
        }

        $header = array_map('strtolower', $data[0][0]); // première ligne
        $expectedHeader = ['name', 'category_id'];

        if (array_diff($expectedHeader, $header)) {
            return redirect()->back()->with('error', 'Invalid file format. Please ensure the file has the correct columns: name, category_id.');
        }

        try {
            Excel::import(new BrandImport, $file);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de l’import : ' . $e->getMessage());
        }

        return redirect()->back()->with('success', 'Brands imported successfully.');
    }

    public function importCategoriesView(): \Illuminate\View\View
    {
        return view('backoffice.category.import-excel');
    }

    public function importCategories(Request $request): \Illuminate\Http\RedirectResponse
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        $file = $request->file('file');
        $data = Excel::toArray([], $file);

        if (empty($data) || empty($data[0])) {
            return redirect()->back()->with('error', 'The file is empty or invalid.');
        }

        $header = array_map('strtolower', $data[0][0]);
        $expectedHeader = ['title_en', 'title_ar', 'title_fr', 'parent_id'];

        if (array_diff($expectedHeader, $header)) {
            return redirect()->back()->with('error', 'Invalid file format. Please ensure the file has the correct columns: title_en, title_ar, title_fr, parent_id.');
        }

        try {
            Excel::import(new CategoryImport, $file);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error during import: ' . $e->getMessage());
        }

        return redirect()->back()->with('success', 'Categories imported successfully.');
    }
}
