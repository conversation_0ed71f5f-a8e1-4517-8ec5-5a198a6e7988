import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/badge_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/customlogo_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/nav_controller.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/search/search_page.dart';
import 'package:boutigak/views/sell/sell_page.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/boutigat_store/boutique_store_page.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_nav_bar/google_nav_bar.dart';

class NavigationBarPage extends StatelessWidget {
  const NavigationBarPage({super.key});

  @override
  Widget build(BuildContext context) {
    final NavController navController = Get.find<NavController>();
    final AuthController authController = Get.find<AuthController>();
    final BadgeController badgeController = Get.find<BadgeController>();

    Widget buildLoginPrompt() {
      return Scaffold(
        appBar: CustomAppBar(
          titleText: "Login",
          icon: FontAwesomeIcons.solidUser,
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(height: 150),
            const CustomLogoWidget(),
            const SizedBox(height: 30),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Vous devez être connecté pour accéder à cette section.',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 10),
            CustomButton(
              text: "Login",
              onPressed: () => Get.to(() => LoginPage()),
            ),
          ],
        ),
      );
    }

    return Obx(
      () => Scaffold(
        body: authController.tokenLoading.value
            ? const Center(child: CircularProgressIndicator())
            : Obx(() {
                final int currentTab = navController.tabIndex.value;

                if ((currentTab == 2 || currentTab == 3) &&
                    !authController.isAuthenticated.value) {
                  return buildLoginPrompt();
                } else {
                  return IndexedStack(
                    index: currentTab,
                    children: [
                      HomePage(),
                      SearchPage(),
                      Container(),
                      InboxPage(),
                      StorePage(),
                    ],
                  );
                }
              }),
        bottomNavigationBar: Obx(() {
          return Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  blurRadius: 10,
                  color: Colors.black.withOpacity(0.1),
                )
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, bottom: 32, top: 12),
              child: GNav(
                selectedIndex: navController.tabIndex.value,
                onTabChange: (index) {
                  if (index == 2) {
                    if (!authController.isAuthenticated.value) {
                      navController.changeTabIndex(2);
                    } else {
                      Get.to(
                        () => SellPage(),
                        transition: Transition.zoom,
                        duration: const Duration(milliseconds: 0),
                      );
                    }
                  } else if (index == 3) {
                    navController.changeTabIndex(index);
                    if (authController.isAuthenticated.value) {
                      // badgeController.resetBadge('messages');
                      // badgeController.resetBadge('notifications');
                    }
                  } else if (index == 4){
                    navController.changeTabIndex(index);

                   final StoreController storeController = Get.find<StoreController>();

                  storeController.fetchRecomandedStores();

                  }
                  else {
                    navController.changeTabIndex(index);
                  }
                },
                haptic: true,
                curve: Curves.easeIn,
                duration: const Duration(milliseconds: 200),
                gap: 8,
                activeColor: AppColors.primary,
                iconSize: 20,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                tabs: [
                  GButton(
                    iconActiveColor: AppColors.primary,
                    textColor: AppColors.primary,
                    backgroundColor: AppColors.primary.withOpacity(.2),
                    icon: FontAwesomeIcons.house,
                    text: 'home'.tr,
                  ),
                  GButton(
                    iconActiveColor: AppColors.primary,
                    textColor: AppColors.primary,
                    backgroundColor: AppColors.primary.withOpacity(.2),
                    icon: FontAwesomeIcons.search,
                    text: 'search'.tr,
                  ),
                  GButton(
                    iconActiveColor: AppColors.primary,
                    textColor: AppColors.primary,
                    backgroundColor: AppColors.primary.withOpacity(.2),
                    icon: FontAwesomeIcons.plus,
                    text: 'sell'.tr,
                  ),
                  GButton(
                    gap: 10,
                    iconActiveColor: AppColors.primary,
                    textColor: AppColors.primary,
                    backgroundColor: AppColors.primary.withOpacity(.2),
                    text: 'inbox'.tr,
                    icon: FontAwesomeIcons.envelope,
                    leading: Obx(() {
                      int badgeCount =
                          badgeController.getModuleCount('messages') +
                              badgeController.getModuleCount('notifications');

                      return AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        transitionBuilder: (child, animation) =>
                            ScaleTransition(scale: animation, child: child),
                        child: badgeCount > 0
                            ? Badge(
                                key: ValueKey<int>(badgeCount),
                                backgroundColor: Colors.red.shade100,
                                offset: const Offset(8, -8),
                                label: Text(
                                  badgeCount > 9 ? '9+' : badgeCount.toString(),
                                  style: TextStyle(
                                    color: Colors.red.shade900,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                child: Icon(
                                  FontAwesomeIcons.envelope,
                                  color: navController.tabIndex.value == 3
                                      ? AppColors.primary
                                      : Colors.black,
                                  size: 20,
                                ),
                              )
                            : Icon(
                                key: const ValueKey<int>(0),
                                FontAwesomeIcons.envelope,
                                color: navController.tabIndex.value == 3
                                    ? AppColors.primary
                                    : Colors.black,
                                size: 20,
                              ),
                      );
                    }),
                  ),
                  GButton(
                    iconActiveColor: AppColors.primary,
                    textColor: AppColors.primary,
                    backgroundColor: AppColors.primary.withOpacity(.2),
                    icon: FontAwesomeIcons.store,
                    text: 'stores'.tr,
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
