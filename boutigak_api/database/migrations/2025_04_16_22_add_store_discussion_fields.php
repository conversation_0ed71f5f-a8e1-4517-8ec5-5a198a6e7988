<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('discution', function (Blueprint $table) {
            $table->boolean('is_store_discussion')->default(false);
            // $table->unsignedBigInteger('store_id')->nullable();
            // $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
        });

        Schema::table('discution_message', function (Blueprint $table) {
            $table->boolean('is_store_discussion')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};