import 'package:intl/intl.dart';
import 'package:boutigak/controllers/auth_controller.dart';


class Message {
  final String? content;
  final bool sentByMe;
  final DateTime timestamp;
  final bool isOffer;
  final double? price; 
  final String? dateCategory;

  Message({
    this.content,
    required this.sentByMe,
    required this.timestamp,
    this.isOffer = false,
    this.price,
     this.dateCategory,
  });

  String get formattedTime => DateFormat('HH:mm').format(timestamp);

  Map<String, dynamic> toJson() {
    return {
      'content': content,
    'sent_by_me': sentByMe,
      'timestamp': timestamp.toIso8601String(),
      'is_offer': isOffer,
      'price': price,
    };
  }

   factory Message.fromJson(Map<String, dynamic> json, String dateCategory) {
    return Message(
      content: json['content'] ?? '',
      sentByMe: json['sent_by_me'],
      timestamp: DateTime.parse(json['created_at']),
      isOffer: json['is_an_offer'] ?? false,
      price: json['price'] != null ? double.tryParse(json['price']) : null,
      dateCategory: dateCategory, 
    );
  }
}
