import Flutter
import UIKit
import <PERSON>SDKCreativeKit
import GoogleMaps
import app_links


@main
@objc class AppDelegate: FlutterAppDelegate {
    private let CHANNEL = "com.example.snapchat_share"
    
    // Initialize the Snapchat API
    var snapAPI: SCSDKSnapAPI!

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> <PERSON><PERSON> {
        

           // Retrieve the link from parameters
        if let url = AppLinks.shared.getLink(launchOptions: launchOptions) {
          // We have a link, propagate it to your Flutter app
          AppLinks.shared.handleLink(url: url)
          return true // Returning true will stop the propagation to other packages
        }
        // Google Maps API Key Setup (if needed)
        GMSServices.provideAPIKey("AIzaSyBBVmQvPJIvxKJqlrgbelKtYdEJnF_GcF0")
        
        // Initialize the Snapchat API
        snapAPI = SCSDKSnapAPI()

        if #available(iOS 10.0, *) {
          UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
        }
        
        // Flutter setup
        let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
        let snapchatShareChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
        
        snapchatShareChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
            if call.method == "shareToCamera" {
                guard let args = call.arguments as? [String: Any],
                      let imageData = args["imageData"] as? FlutterStandardTypedData,
                      let caption = args["caption"] as? String else {
                    result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
                    return
                }
                
                // Call the new share to Snapchat function using SCSDKCreativeKit
                self.shareToSnapchat(imageData: imageData.data, caption: caption) { success in
                    if success {
                        result(nil)
                    } else {
                        result(FlutterError(code: "SHARE_ERROR", message: "Failed to share to Snapchat", details: nil))
                    }
                }
            } else {
                result(FlutterMethodNotImplemented)
            }
        }
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    // Function to share using SCSDKCreativeKit
    private func shareToSnapchat(imageData: Data, caption: String, completion: @escaping (Bool) -> Void) {
        // Create UIImage from data
        guard let image = UIImage(data: imageData) else {
            print("Failed to create UIImage from data.")
            completion(false)
            return
        }

        // Create the Snap Sticker from UIImage
        let snapSticker = SCSDKSnapSticker(stickerImage: image)

        // Prepare the Snap Content with Sticker and Caption
        let snapContent = SCSDKNoSnapContent()
        snapContent.sticker = snapSticker
        // snapContent.caption = caption
        snapContent.attachmentUrl = "https://boutigak.com/"

        // Use SCSDKSnapAPI to send the content
        snapAPI.startSending(snapContent) { (error: Error?) in
            if let error = error {
                print("Failed to share to Snapchat: \(error.localizedDescription)")
                completion(false)
            } else {
                print("Shared to Snapchat successfully.")
                completion(true)
            }
        }
    }
}
