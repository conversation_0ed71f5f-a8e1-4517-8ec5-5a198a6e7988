<?php
/**
 * Server Monitoring Dashboard - Entry Point
 * 
 * This file redirects to the main dashboard or serves it directly
 */

// Check if this is an API request
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
if (strpos($requestUri, '/api/') !== false) {
    // Let the web server handle API requests normally
    return false;
}

// For root requests, serve the dashboard
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Monitoring Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }

        .status-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
        }

        .status-item.online {
            border-left: 5px solid #10b981;
        }

        .status-item.warning {
            border-left: 5px solid #f59e0b;
        }

        .status-item.offline {
            border-left: 5px solid #ef4444;
        }

        .status-item h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .status-item .value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .metric-row:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
        }

        .metric-value {
            font-weight: bold;
            color: #059669;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        .progress-fill.danger {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 15px;
        }

        .server-tabs {
            display: flex;
            margin-bottom: 20px;
            background: white;
            border-radius: 10px;
            padding: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab.active {
            background: #667eea;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #f3f4f6;
        }

        .server-content {
            display: none;
        }

        .server-content.active {
            display: block;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .last-updated {
            text-align: center;
            color: white;
            margin-top: 20px;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .status-bar {
                flex-direction: column;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .server-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-server"></i> Server Monitoring Dashboard</h1>
            <p>Real-time monitoring of your PostgreSQL infrastructure</p>
        </div>

        <div class="status-bar">
            <div class="status-item online">
                <h3>Database Server</h3>
                <div class="value" id="db-status">Online</div>
                <small>Last check: <span id="db-last-check">--</span></small>
            </div>
            <div class="status-item online">
                <h3>App Server 1</h3>
                <div class="value" id="app1-status">Online</div>
                <small>Last check: <span id="app1-last-check">--</span></small>
            </div>
            <div class="status-item online">
                <h3>App Server 2</h3>
                <div class="value" id="app2-status">Online</div>
                <small>Last check: <span id="app2-last-check">--</span></small>
            </div>
        </div>

        <div class="server-tabs">
            <div class="tab active" onclick="switchTab('overview')">
                <i class="fas fa-chart-line"></i> Overview
            </div>
            <div class="tab" onclick="switchTab('database')">
                <i class="fas fa-database"></i> Database Server
            </div>
            <div class="tab" onclick="switchTab('app1')">
                <i class="fas fa-server"></i> App Server 1
            </div>
            <div class="tab" onclick="switchTab('app2')">
                <i class="fas fa-server"></i> App Server 2
            </div>
        </div>

        <div id="overview" class="server-content active">
            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-users"></i> User Activity</h3>
                    <div class="metric-row">
                        <span class="metric-label">Active Users</span>
                        <span class="metric-value" id="active-users">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Total Sessions</span>
                        <span class="metric-value" id="total-sessions">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Requests/min</span>
                        <span class="metric-value" id="requests-per-min">--</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-tachometer-alt"></i> System Overview</h3>
                    <div class="metric-row">
                        <span class="metric-label">Average CPU Usage</span>
                        <span class="metric-value" id="avg-cpu">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Average RAM Usage</span>
                        <span class="metric-value" id="avg-ram">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Total Uptime</span>
                        <span class="metric-value" id="total-uptime">--</span>
                    </div>
                </div>
<!-- 
                <div class="card">
                    <h3><i class="fas fa-chart-area"></i> Performance Trends</h3>
                    <div class="chart-container">
                        <canvas id="overviewChart"></canvas>
                    </div>
                </div> -->

                <!-- <div class="card">
                    <h3><i class="fas fa-exclamation-triangle"></i> Alerts & Issues</h3>
                    <div id="alerts-container">
                        <div class="metric-row">
                            <span class="metric-label">No active alerts</span>
                            <span class="metric-value" style="color: #10b981;">✓</span>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>

        <div id="database" class="server-content">
            <div class="grid">
                <div class="card">
                    <h3><i class="fas fa-microchip"></i> System Resources</h3>
                    <div class="metric-row">
                        <span class="metric-label">CPU Usage</span>
                        <span class="metric-value" id="db-cpu">--</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="db-cpu-bar" style="width: 0%"></div>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">RAM Usage</span>
                        <span class="metric-value" id="db-ram">--</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="db-ram-bar" style="width: 0%"></div>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Disk Usage</span>
                        <span class="metric-value" id="db-disk">--</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="db-disk-bar" style="width: 0%"></div>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-database"></i> PostgreSQL Metrics</h3>
                    <div class="metric-row">
                        <span class="metric-label">Active Connections</span>
                        <span class="metric-value" id="db-connections">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Queries/sec</span>
                        <span class="metric-value" id="db-queries">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Slow Queries</span>
                        <span class="metric-value" id="db-slow-queries">--</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Database Size</span>
                        <span class="metric-value" id="db-size">--</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="last-updated">
            Last updated: <span id="last-updated">--</span>
        </div>
    </div>

    <button class="refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
