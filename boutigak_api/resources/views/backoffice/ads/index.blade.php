@extends('backoffice.layouts.layout')

@section('title', 'Advertisements Management')

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            
            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Advertisements Management</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Advertisements</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title mb-0">
                                <i class="ri-advertisement-line me-2"></i>
                                Advertisements
                            </h4>
                            <div class="card-tools">
                                <button type="button" class="btn btn-info btn-sm me-2" onclick="testAjaxEndpoint()">
                                    <i class="ri-bug-line me-1"></i>
                                    Test Ajax
                                </button>
                                <a href="{{ route('ads.create') }}" class="btn btn-primary">
                                    <i class="ri-add-line me-1"></i>
                                    Add New Advertisement
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="ads-table" class="table table-striped table-bordered dt-responsive nowrap w-100">
                                    <thead class="table-light">
                                        <tr>
                                            <th>ID</th>
                                            <th>Image</th>
                                            <th>Title</th>
                                            <th>Target</th>
                                            <th>Status</th>
                                            <th>Duration</th>
                                            <th>Created By</th>
                                            <th>Created At</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Ad Modal -->
    <div class="modal fade" id="viewAdModal" tabindex="-1" aria-labelledby="viewAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewAdModalLabel">
                        <i class="ri-eye-line me-2"></i>
                        Advertisement Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="adDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
$(document).ready(function() {
    // Initialize DataTable with enhanced error handling
    var table = $('#ads-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('ads.index') }}",
            type: "GET",
            error: function(xhr, error, code) {
                console.error('DataTables Ajax Error:', {
                    xhr: xhr,
                    error: error,
                    code: code,
                    responseText: xhr.responseText,
                    status: xhr.status,
                    statusText: xhr.statusText
                });

                alert('❌ DataTables Error!\n\nStatus: ' + xhr.status + '\nError: ' + error + '\nCheck console for details.');
            }
        },
        columns: [
            {data: 'id', name: 'id'},
            {data: 'image', name: 'image', orderable: false, searchable: false},
            {data: 'title', name: 'title'},
            {data: 'target', name: 'target', orderable: false},
            {data: 'status', name: 'status', orderable: false},
            {data: 'duration', name: 'duration', orderable: false},
            {data: 'created_by', name: 'created_by', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'actions', name: 'actions', orderable: false, searchable: false}
        ],
        order: [[0, 'desc']],
        responsive: true,
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div>'
        }
    });

    console.log('DataTable initialized with URL:', "{{ route('ads.index') }}");

    // Delete ad functionality
    $(document).on('click', '.delete-ad', function() {
        var adId = $(this).data('id');
        
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/ads/' + adId,
                    type: 'DELETE',
                    data: {
                        "_token": "{{ csrf_token() }}",
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire(
                                'Deleted!',
                                response.message,
                                'success'
                            );
                            table.ajax.reload();
                        }
                    },
                    error: function(xhr) {
                        Swal.fire(
                            'Error!',
                            'Something went wrong.',
                            'error'
                        );
                    }
                });
            }
        });
    });
});

// View ad function
function viewAd(adId) {
    $.ajax({
        url: '/ads/' + adId + '/show',
        type: 'GET',
        success: function(ad) {
            var content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Advertisement Image:</label>
                            <div class="mt-2">
                                ${ad.image_url ? 
                                    '<img src="' + ad.image_url + '" alt="Ad Image" class="img-fluid rounded" style="max-height: 200px;">' : 
                                    '<span class="text-muted">No Image</span>'
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Title:</label>
                            <p class="mb-0">${ad.title}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status:</label>
                            <p class="mb-0">
                                <span class="badge ${ad.is_active ? 'bg-success' : 'bg-danger'}">
                                    ${ad.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Target Type:</label>
                            <p class="mb-0">${ad.target_type.charAt(0).toUpperCase() + ad.target_type.slice(1)}</p>
                        </div>
                        ${ad.target_type === 'item' && ad.item ?
                            '<div class="mb-3"><label class="form-label fw-bold">Target Item:</label><p class="mb-0">' + ad.item.title + '</p></div>' : ''
                        }
                        ${ad.target_type === 'store' && ad.store ?
                            '<div class="mb-3"><label class="form-label fw-bold">Target Store:</label><p class="mb-0">' + ad.store.name + '</p></div>' : ''
                        }
                        ${ad.target_type === 'item-store' ?
                            '<div class="mb-3"><label class="form-label fw-bold">Target Store:</label><p class="mb-0">' + (ad.target_store ? ad.target_store.name : 'Unknown Store') + '</p></div>' +
                            (ad.item ? '<div class="mb-3"><label class="form-label fw-bold">Specific Item:</label><p class="mb-0">' + ad.item.title + '</p></div>' :
                                      '<div class="mb-3"><label class="form-label fw-bold">Target:</label><p class="mb-0">All items from store</p></div>') : ''
                        }
                        ${ad.target_type === 'url' ?
                            '<div class="mb-3"><label class="form-label fw-bold">Target URL:</label><p class="mb-0"><a href="' + ad.target_url + '" target="_blank">' + ad.target_url + '</a></p></div>' : ''
                        }
                        ${ad.start_date ? 
                            '<div class="mb-3"><label class="form-label fw-bold">Start Date:</label><p class="mb-0">' + new Date(ad.start_date).toLocaleDateString() + '</p></div>' : ''
                        }
                        ${ad.end_date ? 
                            '<div class="mb-3"><label class="form-label fw-bold">End Date:</label><p class="mb-0">' + new Date(ad.end_date).toLocaleDateString() + '</p></div>' : ''
                        }
                        ${ad.created_by_store ? 
                            '<div class="mb-3"><label class="form-label fw-bold">Created By Store:</label><p class="mb-0">' + ad.created_by_store.name + '</p></div>' : 
                            '<div class="mb-3"><label class="form-label fw-bold">Created By:</label><p class="mb-0">Admin</p></div>'
                        }
                    </div>
                </div>
            `;
            
            $('#adDetailsContent').html(content);
            $('#viewAdModal').modal('show');
        },
        error: function(xhr) {
            Swal.fire(
                'Error!',
                'Failed to load advertisement details.',
                'error'
            );
        }
    });
}

// Test Ajax endpoint function
function testAjaxEndpoint() {
    console.log('Testing Ajax endpoint...');

    $.ajax({
        url: '/ads/test-ajax',
        type: 'GET',
        success: function(response) {
            if (response.success) {
                alert('✅ Ajax endpoint test successful!\n\n' + response.message + '\nAds count: ' + response.data.length);
                console.log('Ajax test successful:', response);
            } else {
                alert('❌ Ajax endpoint test failed!\n\n' + response.message);
                console.error('Ajax test failed:', response);
            }
        },
        error: function(xhr) {
            var errorMsg = 'Ajax endpoint failed';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            alert('❌ Ajax endpoint error!\n\n' + errorMsg);
            console.error('Ajax endpoint error:', xhr);
        }
    });
}
</script>
@endpush
