<?php

namespace App\Http\Controllers;


use App\services\BadgeService; 
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BadgeController extends Controller
{
    protected $badgeService;
    
    public function __construct(BadgeService $badgeService)
    {
        $this->badgeService = $badgeService;
    }
    
    /**
     * Get all badge counts for the authenticated user
     */
    public function getCounts()
    {
        $user = Auth::user();
        $counts = $this->badgeService->getCounts($user);
        
        return response()->json([
            'counts' => $counts,
            'total' => $this->badgeService->getTotalCount($user)
        ]);
    }
    
    /**
     * Reset badge count for a specific module
     */
    public function reset(Request $request)
    {
        $request->validate([
            'module' => 'required|string'
        ]);
        
        $user = Auth::user();
        $this->badgeService->reset($user, $request->module);
        
        return response()->json([
            'message' => 'Badge reset successfully',
            'counts' => $this->badgeService->getCounts($user),
            'total' => $this->badgeService->getTotalCount($user)
        ]);
    }
}