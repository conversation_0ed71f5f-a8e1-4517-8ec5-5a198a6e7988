  import 'package:flutter/material.dart';
  
  
  
  
  void _showAuthDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Authentification requise'),
          content: Text('Veuillez vous connecter pour accéder à cette fonctionnalité.'),
          actions: <Widget>[
            TextButton(
              child: Text('Se connecter'),
              onPressed: () {
                Navigator.of(context).pop(); // Ferme la boîte de dialogue
                Navigator.of(context).pushNamed('/login'); // Redirige vers la page de login
              },
            ),
            TextButton(
              child: Text('Annuler'),
              onPressed: () {
                Navigator.of(context).pop(); // Ferme la boîte de dialogue
              },
            ),
          ],
        );
      },
    );
  }