import 'dart:convert';
import 'dart:developer';
import 'dart:ui';


import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:shared_preferences/shared_preferences.dart';


class SessionService {

  static  String accessTokenKey='accessToken';
  static  String refreshTokenKey='refreshToken';

  static Future saveToken(String accessToken) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(accessTokenKey, accessToken);

  }

  
  static Future saveUser(Map<String, dynamic> user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String userString = json.encode(user);
    await prefs.setString("loggedUser", userString);

  }

  static Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.reload();
    String? token = prefs.getString(accessTokenKey);
    return token ?? "";
  }

  // static Future getLoh(String body) async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   await prefs.setString("loggedUser", body);
  // }

  static Future<User?> getLoggedUser() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.reload();
    String? userJson = prefs.getString("loggedUser");
    if (userJson != null) {
      Map<String,dynamic> user = json.decode(userJson);
      return User.fromJson(user);
    } else {
      return null;
    }
  }

  static Future<void> logout() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove("loggedUser");
    await prefs.remove(accessTokenKey);
  }

  static Future<void> clearPrefs() async {
    log("@@@@clear Prefs");
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove("loggedUser");
    await prefs.remove(accessTokenKey);
  }

  static Future updateLang(String languageCode) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('prefLang', languageCode);
  }


}
