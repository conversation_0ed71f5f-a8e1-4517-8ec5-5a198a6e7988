<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PromoCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'discount',
        'expires_at',
        'is_active',
        'limit_usages',
        'times_used'
    ];

    protected $casts = [
        'expires_at' => 'date',
    ];

    public function incrementUsage()
    {
        $this->increment('times_used');
    }
}
