<?php

if (!function_exists('sftp_image')) {
    /**
     * Generate a URL for an image using the SFTP server.
     * Only use this for images, not CSS/JS assets.
     *
     * @param string $path
     * @return string
     */
    function sftp_image($path)
    {
        $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');
        $path = ltrim($path, '/');
        return $sftpBaseUrl . '/' . $path;
    }
}

if (!function_exists('sftp_storage_url')) {
    /**
     * Generate a URL for a storage file using the SFTP server.
     * Only adds 'storage/' prefix if the path doesn't already contain it.
     *
     * @param string $path
     * @return string
     */
    function sftp_storage_url($path)
    {
        $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');
        $storagePath = config('assets.sftp.storage_path', 'storage');
        $path = ltrim($path, '/');

        // Only add storage prefix if path doesn't already start with it
        if (!str_starts_with($path, $storagePath . '/')) {
            $path = $storagePath . '/' . $path;
        }

        return $sftpBaseUrl . '/' . $path;
    }
}

if (!function_exists('sftp_url')) {
    /**
     * Generate a direct SFTP URL without adding storage prefix.
     * Use this when the path already contains the full path.
     *
     * @param string $path
     * @return string
     */
    function sftp_url($path)
    {
        // $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');
        // $path = ltrim($path, '/');
        return $path;
    }
}

if (!function_exists('image_url')) {
    /**
     * Generate a URL for an image, automatically using SFTP if configured.
     *
     * @param string $path
     * @return string
     */
    function image_url($path)
    {
        if (config('assets.use_sftp_for_images', true)) {
            return sftp_storage_url($path);
        }


        return asset( ltrim($path, '/'));
    }
}
