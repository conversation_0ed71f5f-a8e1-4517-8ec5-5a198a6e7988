@extends('backoffice.layouts.layout')

@section('title', 'Show Category | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Category Details</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Title (EN)</label>
                                <p class="form-control">{{ $category->title_en }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Title (AR)</label>
                                <p class="form-control">{{ $category->title_ar }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Title (FR)</label>
                                <p class="form-control">{{ $category->title_fr }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Parent Category</label>
                                <p class="form-control">{{ $category->parent ? $category->parent->title_en : 'None' }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Details</label>
                                <ul class="list-group">
                                    @foreach($category->details as $detail)
                                        <li class="list-group-item">
                                            <strong>EN:</strong> {{ $detail->label_en }} |
                                            <strong>FR:</strong> {{ $detail->label_fr }} |
                                            <strong>AR:</strong> {{ $detail->label_ar }}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                            <a href="{{ route('categories.index') }}" class="btn btn-secondary">Back to List</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
