package mr.app.boutigak

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import androidx.core.content.FileProvider
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.snapchat_share"
    private val TAG = "SnapchatShareDebug 2"
    private val SNAPCHAT_PACKAGE = "com.snapchat.android"
    private val CLIENT_ID = "8aea929b-bbde-4d84-92d7-77736ee78a61"
    private val CREATIVE_KIT_LITE_REQUEST_CODE = 9834

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "shareToCamera") {
                Log.d(TAG, "Received 'shareToCamera' method call")

                val imageData = call.argument<ByteArray>("imageData")
                val caption = call.argument<String>("caption")
                val attachmentUrl = call.argument<String>("attachmentUrl")

                if (imageData != null) {
                    try {
                        // Save image to file
                        val tempFile = saveImageToFile(imageData, "shared_sticker.png")
                        Log.d(TAG, "Image data saved to: ${tempFile.absolutePath}")

                        // Create sticker URI
                        val stickerUri = FileProvider.getUriForFile(
                            this,
                            "${applicationContext.packageName}.provider",
                            tempFile
                        )

                        Log.d(TAG, "Generated sticker URI: $stickerUri")

                        // Share to Snapchat camera
                        shareStickerToSnapchatCamera(stickerUri, caption, attachmentUrl)
                        result.success("Shared successfully!")
                    } catch (e: Exception) {
                        Log.e(TAG, "Exception occurred while sharing to Snapchat: ${e.message}", e)
                        result.error("ERROR", "Failed to share to Snapchat", null)
                    }
                } else {
                    Log.e(TAG, "Invalid arguments: Image data is missing")
                    result.error("INVALID_ARGS", "Image data is missing", null)
                }
            } else {
                Log.w(TAG, "Method not implemented: ${call.method}")
                result.notImplemented()
            }
        }
    }

    private fun saveImageToFile(imageData: ByteArray, filename: String): File {
        val tempFile = File(applicationContext.cacheDir, filename)
        val fos = FileOutputStream(tempFile)
        fos.write(imageData)
        fos.close()
        Log.d(TAG, "Image data saved to: ${tempFile.absolutePath}")
        return tempFile
    }

    private fun shareStickerToSnapchatCamera(stickerUri: Uri, caption: String?, attachmentUrl: String?) {

        Log.d(TAG, "Added attachment URL to intent: $attachmentUrl")

        val intent = getBaseIntent(this, CLIENT_ID, "snapchat://creativekit/camera", "*/*")
        intent.putExtra("CLIENT_APP_NAME", "My Flutter App")
        
        if (!caption.isNullOrEmpty()) {
            // intent.putExtra("captionText", caption)
        }

        // Add the attachment URL if provided
        if (!attachmentUrl.isNullOrEmpty()) {
            intent.putExtra("attachmentUrl", attachmentUrl)
            Log.d(TAG, "Added attachment URL to intent: $attachmentUrl")
        }

        // Add sticker data to the intent
        addSticker(intent, stickerUri)

        // Grant permission to Snapchat to access the file
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        grantUriPermission(SNAPCHAT_PACKAGE, stickerUri, Intent.FLAG_GRANT_READ_URI_PERMISSION)

        // Start Snapchat intent
        if (intent.resolveActivity(packageManager) != null) {
            startActivity(intent)
            Log.d(TAG, "Snapchat app launched successfully")
        } else {
            Log.e(TAG, "Snapchat app not installed or cannot handle the intent")
        }
    }

    private fun getBaseIntent(context: Context, clientID: String, intentUri: String, intentType: String): Intent {
        Log.d(TAG, "Creating base intent with URI: $intentUri and Type: $intentType")
        val intent = Intent(Intent.ACTION_SEND)
        intent.setPackage(SNAPCHAT_PACKAGE)
        intent.putExtra("CLIENT_ID", clientID)
        intent.setDataAndType(Uri.parse(intentUri), intentType)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        intent.putExtra(
            "RESULT_INTENT", PendingIntent.getActivity(
                context,
                CREATIVE_KIT_LITE_REQUEST_CODE,
                Intent(),
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_IMMUTABLE else PendingIntent.FLAG_ONE_SHOT
            )
        )
        return intent
    }

    private fun addSticker(intent: Intent, stickerUri: Uri, posX: Float = 0.5f, posY: Float = 0.5f, rotation: Float = 0f, widthDp: Int = 200, heightDp: Int = 200) {
        try {
            val stickerJson = JSONObject()
            stickerJson.put("uri", stickerUri.toString())
            stickerJson.put("posX", posX)
            stickerJson.put("posY", posY)
            stickerJson.put("rotation", rotation)
            stickerJson.put("widthDp", widthDp)
            stickerJson.put("heightDp", heightDp)
            intent.putExtra("sticker", stickerJson.toString())
            Log.d(TAG, "Sticker JSON added to intent: ${stickerJson.toString()}")
        } catch (ex: JSONException) {
            Log.w(TAG, "Couldn't create sticker JSON", ex)
        }
    }
}
