<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\services\OtpService;
use Illuminate\Support\Facades\Log;
use App\Models\User;

class OtpController extends Controller
{
    private $otpService;

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
    }

    public function generateOtp(Request $request)
    {
        Log::info('Generating OTP for ' . $request->phoneNumber);
        $request->validate([
            'phoneNumber' => 'required|string',
        ]);

        $otp = $this->otpService->generateOtp($request->phoneNumber);

        Log::info('OTP generated successfully for ' . $request->phoneNumber);

        return response()->json(['message' => 'OTP sent successfully', 'otp' => $otp]);
    }

    public function verifyOtp(Request $request)
    {
        $request->validate([
            'phoneNumber' => 'required|string',
            'otp' => 'required',
        ]);

        $isValid = $this->otpService->verifyOtp($request->phoneNumber, $request->otp);

        if ($isValid) {

            $user = User::where('phone', $request->phoneNumber)->first();
            
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            $user->is_verified = true;
            $user->save();

            $token = $user->createToken('Boutigak')->plainTextToken;

            return response()->json([
                'message' => 'OTP verified successfully',
                'user' => $user,
                'access_token' => $token
            ]);
        }

        return response()->json(['error' => 'Invalid or expired OTP'], 400);
    }
}
