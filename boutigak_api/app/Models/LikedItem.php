<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LikedItem extends Model
{
    protected $table = 'liked_item';

    protected $fillable = [
        'user_id',
        'item_id',
        
    ];

    protected $dates = [
        
        'created_at',
        'updated_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
