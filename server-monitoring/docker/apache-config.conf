<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/html
    
    # Enable directory browsing for development
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Set index files
        DirectoryIndex index.php index.html index.htm
        
        # Enable PHP processing
        <FilesMatch \.php$>
            SetHandler application/x-httpd-php
        </FilesMatch>
    </Directory>
    
    # API directory configuration
    <Directory /var/www/html/api>
        Options -Indexes
        AllowOverride All
        Require all granted
        
        # Ensure PHP files are processed
        <FilesMatch \.php$>
            SetHandler application/x-httpd-php
        </FilesMatch>
    </Directory>
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # CORS headers for API endpoints
    <LocationMatch "^/api/">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
    </LocationMatch>
    
    # Logging
    ErrorLog /var/log/apache2/monitoring_error.log
    CustomLog /var/log/apache2/monitoring_access.log combined
    
    # PHP error logging
    php_admin_value error_log /var/log/monitoring/php_errors.log
    php_admin_flag log_errors on
    php_admin_value display_errors off
</VirtualHost>

# Development-specific settings
<IfDefine DEVELOPMENT>
    # Show PHP errors in development
    php_admin_value display_errors on
    php_admin_value display_startup_errors on
    php_admin_value error_reporting "E_ALL"
</IfDefine>
