import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/dashline.dart';
import 'package:boutigak/views/payment/payment.dart';
import 'package:boutigak/views/profil/address_management_page.dart';
import 'package:boutigak/views/home/<USER>';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/splashscreen.dart';
import 'package:flutter/material.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

import 'package:shimmer/shimmer.dart';

class CartPage extends StatelessWidget {
  final OrderController orderController;
  final Store store;
  final Color dominentColor;
  final Color surfaceColor;

  CartPage({
    Key? key,
    required this.orderController,
    required this.store,
    required this.dominentColor,
    required this.surfaceColor,
  }) : super(key: key) {
    // Calculate total when page is created
    orderController.calculateTotal();
  }

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Your Cart', style: TextStyle(color: surfaceColor)),
        backgroundColor: dominentColor,
      ),
      body: Obx(() {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      width: 40.0,
                      height: 40.0,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        image: DecorationImage(
                          fit: BoxFit.cover,
                          image: store.images.isNotEmpty
                              ? NetworkImage('${hostURLs[PossiblesHosts.STORAGE]}${store.images.first}')
                              : AssetImage('assets/default_store_image.png') as ImageProvider,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.3),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: Offset(0.5, 0.5),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("Your Order",
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        Text(
                            "${orderController.items.length} item(s) from ${store.name}",
                            style: TextStyle(fontSize: 16, color: Colors.grey)),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 16),

              Expanded(
                child: ListView.builder(
                  itemCount: orderController.items.length,
                  itemBuilder: (context, index) {
                    OrderItemController orderItemController = orderController.items[index];
                    ItemController itemController = Get.put(ItemController(), tag: orderItemController.itemId.value.toString());
                    itemController.fetchItemById(orderItemController.itemId.value);


    
                    return Obx(() {
                      final item = itemController.selectedItem.value;
                     if (item == null) {
  return Shimmer.fromColors(
    baseColor: Colors.grey.shade300,
    highlightColor: Colors.grey.shade100,
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 10.0),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 75,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 30,
                      height: 16,
                      color: Colors.white,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        height: 16,
                        color: Colors.white,
                      ),
                    ),
                    Container(
                      width: 50,
                      height: 14,
                      color: Colors.white,
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _shimmerCircle(),
                      SizedBox(width: 10),
                      _shimmerCircle(),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    ),
  );
}


                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10.0),
                        child: Row(
                          children: [
                            item.images.isNotEmpty
                                ? Container(
                                    width: 56,
                                    height: 75,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: NetworkImage('${hostURLs[PossiblesHosts.STORAGE]}${item.images.first}'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  )
                                : Icon(Icons.shopping_cart, size: 80),
                            SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text('${orderItemController.quantity.value}x',
                                          style: TextStyle(fontWeight: FontWeight.bold)),
                                      SizedBox(width: 8),
                                      Expanded(child: Text(item.title, overflow: TextOverflow.ellipsis)),
                                      Text('${(item.price * orderItemController.quantity.value).toStringAsFixed(2)} UM'),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Spacer(),
                                      _buildIconButton(icon: Icons.add, onPressed: () {
                                        orderItemController.incrementQuantity();
                                        orderController.calculateTotal();
                                      }),
                                      SizedBox(width: 10),
                                      _buildIconButton(icon: Icons.remove, onPressed: () {
                                        if (orderItemController.quantity.value > 1) {
                                          orderItemController.decrementQuantity();
                                          orderController.calculateTotal();
                                        } else {
                                          _removeItemDialog(context, orderItemController.itemId.value);
                                        }
                                      }),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    });
                  },
                ),
              ),
             Column(
  children: [
    dashedLine(),
    SizedBox(height: 10),
    Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text('Total:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        Obx(() => Text(
              '${orderController.total.value.toStringAsFixed(2)} UM',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            )),
      ],
    ),
    SizedBox(height: 20),
    CustomButton(
      text: "Checkout",
      onPressed: () {
        Get.to(() => CheckoutPage(
              orderController: orderController,
              store: store,
              dominentColor: dominentColor,
              surfaceColor: surfaceColor,
            ));
      },
      bgColor: dominentColor,
    ),
    SizedBox(height: 20),
  ],
)

            ],
          ),
        );
      }),
    );
  }
Widget _shimmerCircle() {
  return Container(
    width: 26,
    height: 26,
    decoration: BoxDecoration(
      color: Colors.white,
      shape: BoxShape.circle,
    ),
  );
}

  Widget _buildIconButton({required IconData icon, required VoidCallback onPressed}) {
    return Container(
      width: 26,
      height: 26,
      decoration: BoxDecoration(color: Colors.grey[300], shape: BoxShape.circle),
      child: IconButton(
        icon: Icon(icon, size: 18),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
        constraints: BoxConstraints(),
      ),
    );
  }

  void _removeItemDialog(BuildContext context, int itemId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Center(child: Image.asset('assets/images/effacer.png', width: 100, height: 100)),
        content: Text("Do you want to remove this item from your cart?"),
        actions: [
          TextButton(
              child: Text("Cancel", style: TextStyle(color: AppColors.onBackground)),
              onPressed: () => Navigator.of(context).pop()),
          TextButton(
              child: Text("Remove", style: TextStyle(color: AppColors.error)),
              onPressed: () {
                orderController.removeItem(itemId);
                Navigator.of(context).pop();
              }),
        ],
      ),
    );
  }
}


class LocationPickerWidget extends StatefulWidget {
  final OrderController orderController;

  LocationPickerWidget({
    Key? key,
    required this.orderController,
  }) : super(key: key);

  @override
  State<LocationPickerWidget> createState() => _LocationPickerWidgetState();
}

class _LocationPickerWidgetState extends State<LocationPickerWidget> {

  

  @override
  void initState() {
    super.initState();
    widget.orderController.fetchUserLocations();
  }
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() => DropdownButtonFormField<Location>(
          value: widget.orderController.selectedLocation.value,
          hint: Text('Select a delivery address'),
          items: widget.orderController.userLocations.map((Location location) {
            return DropdownMenuItem<Location>(
              value: location,
              child: Text('${location.name} - ${location.address}'),
            );
          }).toList(),
          onChanged: (Location? location) {
            if (location != null) {
              widget.orderController.selectedLocation.value = location;
              widget.orderController.isLocationSelected.value = true;
            }
          },
          decoration: InputDecoration(
            labelText: 'Delivery Address',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
          ),
        )),
        
        SizedBox(height: 16),
        
        TextButton(
          onPressed: () {
            Get.to(() => AddressManagementPage());
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.add_location_alt_outlined),
              SizedBox(width: 8),
              Text('Manage Addresses'),
            ],
          ),
        ),
      ],
    );
  }
}
class CheckoutPage extends StatelessWidget {
  final OrderController orderController;
  final Store store;
  final Color dominentColor;
  final Color surfaceColor;

  CheckoutPage({
    Key? key,
    required this.orderController,
    required this.store,
    required this.dominentColor,
    required this.surfaceColor,
  }) : super(key: key);

  InputDecoration inputDecoration(String labelText) {
    return InputDecoration(
      labelText: labelText,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(15)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Checkout', style: TextStyle(color: surfaceColor)),
        backgroundColor: dominentColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Obx(() {
          return Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Checkbox(
                      value: orderController.isCashOnDelivery.value,
                      onChanged: (value) {
                        orderController.isCashOnDelivery.value = value!;
                      },
                      activeColor: dominentColor,
                    ),
                    Text('Cash on Delivery', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
                    Spacer(),
                    Icon(Icons.payments_outlined, color: dominentColor),
                  ],
                ),
              ),
              SizedBox(height: 10),
              DropdownButtonFormField<int>(
                value: orderController.deliveryChargeID.value != 0 ? orderController.deliveryChargeID.value : null,
                onChanged: (newValue) {
                  orderController.deliveryChargeID.value = newValue!;
                  orderController.calculateTotal();
                },
                items: orderController.deliveryCharges.map((DeliveryCharge deliveryCharge) {
                  return DropdownMenuItem<int>(
                    value: deliveryCharge.id,
                    child: Text('${deliveryCharge?.type} - ${deliveryCharge.amount} Mru'),
                  );
                }).toList(),
                decoration: inputDecoration('Frais de livraison'),
              ),
              SizedBox(height: 10),
              LocationPickerWidget(orderController: orderController),
              SizedBox(height: 20),
              Text('Total: ${orderController.total.value.toStringAsFixed(2)} UM',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              SizedBox(height: 10),
              Obx(() => CustomButton(
                text: orderController.isLoading.value ? "Processing..." : "Order",
                onPressed: (orderController.isLocationSelected.value && orderController.deliveryChargeID.value != 0 && !orderController.isLoading.value)
                    ? () async {
                        orderController.isLoading.value = true;
                        try {
                          bool success = await orderController.createOrder(storeId: store.id!);
                          if (success) {
                            
                            orderController.clearOrder();

                            Get.snackbar("Success", "Order placed successfully",
                                backgroundColor: Colors.green, colorText: Colors.white, snackPosition: SnackPosition.BOTTOM);

                            Get.offAll(() => ZoomDrawerWrapper());
                            Future.delayed(Duration(milliseconds: 100), () {
                              Get.to(() => MyOrdersPage());
                            });
                          }
                        } finally {
                          orderController.isLoading.value = false;
                        }
                      }
                    : () {},
                bgColor: orderController.isLocationSelected.value && orderController.deliveryChargeID.value != 0 && !orderController.isLoading.value
                    ? dominentColor
                    : Colors.grey,
              ),
          )],
          );
        }),
      ),
    );
  }
}
