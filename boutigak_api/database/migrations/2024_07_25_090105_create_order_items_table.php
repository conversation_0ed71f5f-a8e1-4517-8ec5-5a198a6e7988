<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    

      /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::create('order_item', function (Blueprint $table) {
        //     $table->id();
        //     $table->unsignedBigInteger('order_id');
        //     $table->unsignedBigInteger('item_id');
        //     $table->integer('quantity');
        //     $table->double('price'); // Store the price at the time of order

        //     $table->foreign('order_id')->references('id')->on('order')->onDelete('cascade');
        //     $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
            
        //     $table->timestamps();
        // });

        // Schema::table('order', function (Blueprint $table) {
        //     $table->dropForeign(['item_id']);
        //     $table->dropColumn('item_id');
        // });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Schema::dropIfExists('order_item');

        // // Optionally, add item_id back to the orders table
        // Schema::table('orders', function (Blueprint $table) {
        //     $table->unsignedBigInteger('item_id')->nullable();

        //     $table->foreign('item_id')->references('id')->on('items')->onDelete('cascade');
        // });
    }
};
