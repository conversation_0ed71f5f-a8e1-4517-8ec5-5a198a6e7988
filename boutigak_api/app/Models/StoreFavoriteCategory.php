<?php

namespace App\Models;

use App\Models\Store;
use App\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StoreFavoriteCategory extends Model
{
    protected $table = 'store_favirote_categories';

    protected $fillable = [
        'store_id',
        'category_id',
    ];

    /**
     * Get the store that owns the favorite category.
     */
    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the category that belongs to the favorite.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
