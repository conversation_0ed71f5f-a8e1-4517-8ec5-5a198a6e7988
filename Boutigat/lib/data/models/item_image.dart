class ItemImage {
  final int id;
  final String url;
  final int order;

  ItemImage({
    required this.id, 
    required this.url,
    this.order = 0,
  });

  factory ItemImage.fromJson(Map<String, dynamic> json) {
    return ItemImage(
      id: json['id'],
      url: json['url'],
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'order': order,
    };
  }
}
