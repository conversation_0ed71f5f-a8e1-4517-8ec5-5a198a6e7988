class Ad {
  final int id;
  final int? storeId;
  final String title;
  final String imageUrl;
  final String targetType; // 'item', 'store', or 'url'
  final int? targetId;     // ID of item or store
  final String? targetUrl; // External URL if targetType is 'url'
  final bool isActive;
  final DateTime? startDate;
  final DateTime? endDate;

  Ad({
    required this.id,
    this.storeId,
    required this.title,
    required this.imageUrl,
    required this.targetType,
    this.targetId,
    this.targetUrl,
    required this.isActive,
    this.startDate,
    this.endDate,
  });

  factory Ad.fromJson(Map<String, dynamic> json) {
    return Ad(
      id: json['id'],
      storeId: json['store_id'],
      title: json['title'],
      imageUrl: json['image_url'],
      targetType: json['target_type'],
      targetId: json['target_id'],
      targetUrl: json['target_url'] ?? '',
      isActive: json['is_active'] == 1,
      startDate: json['start_date'] != null ? DateTime.parse(json['start_date']) : null,
      endDate: json['end_date'] != null ? DateTime.parse(json['end_date']) : null,
    );
  }
}
