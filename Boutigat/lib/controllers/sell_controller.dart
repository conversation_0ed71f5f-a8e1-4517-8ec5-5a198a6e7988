import 'dart:developer';

import 'package:get/get.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:dio/dio.dart' as dio;

class PhotoActionsController extends GetxController {
  final Rx<Color> photoButtonColor = Rx<Color>(AppColors.onBackground);
  final Rx<Color> uploadButtonColor = Rx<Color>(AppColors.onBackground);
  final RxList<String> photos = <String>[].obs;
  final RxBool isActionInProgress = false.obs;
  final ImagePicker picker = ImagePicker();
  final ItemController item;
  final RxBool lastActionWasUpload = false.obs;
  RxInt currentPage = 0.obs;
  PageController pageController = PageController();

  PhotoActionsController(this.item);

  void takePhoto() async {
    if (isActionInProgress.value) return;
    isActionInProgress.value = true;
    photoButtonColor.value = AppColors.primary;
    lastActionWasUpload.value = false;

    try {
      final pickedFile = await picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        addPhoto(pickedFile.path);
   
      } else {
    
      }
    } catch (e) {
      print('Error taking photo: $e');
  
    } finally {
      isActionInProgress.value = false;
      photoButtonColor.value = AppColors.onBackground;
    }
  }



 

void uploadPhoto() async {
  if (isActionInProgress.value) return;
  isActionInProgress.value = true;
  uploadButtonColor.value = AppColors.primary;
  lastActionWasUpload.value = true;

  try {
    final List<XFile>? pickedFiles = await picker.pickMultiImage();
    
    if (pickedFiles != null && pickedFiles.isNotEmpty) {
      for (final file in pickedFiles) {
        if (photos.length >= 10) {
          Get.snackbar("Limit reached", "You can only add up to 10 photos.");
          break;
        }
        addPhoto(file.path);
        print("Photo uploaded: ${file.path}");
      }
    } else {
      print('No images selected.');
    }
  } catch (e) {
    print('Error uploading photos: $e');
  } finally {
    isActionInProgress.value = false;
    uploadButtonColor.value = AppColors.onBackground;
  }
}

 void addPhoto(String path) {
    if (!File(path).existsSync()) {
 
    } else {
      photos.add(path);
      item.addPhoto(path);
 
    }
  }



  void removePhoto(String path) {
    photos.remove(path);
    
    log('path: $path');
    if (path.startsWith('http://') || path.startsWith('https://')) {
      // Extract the image ID from the URL or find the corresponding image
      final itemController = item;
      
      log('existingImages: ${itemController.existingImages.map((img) => '${img.id}: ${img.url}').join(', ')}');
      
      final imageId = itemController.existingImages
          .where((img) => path.contains(img.url))
          .map((img) => img.id)
          .firstOrNull;

      log('imageId: $imageId');
      
      if (imageId != null) {
        // Mark this image for deletion
        itemController.markImageForDeletion(imageId);
        print("Marked image ID $imageId for deletion");
      }
    }
    
    item.removePhoto(path);
  }



 
  @override
  void onInit() {
    super.onInit();
    pageController.addListener(() {
      int page = pageController.page!.round();
      if (currentPage.value != page) {
        currentPage.value = page;
      }
    });
  }



  void setCurrentPage(int index) {
    currentPage.value = index;
    pageController.jumpToPage(index);
  }





  void reorderPhotos(int oldIndex, int newIndex) {
          log('photo reordered ');

    if (oldIndex < photos.length && newIndex <= photos.length) {

      final String item = photos.removeAt(oldIndex);
      photos.insert(newIndex, item);
      update(); // Update the UI
    }
  }

  // When uploading to the server, ensure we send the images in the correct order
  Future<List<dio.MultipartFile>> prepareImageFiles() async {
    List<dio.MultipartFile> imageFiles = [];
    
    // Process images in the order they appear in the photos list
    for (int i = 0; i < photos.length; i++) {
      final String photoPath = photos[i];
      if (photoPath.startsWith('http')) {
        // Skip network images as they're already on the server
        continue;
      }
      
      File file = File(photoPath);
      if (await file.exists()) {
        String fileName = photoPath.split('/').last;
        imageFiles.add(await dio.MultipartFile.fromFile(
          photoPath,
          filename: fileName,
        ));
      }
    }
    
    return imageFiles;
  }

 void clearPhotoActionData() {
 

  photos.clear();


  update(); 
}



}
