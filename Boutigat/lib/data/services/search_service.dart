import 'dart:convert';
import 'dart:developer';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/search.dart';
import 'package:boutigak/data/services/webservices.dart';


class SearchService {

  static Future<List<Item>?> searchItems(String query) async {
    try {


      final AuthController authController = Get.find<AuthController>();
      bool isAuthenticated = authController.token.value != '';



      log('is Authenticated ${isAuthenticated}');
                  // Choose the appropriate endpoint based on authentication status
      final String endpoint = isAuthenticated 
          ? '${AvailableServices.search}?query=$query'
          : '${AvailableServices.publicSearch}?query=$query';
      
                 // Make the request with or without token based on authentication status
      var response = await WebService.get(endpoint, withToken: isAuthenticated);
      print('Response status code search: ${response.statusCode}');
      print('response search ${response.body}');
      log('Response status code search: ${response.statusCode}');
      log('response search ${response.body}');
      
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Item> items = body.map((dynamic item) => Item.fromJson(item)).toList();
        return items;
      } else {
        // Get.snakbar("Error", "Failed to load items");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching items ${e.toString()}");
      return null;
    }
  }

  static Future<List<SearchHistory>?> fetchSearchHistory() async {


    try {


  
      


      var response = await WebService.get(AvailableServices.searchHistory);

  //  print('in fetch search history');
  //       print('-----------------------Response status code: ${response.statusCode}----------');

  //    print('response  history ---------------${response.body}++++++++++++++++++++');


      if (response.statusCode == 200) {

        List<dynamic> body = jsonDecode(response.body);

        List<SearchHistory> history = body.map((dynamic item) => SearchHistory.fromJson(item)).toList();

        return history;



      } else {



        //  Get.snackbar("Error", "Failed to load search history");


        return null;


      }

    } catch (e) {

      // Get.snakbar("Error", "An error occurred while fetching search history ${e.toString()}");
      
      return null;

    }
  }


 
 static Future<List<Map<String, dynamic>>?> fetchPopuarCategories() async {

  try {

    var response = await WebService.get(AvailableServices.popularCategories);

    if (response.statusCode == 200) {

      List<dynamic> body = jsonDecode(response.body);
      // Ensure the body is a list of maps

      return body.map((e) => e as Map<String, dynamic>).toList();

    } else {

      // Get.snakbar("Error", "Failed to load popular categories");

      return null;

    }

  } catch (e) {


    // Get.snakbar("Error", "An error occurred while fetching popular categories: ${e.toString()}");

    return null;


  }


}
  static Future<bool> deleteSearchHistory(int id) async {
    try {
      var response = await WebService.delete('${AvailableServices.searchHistory}/$id');
      


      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting search history: $e');
      return false;
    }
  }
}
