
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/brands_service.dart';
import 'package:boutigak/data/services/status_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/status.dart';



class StatusController extends GetxController {
  final AuthController authController = Get.find<AuthController>();
  Rx<Status?> selectedItem = Rx<Status?>(null);
  RxString id;
  RxString title = ''.obs;
  RxString status = ''.obs;
  RxString image = ''.obs;
  RxList<Status> myItems = <Status>[].obs;
  // Modify later to required
  StatusController({
    String initialId = '',
    String initialTitle = '',
    String initialImage= '',
    String initialStatus = '',
  }) : id = (initialId.isNotEmpty ? initialId : '').obs {
    title.value = initialTitle;
    status.value = initialStatus;
    image.value = initialImage;
  }

 
void fetchMyItems() async {
    List<Status>? fetchedMyItems;

    // Vérifie si l'utilisateur est authentifié
    if (authController.isAuthenticated.value) {
      // Si l'utilisateur est connecté, fetchItems
      fetchedMyItems = await StatusService.fetchMyItems();
    } 
    // Si les éléments sont récupérés correctement, mettre à jour la liste
    if (fetchedMyItems != null) {
      myItems.value = fetchedMyItems;
    } else {
   //   // Get.snakbar("Error", "Failed to load My items");
    }
  }

  
  // Method to select an item and make it reactive
  void selectItem(Status status) {
    selectedItem.value = status;  // Set the current item to be observed
  }

  

  @override
  void onInit() {
    super.onInit();
    fetchMyItems();
  }
}
