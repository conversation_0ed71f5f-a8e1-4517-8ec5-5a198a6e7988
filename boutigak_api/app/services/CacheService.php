<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheService
{
    /**
     * Get data from cache or execute callback and cache result
     *
     * @param string $key
     * @param int $minutes
     * @param callable $callback
     * @return mixed
     */
    public static function remember(string $key, int $minutes, callable $callback)
    {
        return Cache::remember($key, now()->addMinutes($minutes), $callback);
    }

    /**
     * Clear cache by key or pattern
     *
     * @param string $key
     * @return bool
     */
    public static function forget(string $key): bool
    {
        return Cache::forget($key);
    }

    /**
     * Clear cache by tag
     *
     * @param string|array $tags
     * @return bool
     */
    public static function clearTag($tags): bool
    {
        try {
            Cache::tags($tags)->flush();
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear cache tags', ['error' => $e->getMessage()]);
            return false;
        }
    }
}