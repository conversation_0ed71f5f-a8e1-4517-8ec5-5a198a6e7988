<?php

namespace App\services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BPayService
{
    protected $baseUrl;
    protected $clientId;
    private $accessToken;
    private $refreshToken;

    public function __construct()
    {
        $this->baseUrl = config('services.bpay.base_url', 'https://ebankily-tst.appspot.com');
        $this->clientId = config('services.bpay.client_id', 'ebankily');
    }

    /**
     * Authenticate with B-Pay
     */
    public function authenticate(string $username, string $password): array
    {
        try {
            $response = Http::timeout(180) // Timeout set to 3 minutes
                ->asForm()
                ->post($this->baseUrl . '/authentification', [
                    'grant_type' => 'password',
                    'username' => $username,
                    'password' => $password,
                    'client_id' => $this->clientId
                ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->accessToken = $data['access_token'];
                $this->refreshToken = $data['refresh_token'];
                return $data;
            }

            Log::error('B-Pay Authentication Error', [
                'response' => $response->json()
            ]);

            return [
                'error' => true,
                'message' => 'Authentication failed'
            ];

        } catch (\Exception $e) {
            Log::error('B-Pay Authentication Exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Refresh authentication token
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            $response = Http::timeout(180) // Timeout set to 3 minutes
                ->asForm()
                ->post($this->baseUrl . '/authentification', [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                    'client_id' => $this->clientId
                ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('B-Pay Token Refresh Error', [
                'response' => $response->json()
            ]);

            return [
                'error' => true,
                'message' => 'Token refresh failed'
            ];

        } catch (\Exception $e) {
            Log::error('B-Pay Token Refresh Exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process merchant payment
     */
    public function processPayment(
        string $clientPhone,
        string $passcode,
        string $operationId,
        string $amount,
        string $language = 'FR'
    ): array {
        try {

            Log::info('Processing payment with access token: ' . $this->accessToken);
            $response = Http::timeout(4000) 
                ->withToken($this->accessToken)
                ->post($this->baseUrl . '/payment', [
                    'clientPhone' => $clientPhone,
                    'passcode' => $passcode,
                    'operationId' => $operationId,
                    'amount' => $amount,
                    'language' => $language
                ]);


            Log::info('response'.json_encode($response->json()));

            

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('B-Pay Payment Error', [
                'response' => $response->json()
            ]);

            return [
                'error' => true,
                'message' => 'Payment failed'
            ];

        } catch (\Exception $e) {
            Log::error('B-Pay Payment Exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Check transaction status
     */
    public function checkTransaction(string $operationId): array
    {
        try {
            $response = Http::timeout(180) // Timeout set to 3 minutes
                ->withToken($this->accessToken)
                ->post($this->baseUrl . '/checkTransaction', [
                    'operationID' => $operationId
                ]);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('B-Pay Transaction Check Error', [
                'response' => $response->json()
            ]);

            return [
                'error' => true,
                'message' => 'Transaction check failed'
            ];

        } catch (\Exception $e) {
            Log::error('B-Pay Transaction Check Exception', [
                'error' => $e->getMessage()
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }
}
