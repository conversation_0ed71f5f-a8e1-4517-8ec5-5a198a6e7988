@extends('backoffice.layouts.layout')

@section('title', 'Create Category | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Create Category</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('categories.store') }}" method="POST">
                                @csrf
                                <div class="mb-3">
                                    <label for="title_en" class="form-label">Title (EN)</label>
                                    <input type="text" class="form-control" id="title_en" name="title_en" required>
                                </div>
                                <div class="mb-3">
                                    <label for="title_ar" class="form-label">Title (AR)</label>
                                    <input type="text" class="form-control" id="title_ar" name="title_ar" required>
                                </div>
                                <div class="mb-3">
                                    <label for="title_fr" class="form-label">Title (FR)</label>
                                    <input type="text" class="form-control" id="title_fr" name="title_fr" required>
                                </div>
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">Parent Category</label>
                                    <select class="form-control" id="parent_id" name="parent_id">
                                        <option value="">None</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->title_en }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="details" class="form-label">Details</label>
                                    <div id="details">
                                        <div class="detail-item d-flex align-items-center mb-2">
                                            <input type="text" class="form-control me-2" name="details[0][label_en]" placeholder="Label (EN)" required>
                                            <input type="text" class="form-control me-2" name="details[0][label_fr]" placeholder="Label (FR)" required>
                                            <input type="text" class="form-control me-2" name="details[0][label_ar]" placeholder="Label (AR)" required>
                                            <button type="button" class="btn btn-danger btn-sm remove-detail">-</button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="addDetail">+</button>
                                </div>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            $('#addDetail').on('click', function() {
                var index = $('#details .detail-item').length;
                var detailHtml = `
                    <div class="detail-item d-flex align-items-center mb-2">
                        <input type="text" class="form-control me-2" name="details[${index}][label_en]" placeholder="Label (EN)" required>
                        <input type="text" class="form-control me-2" name="details[${index}][label_fr]" placeholder="Label (FR)" required>
                        <input type="text" class="form-control me-2" name="details[${index}][label_ar]" placeholder="Label (AR)" required>
                        <button type="button" class="btn btn-danger btn-sm remove-detail">-</button>
                    </div>
                `;
                $('#details').append(detailHtml);
            });

            $(document).on('click', '.remove-detail', function() {
                $(this).closest('.detail-item').remove();
            });
        });
    </script>
@endpush
