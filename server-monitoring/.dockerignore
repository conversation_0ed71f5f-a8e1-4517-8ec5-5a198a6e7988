# Docker ignore file for Server Monitoring

# Git
.git
.gitignore
.gitattributes

# Docker files (avoid recursion)
Dockerfile
docker-compose.yml
.dockerignore

# Environment files
.env
.env.local
.env.*.local

# Logs
docker/logs/
*.log

# Temporary files
tmp/
temp/
.tmp

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Node modules (if any)
node_modules/
npm-debug.log*

# Composer
vendor/
composer.lock

# Backup files
backups/
*.backup
*.bak

# Test files
tests/
phpunit.xml

# Documentation
README.md
docs/

# SSL certificates (will be generated)
docker/ssl/

# Cache
cache/
.cache/
