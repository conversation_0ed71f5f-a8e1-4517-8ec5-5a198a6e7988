<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShareOrder extends Model
{
    protected $table = 'share_order';

    protected $fillable = [
        'user_id',
        'order_id',
        
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
