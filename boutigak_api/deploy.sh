#!/bin/bash

# Laravel Deployment Script for Boutigak API
# Usage: ./deploy.sh [server1|server2|both]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Server configurations
declare -A SERVERS
SERVERS[server1_host]="**************"
SERVERS[server1_path]="/var/www/html/boutigak_api"
SERVERS[server1_name]="App Server 1"

SERVERS[server2_host]="************"
SERVERS[server2_path]="/var/www/boutigak_api"
SERVERS[server2_name]="App Server 2"

# Common configuration
SSH_USER="root"
SSH_PASSWORD="Boutigak@36666688@38407840"
SSH_PORT="22"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to deploy to a specific server
deploy_to_server() {
    local server_key=$1
    local host=${SERVERS[${server_key}_host]}
    local path=${SERVERS[${server_key}_path]}
    local name=${SERVERS[${server_key}_name]}
    
    log_info "Starting deployment to $name ($host)"
    
    # Create deployment archive
    log_info "Creating deployment archive..."
    tar -czf deployment.tar.gz \
        --exclude='.git' \
        --exclude='.github' \
        --exclude='node_modules' \
        --exclude='.env' \
        --exclude='storage/logs/*' \
        --exclude='storage/framework/cache/*' \
        --exclude='storage/framework/sessions/*' \
        --exclude='storage/framework/views/*' \
        --exclude='tests' \
        --exclude='*.md' \
        --exclude='deploy.sh' \
        .
    
    # Upload and deploy
    log_info "Uploading to $name..."
    
    # Using sshpass for password authentication
    if ! command -v sshpass &> /dev/null; then
        log_error "sshpass is required but not installed. Please install it first."
        log_info "Ubuntu/Debian: sudo apt-get install sshpass"
        log_info "macOS: brew install sshpass"
        exit 1
    fi
    
    # Upload file
    sshpass -p "$SSH_PASSWORD" scp -P $SSH_PORT deployment.tar.gz $SSH_USER@$host:/tmp/
    
    # Execute deployment commands
    log_info "Executing deployment commands on $name..."
    
    sshpass -p "$SSH_PASSWORD" ssh -p $SSH_PORT $SSH_USER@$host << EOF
        set -e
        
        echo "🚀 Starting deployment on $name"
        
        # Create backup
        if [ -d "$path" ]; then
            cp -r $path ${path}_backup_\$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
        fi
        
        # Create directory if it doesn't exist
        mkdir -p $path
        
        # Extract files
        cd $path
        echo "📦 Extracting deployment archive..."
        tar -xzf /tmp/deployment.tar.gz
        rm /tmp/deployment.tar.gz
        
        # Create .env file if it doesn't exist
        if [ ! -f .env ]; then
            cp .env.example .env
            echo "📝 Created .env file from .env.example"
        fi
        
        # Set environment variables
        echo "⚙️ Configuring environment variables..."
        sed -i 's/APP_ENV=.*/APP_ENV=production/' .env
        sed -i 's/APP_DEBUG=.*/APP_DEBUG=false/' .env
        sed -i 's|APP_URL=.*|APP_URL=https://boutigak.com|' .env
        
        # Database configuration
        sed -i 's/DB_HOST=.*/DB_HOST=************/' .env
        sed -i 's/DB_PORT=.*/DB_PORT=5432/' .env
        sed -i 's/DB_DATABASE=.*/DB_DATABASE=boutigak_db/' .env
        sed -i 's/DB_USERNAME=.*/DB_USERNAME=postgress/' .env
        sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=adminboutigak/' .env
        
        # SFTP Configuration
        sed -i 's|SFTP_BASE_URL=.*|SFTP_BASE_URL=https://************|' .env
        sed -i 's/USE_SFTP_FOR_IMAGES=.*/USE_SFTP_FOR_IMAGES=true/' .env
        
        # Create necessary directories
        echo "📁 Creating necessary directories..."
        mkdir -p storage/framework/{cache,sessions,views}
        mkdir -p storage/logs
        mkdir -p storage/app/public
        mkdir -p bootstrap/cache
        
        # Set permissions
        echo "🔒 Setting file permissions..."
        chown -R www-data:www-data $path
        chmod -R 755 $path
        chmod -R 775 storage
        chmod -R 775 bootstrap/cache
        
        # Laravel commands
        echo "🔧 Running Laravel optimization commands..."
        
        # Generate application key if not exists
        if ! grep -q "APP_KEY=base64:" .env; then
            php artisan key:generate --force
            echo "🔑 Generated new application key"
        fi
        
        # Database operations (only run on first server)
        if [ "$host" = "**************" ]; then
            echo "🗄️ Running database migrations..."
            php artisan migrate --force
        else
            echo "⏭️ Skipping migrations on secondary server"
        fi
        
        # Cache optimization
        php artisan config:cache
        php artisan route:cache
        php artisan view:cache
        
        # Create storage link
        php artisan storage:link || echo "Storage link already exists"
        
        # Clear caches
        echo "🧹 Clearing caches..."
        php artisan cache:clear
        
        # Queue management
        echo "🔄 Managing queues..."
        php artisan queue:restart || echo "Queue restart completed"
        
        # Restart services
        echo "🔄 Restarting web services..."
        if command -v systemctl &> /dev/null; then
            systemctl reload apache2 || systemctl reload nginx || echo "Web server reload completed"
            systemctl restart php8.1-fpm || systemctl restart php-fpm || echo "PHP-FPM restart completed"
        fi
        
        # Verify deployment
        echo "✅ Verifying deployment..."
        if php artisan --version; then
            echo "✅ Laravel is working correctly"
        else
            echo "❌ Laravel verification failed"
            exit 1
        fi
        
        echo "🎉 Deployment completed successfully on $name"
EOF
    
    # Clean up local deployment archive
    rm -f deployment.tar.gz
    
    log_success "Deployment to $name completed successfully!"
}

# Main script
main() {
    local target=${1:-both}
    
    log_info "Boutigak API Deployment Script"
    log_info "Target: $target"
    
    # Check if we're in the right directory
    if [ ! -f "composer.json" ] || [ ! -f "artisan" ]; then
        log_error "This script must be run from the Laravel project root directory"
        exit 1
    fi
    
    # Install dependencies and build assets
    log_info "Installing dependencies and building assets..."
    composer install --no-dev --optimize-autoloader
    npm ci && npm run build
    
    case $target in
        "server1")
            deploy_to_server "server1"
            ;;
        "server2")
            deploy_to_server "server2"
            ;;
        "both")
            deploy_to_server "server1"
            deploy_to_server "server2"
            ;;
        *)
            log_error "Invalid target. Use: server1, server2, or both"
            exit 1
            ;;
    esac
    
    log_success "All deployments completed successfully!"
}

# Show usage if help is requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Usage: $0 [server1|server2|both]"
    echo ""
    echo "Deploy Laravel application to production servers"
    echo ""
    echo "Options:"
    echo "  server1    Deploy only to App Server 1 (**************)"
    echo "  server2    Deploy only to App Server 2 (************)"
    echo "  both       Deploy to both servers (default)"
    echo "  --help     Show this help message"
    echo ""
    echo "Requirements:"
    echo "  - sshpass (for SSH password authentication)"
    echo "  - composer (for dependency management)"
    echo "  - npm (for asset building)"
    exit 0
fi

# Run main function
main "$@"
