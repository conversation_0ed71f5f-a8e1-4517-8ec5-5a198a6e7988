<?php

namespace App\Http\Controllers;

use Exception;
use Carbon\Carbon;
use App\Models\Item;
use App\Models\User;
use App\Models\Brand;
use App\Models\Media;
use App\Models\Store;
use App\Models\Category;
use App\Models\ItemMedia;
use App\Models\PromoCode;
use App\Enums\eItemStatus;
use App\Models\ItemHistory;
use App\Models\ItemPayment;
use App\Models\Notification;
use App\services\FCMService;
use App\services\ImageOptimizationService;
use Illuminate\Http\Request;
use App\Models\CategoryPrice;
use App\Models\SearchHistory;
use App\Models\CategoryDetail;
use Yajra\DataTables\DataTables;
use Illuminate\Http\JsonResponse;
use App\Models\CategoryItemDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Intervention\Image\Laravel\Facades\Image;
class ItemController extends Controller
{
    protected FCMService $firebaseService;
    protected ImageOptimizationService $imageOptimizationService;

    public function __construct(FCMService $firebaseService, ImageOptimizationService $imageOptimizationService)
    {
        $this->firebaseService = $firebaseService;
        $this->imageOptimizationService = $imageOptimizationService;
    }

    private function getLocalizedNotificationMessages(string $type, array $params = []): array
    {
        // Set default values for all possible parameters
        $defaultParams = [
            'status' => '',
            'is_promoted' => false,
            'store_name' => '',
            'customer_name' => '',
            'item_title' => '',
            'reason' => '',
            'follower_name' => '',
            'sender_name' => '',
            'price' => '',
            'content' => '',
        ];

        // Merge provided params with defaults
        $params = array_merge($defaultParams, array_filter($params));

        // Helper function to format Arabic text with proper RTL handling
        $formatArabic = function (string $text, array $values = []): string {
            return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
        };

        $messages = [
            'item_status_update' => [
                'en' => [
                    'title' => 'Item Status Updated',
                    'body' => sprintf("Your item status has been updated to: %s", $params['status'])
                ],
                'fr' => [
                    'title' => 'Statut de l\'article mis à jour',
                    'body' => sprintf("Le statut de votre article a été mis à jour vers: %s", $params['status'])
                ],
                'ar' => [
                    'title' => 'تم تحديث حالة العنصر',
                    'body' => $formatArabic("%s :تم تحديث حالة عنصرك إلى", [$params['status']])
                ]
            ],
            'item_promoted' => [
                'en' => [
                    'title' => 'Item Promotion Status',
                    'body' => $params['is_promoted'] ? 'Your item has been promoted' : 'Your item is no longer promoted'
                ],
                'fr' => [
                    'title' => 'Statut de promotion',
                    'body' => $params['is_promoted'] ? 'Votre article a été promu' : 'Votre article n\'est plus promu'
                ],
                'ar' => [
                    'title' => 'حالة ترويج العنصر',
                    'body' => $params['is_promoted'] ? 'تم ترويج العنصر الخاص بك' : 'لم يعد يتم ترويج العنصر الخاص بك'
                ]
            ],
            'new_order' => [
                'en' => [
                    'title' => 'New Order Received',
                    'body' => sprintf("You have received a new order from %s", $params['customer_name'])
                ],
                'fr' => [
                    'title' => 'Nouvelle Commande Reçue',
                    'body' => sprintf("Vous avez reçu une nouvelle commande de %s", $params['customer_name'])
                ],
                'ar' => [
                    'title' => 'طلب جديد',
                    'body' => $formatArabic("%s لقد تلقيت طلبًا جديدًا من", [$params['customer_name']])
                ]
            ],
            'new_item' => [
                'en' => [
                    'title' => 'New Item Added',
                    'body' => sprintf("%s has added a new item: %s", $params['store_name'], $params['item_title'])
                ],
                'fr' => [
                    'title' => 'Nouvel Article Ajouté',
                    'body' => sprintf("%s a ajouté un nouvel article: %s", $params['store_name'], $params['item_title'])
                ],
                'ar' => [
                    'title' => 'تمت إضافة عنصر جديد',
                    'body' => $formatArabic("%s :%s أضاف عنصرًا جديدًا", [$params['item_title'], $params['store_name']])
                ]
            ],
            'order_modified' => [
                'en' => [
                    'title' => 'Order Modified',
                    'body' => sprintf("Your order has been modified by %s. Reason: %s", $params['store_name'], $params['reason'])
                ],
                'fr' => [
                    'title' => 'Commande Modifiée',
                    'body' => sprintf("Votre commande a été modifiée par %s. Raison: %s", $params['store_name'], $params['reason'])
                ],
                'ar' => [
                    'title' => 'تم تعديل الطلب',
                    'body' => $formatArabic("%s :السبب .%s تم تعديل طلبك بواسطة", [$params['reason'], $params['store_name']])
                ]
            ],
            'new_follower' => [
                'en' => [
                    'title' => 'New Follower',
                    'body' => sprintf("%s is now following your store", $params['follower_name'])
                ],
                'fr' => [
                    'title' => 'Nouvel Abonné',
                    'body' => sprintf("%s suit maintenant votre boutique", $params['follower_name'])
                ],
                'ar' => [
                    'title' => 'متابع جديد',
                    'body' => $formatArabic("يتابع متجرك الآن %s", [$params['follower_name']])
                ]
            ],
            'new_message' => [
                'en' => [
                    'title' => 'New Message',
                    'body' => sprintf("New message from %s", $params['sender_name'])
                ],
                'fr' => [
                    'title' => 'Nouveau Message',
                    'body' => sprintf("Nouveau message de %s", $params['sender_name'])
                ],
                'ar' => [
                    'title' => 'رسالة جديدة',
                    'body' => $formatArabic("%s رسالة جديدة من", [$params['sender_name']])
                ]
            ],
            'new_offer' => [
                'en' => [
                    'title' => 'New Offer',
                    'body' => sprintf("%s made an offer: %s", $params['sender_name'], $params['price'])
                ],
                'fr' => [
                    'title' => 'Nouvelle Offre',
                    'body' => sprintf("%s a fait une offre: %s", $params['sender_name'], $params['price'])
                ],
                'ar' => [
                    'title' => 'عرض جديد',
                    'body' => $formatArabic("%s :%s قدم عرضًا", [$params['price'], $params['sender_name']])
                ]
            ]
        ];

        // Log notification parameters for debugging
        Log::debug('Notification parameters', [
            'type' => $type,
            'params' => $params
        ]);

        return $messages[$type] ?? $messages['item_status_update'];
    }

    private function sendLocalizedNotification(User $user, string $type, array $params = [], $storeID = null): void
    {
        $token = $user->deviceToken->token ?? null;

        if (!$token) {
            Log::warning('No device token found for user', [
                'user_id' => $user->id
            ]);
            return;
        }

        try {
            $userLang = $user->lang ?? 'en'; // Default to English if no language set

            // Get messages for all languages
            $messages = $this->getLocalizedNotificationMessages($type, $params);

            // Get the message in user's preferred language for push notification
            $localizedMessage = $messages[$userLang] ?? $messages['en']; // Fallback to English

            // Create notification with all language versions
            Notification::create([
                'user_id' => $user->id,
                'store_id' => $storeID,
                'type' => $type,
                'is_read' => false,
                // Store titles in all languages
                'title_en' => $messages['en']['title'],
                'title_fr' => $messages['fr']['title'],
                'title_ar' => $messages['ar']['title'],
                // Store messages in all languages
                'message_en' => $messages['en']['body'],
                'message_fr' => $messages['fr']['body'],
                'message_ar' => $messages['ar']['body'],
                // Store the selected language version for backwards compatibility
                // 'title' => $localizedMessage['title'],
                // 'message' => $localizedMessage['body'],
                // Store additional params as JSON for potential future use
                'params' => json_encode($params)
            ]);

            // Send push notification in user's preferred language
            $this->firebaseService->sendNotification(
                $token,
                $localizedMessage['title'],
                $localizedMessage['body']
            );

            Log::info('Notification sent successfully', [
                'user_id' => $user->id,
                'language' => $userLang,
                'type' => $type,
                'store_id' => $storeID
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'type' => $type,
                'store_id' => $storeID
            ]);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/items",
     *     tags={"Item"},
     *     summary="Create a new item",
     *     description="Create a new item with the provided details.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="condition", type="string", example="new"),
     *                 @OA\Property(property="brand_id", type="integer", example=1),
     *                 @OA\Property(property="category_id", type="integer", example=1),
     *                 @OA\Property(property="category_item_details", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="value", type="string", example="Sample value")
     *                     )
     *                 ),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(type="string", format="binary")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=201, description="Successful creation"),
     *     @OA\Response(response=400, description="Validation error"),
     *     @OA\Response(response=500, description="Failed to create item")
     * )
     */
    public function store(Request $request)
    {
        $headers = $request->headers->all();

        Log::info("Headers: " . json_encode($headers));

        Log::info('in request ,,,,' . json_encode($request->all()));

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'title_ar' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',

            'price' => 'required|numeric',
            'condition' => 'required|string',
            'brand_id' => 'required|integer|exists:brand,id',
            'category_id' => 'required|integer|exists:category,id',
            'category_detail_id' => 'required',
            'category_item_details' => 'nullable|array',
            'category_item_details.*.id' => 'nullable|integer|exists:category_detail,id',
            'category_item_details.*.value' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,gif,svg,heif',
                'max:20048',
            ]

        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Create the item


            $item = Item::create($validator->validated());


            Log::info('user ' . json_encode(auth()->user()));
            $item->user_id = auth()->user()->id;

            $item->save();


            $categoryDetails = CategoryDetail::where('category_id', $validator->validated()['category_detail_id'])->get();


            // $categoryItemDetails = $categoryDetails->map(function ($categoryDetail) use ($item) {
            //     return [
            //         'label_en' => $categoryDetail->label_en,
            //         'label_ar' => $categoryDetail->label_ar,
            //         'label_fr' => $categoryDetail->label_fr,
            //         'value' => $categoryDetail->label_en, // Adjust if needed
            //         'item_id' => $item->id,
            //     ];
            // })->toArray();

            // // Add category item details to the database
            // CategoryItemDetail::insert($categoryItemDetails);

            // If there are additional category item details from the request, insert them
            if ($request->has('category_item_details')) {
                foreach ($request->input('category_item_details') as $detail) {
                    CategoryItemDetail::updateOrCreate(
                        ['id' => $detail['id'],
                        'label_en' => $detail['label_en'],
                        'label_ar' => $detail['label_ar'],
                        'label_fr' => $detail['label_fr'],
                        'item_id' => $item->id],
                        ['value' => $detail['value']]
                    );
                }
            }



    if ($request->has('image_order_updated') && $request->input('image_order_updated') === 'true') {
        Log::info("in image_order_updated");
        if ($request->has('image_orders')) {
            // Decode the JSON string to an array
            $imageOrders = json_decode($request->input('image_orders'), true);

            if (is_array($imageOrders)) {
                foreach ($imageOrders as $imageId => $order) {
                    $itemMedia = ItemMedia::where('media_id', (int)$imageId)
                                        ->where('item_id', $item->id)
                                        ->first();
                    if ($itemMedia) {
                        $itemMedia->update(['order' => (int)$order]);
                    }
                }
            } else {
                Log::warning('Invalid image_orders format: ' . $request->input('image_orders'));
            }
        }

        Log::info('Processing image orders for item ' . $item->id);
        Log::info('Image orders: ' . $request->input('image_orders'));
    }

// When handling new image uploads
if ($request->hasFile('images')) {
    foreach ($request->file('images') as $index => $image) {
        // Use the ImageOptimizationService to optimize and store the image
        $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'item_images');

        $media = Media::create([
            'url' => $optimizedImageData['url'],
        ]);

        // Use the provided order if available, otherwise use index
        $order = $request->has('new_image_orders') && isset($request->input('new_image_orders')[$index])
            ? (int)$request->input('new_image_orders')[$index]
            : $index;

        ItemMedia::create([
            'media_id' => $media->id,
            'item_id' => $item->id,
            'order' => $order,
        ]);
    }
}


            Log::info('request id  ' . $request->category_id);


            $categroies = CategoryPrice::all();

            Log::info('categories ... ' . json_encode($categroies));


            // TODO: set default category price in bo
            $categoryPrice = CategoryPrice::firstWhere('category_id', $request->category_id)->price ?? '50.0';


            Log::info('categoryPrice... ' . json_encode($categoryPrice));


            DB::commit();

            return response()->json(

                [
                    'item' => $item,
                    'categoryPrice' => $categoryPrice
                ]

                , 201);
        } catch (\Exception $e) {
            // Rollback the transaction if something went wrong
            DB::rollback();

            Log::error('Error creating item: ' . $e->getMessage());

            return response()->json(['error' => 'Failed to create item'], 500);
        }
    }


    public function update(Request $request)
    {

        Log::info('in request update item ,,,,' . json_encode($request->all()));
        // Define validation rules
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'title_ar' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'price' => 'required|numeric',
            'condition' => 'required|string',
            'brand_id' => 'required|integer|exists:brand,id',
            'category_id' => 'required|integer|exists:category,id',
            'category_item_details' => 'nullable|array',
            'category_item_details.*.id' => 'nullable|integer|exists:category_item_detail,id',
            'category_item_details.*.value' => 'nullable|string',
            'images' => 'nullable|array|max:5', // Limit number of images
            'images.*' => [
                'required',
                'file',
                'mimes:jpeg,png,jpg,gif,svg,heif',
                'max:20000048',
            ]

        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            $item = Item::find($request->id);

            Log::info('log item ' . json_encode($item));

            // Update the item
            $item->update($validator->validated());


            $item->user_id = auth()->user()->id ?? null;

            $item->save();
            // Remove old category item details if any
            CategoryItemDetail::where('item_id', $item->id)->delete();


            // If there are additional category item details from the request, insert them
            if ($request->has('category_item_details')) {
                foreach ($request->input('category_item_details') as $detail) {
                    CategoryItemDetail::updateOrCreate(
                        ['id' => $detail['id'],
                        'label_en' => $detail['label_en'],
                        'label_ar' => $detail['label_ar'],
                        'label_fr' => $detail['label_fr'],
                        'item_id' => $item->id],
                        ['value' => $detail['value']]
                    );
                }
            }

            if ($request->has('image_order_updated') && $request->input('image_order_updated') === 'true') {
                Log::info("in image_order_updated");
                if ($request->has('image_orders')) {
                    // Decode the JSON string to an array
                    $imageOrders = json_decode($request->input('image_orders'), true);

                    if (is_array($imageOrders)) {
                        foreach ($imageOrders as $imageId => $order) {
                            $itemMedia = ItemMedia::where('media_id', (int)$imageId)
                                                ->where('item_id', $item->id)
                                                ->first();
                            if ($itemMedia) {
                                $itemMedia->update(['order' => (int)$order]);
                            }
                        }
                    } else {
                        Log::warning('Invalid image_orders format: ' . $request->input('image_orders'));
                    }
                }

                Log::info('Processing image orders for item ' . $item->id);
                Log::info('Image orders: ' . $request->input('image_orders'));
            }

        // When handling new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                // Use the ImageOptimizationService to optimize and store the image
                $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'item_images');

                $media = Media::create([
                    'url' => $optimizedImageData['url'],
                ]);

                // Use the provided order if available, otherwise use index
                $order = $request->has('new_image_orders') && isset($request->input('new_image_orders')[$index])
                    ? (int)$request->input('new_image_orders')[$index]
                    : $index;

                ItemMedia::create([
                    'media_id' => $media->id,
                    'item_id' => $item->id,
                    'order' => $order,
                ]);
            }
        }


            // Commit the transaction
            DB::commit();

            return response()->json($item, 200);
        } catch (\Exception $e) {
            // Rollback the transaction if something went wrong
            DB::rollback();

            Log::info('log error ' . json_encode($e->getMessage()));

            return response()->json(['error' => 'Failed to update item'], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/liked-items",
     *     tags={"Item"},
     *     summary="Get liked items",
     *     description="Retrieve a list of items liked by the authenticated user.",
     *     operationId="getLikedItems",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function getLikedItems(Request $request)
    {
        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user();
        Log::info("user: " . json_encode($user));

        // Fetch liked items with their related brand
        $likedItems = $user->likedItems()->with('brand')->get();

        // Add is_liked attribute
        $likedItems->each(function ($item) {
            $item->is_liked = true;
        });

        return response()->json($likedItems, 200);
    }

    /**
     * @OA\Post(
     *     path="/api/items/like-unlike/{item}",
     *     tags={"Item"},
     *     summary="Like or unlike an item",
     *     description="Like or unlike an item by the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="item",
     *         in="path",
     *         required=true,
     *         description="The item's ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Item liked")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */

    public function likeUnlike(Request $request, Item $item)
    {
        $user = auth()->user();

        // Check if user has already liked the item
        $liked = $user->likedItems()->where('item_id', $item->id)->exists();

        if ($liked) {
            // Unlike the item
            $user->likedItems()->detach($item->id);
            return response()->json(['message' => 'Item unliked'], 200);
        } else {
            // Like the item
            $user->likedItems()->attach($item->id);
            return response()->json(['message' => 'Item liked'], 200);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/items/recommended",
     *     tags={"Item"},
     *     summary="Get recommended items",
     *     description="Retrieve items that the user already liked and have the same category as the stores they follow.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(response=200, description="Successful operation"),
     *     @OA\Response(response=401, description="Unauthorized")
     * )
     */
    public function getRecommendedItems(Request $request)
    {
        $user = auth()->user();
        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 10);
        $halfPerPage = ceil($perPage / 2);

        // Base query for all items
        $query = Item::where('status', eItemStatus::APPROVED)
            ->where('user_id', '!=', $user->id)
            ->whereNull('store_id')
            ->with(['brand', 'category', 'user']);

        // Calculate total counts for pagination
        $totalPromoted = clone $query;
        $totalPromoted = $totalPromoted->where('is_promoted', true)->count();

        $totalRecommended = clone $query;
        $totalRecommended = $totalRecommended
            ->where('is_promoted', false)
            ->where(function ($q) use ($user) {
                $q->whereIn('category_id', $user->likedItems()->pluck('category_id'))
                    ->orWhereIn('store_id', $user->followedStores()->pluck('store_id'));
            })
            ->count();

        $totalItems = $totalPromoted + $totalRecommended;

        // Calculate offsets
        $promotedOffset = ($page - 1) * $halfPerPage;
        $regularOffset = ($page - 1) * ($perPage - $halfPerPage);

        // Get promoted items first
        $promotedItems = clone $query;
        $promotedItems = $promotedItems->where('is_promoted', true)
            ->orderBy('created_at', 'desc')
            ->skip($promotedOffset)
            ->take($halfPerPage)
            ->get();

        // Get recommended items based on user preferences
        $recommendedItems = clone $query;
        $recommendedItems = $recommendedItems
            ->where('is_promoted', false)
            ->where(function ($q) use ($user) {
                $q->whereIn('category_id', $user->likedItems()->pluck('category_id'))
                    ->orWhereIn('store_id', $user->followedStores()->pluck('store_id'));
            })
            ->orderBy('created_at', 'desc')
            ->skip($regularOffset)
            ->take($perPage - $halfPerPage)
            ->get();

        // Merge items
        $items = $promotedItems->concat($recommendedItems);

        // Add is_liked attribute
        $likedItemIds = $user->likedItems()->pluck('item_id');
        $items->each(function ($item) use ($likedItemIds) {
            $item->is_liked = $likedItemIds->contains($item->id);
        });

        return response()->json([
            'items' => $items,
            'total' => $totalItems,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage,
            'last_page' => ceil($totalItems / $perPage)
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/followed-stores",
     *     tags={"Item"},
     *     summary="Get followed stores",
     *     description="Retrieve all stores followed by the authenticated user.",
     *     operationId="getFollowedStores",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="description", type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="No stores found")
     * )
     */
    public function getFollowedStores(Request $request)
    {
        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user();
        Log::info("User: " . json_encode($user));

        // Fetch followed stores
        $followedStores = $user->followedStores()->get();

        return response()->json($followedStores, 200);
    }

    /**
     * @OA\Get(
     *     path="/api/items/search",
     *     tags={"Item"},
     *     summary="Search for items",
     *     description="Search for items based on a query string. If the user is authenticated, the search history will be saved.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         required=true,
     *         description="The search query string",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(property="status", type="string", example="APPROVED"),
     *                 @OA\Property(property="brand", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Brand Name")
     *                 ),
     *                 @OA\Property(property="category", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Category Title")
     *                 ),
     *                 @OA\Property(property="user", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="User Name")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     )
     * )
     */
    public function search(Request $request)
    {
        $query = $request->input('query');

        if (empty($query)) {
            return response()->json(['error' => 'Query parameter is required'], 400);
        }

        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user() ?? null;
        Log::info("User: " . json_encode($user));

        if ($user) {
            $existingSearch = SearchHistory::where('user_id', $user->id)
                ->where('query', $query)
                ->exists();

            if (!$existingSearch) {
                SearchHistory::create([
                    'user_id' => $user->id,
                    'query' => $query,
                    'searched_at' => now()
                ]);
            }
        }

        $items = Item::search($query)->with('brand')->get();

        foreach ($items as $item) {
            $category = $item->category;
            if ($category) {
                $category->increment('search_count');
            }
        }

        Log::info("Items: " . json_encode($items));

        return response()->json($items, 200);
    }

    /**
     * @OA\Put(
     *     path="/api/items-status/{item}",
     *     tags={"Item"},
     *     summary="Update item status",
     *     description="Change the status of an item. If the status is 'REJECTED', a reason must be provided.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="item",
     *         in="path",
     *         required=true,
     *         description="The item's ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", example="APPROVED"),
     *             @OA\Property(property="rejection_reason", type="string", example="Reason for rejection")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Successful operation"),
     *     @OA\Response(response=400, description="Validation error"),
     *     @OA\Response(response=404, description="Item not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     *
     * Update the status of an item.
     *
     * @param Request $request
     * @param Item $item
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateStatus(Request $request, Item $item): JsonResponse
    {
        // Log request data
        Log::info('Request data: ' . json_encode($request->all()));

        Log::info('item : ' . json_encode($item));


        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:APPROVED,REJECTED,PENDING',
            'rejection_reason' => 'required_if:status,REJECTED|string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $validatedData = $validator->validated();

        try {
            // Update item status
            $item->status = $validatedData['status'];

            if ($item->status === 'REJECTED') {
                $item->rejection_reason = $validatedData['rejection_reason'] ?? null;
            }

            $item->save();

            // Set locale to French for date formatting
            Carbon::setLocale('fr');
            $formattedDate = Carbon::parse($item->updated_at)->translatedFormat('j F Y, H:i');


            $this->sendLocalizedNotification(
                $item->user,
                'item_status_update',
                ['status' => $item->status]
            );

            return response()->json([
                'message' => match ($item->status) {
                    'APPROVED' => 'Item approved successfully',
                    'REJECTED' => 'Item rejected: ' . $item->rejection_reason,
                    default => 'Item status updated successfully'
                },
                'item' => $item,
                'updated_at' => $formattedDate
            ], 200);

        } catch (\Exception $e) {
            Log::error('Failed to update item status: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update item status'], 500);
        }
    }


    /**
     * @OA\Get(
     *     path="/api/search-history",
     *     tags={"Search"},
     *     summary="Get search history",
     *     description="Retrieve the search history of the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="query", type="string", example="Sample query"),
     *                 @OA\Property(property="searched_at", type="string", format="date-time", example="2024-07-25T15:32:07Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function getSearchHistory(Request $request)
    {
        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user();
        Log::info("User: " . json_encode($user));

        // Fetch the search history for the authenticated user
        $searchHistory = SearchHistory::where('user_id', $user->id ?? null)
            ->orderBy('searched_at', 'desc')
            ->get();

        return response()->json($searchHistory, 200);
    }


    public function mostSearchedCategories()
    {
        // Retrieve the 10 most searched categories with search_count > 0
        $categories = Category::where('search_count', '>', 0)
            ->orderBy('search_count', 'desc')
            ->limit(10)
            ->get();

        return response()->json($categories);
    }

    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $query = Item::query()->with(['images', 'brand', 'user', 'category'])
                ->whereNull('store_id'); // Only include items that do not belong to a store

            // is_promoted filter
            if ($request->filled('is_promoted')) {
                $query->where('is_promoted', $request->is_promoted);
            }

            // is_approved filter
            if ($request->filled('is_approved')) {
                $query->where('status', $request->is_approved === 'approved' ? eItemStatus::APPROVED : eItemStatus::CREATED);
            }

            // category filter
            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // user filter
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            // sort order filter
            if ($request->filled('sort_order')) {
                if ($request->sort_order === 'recent') {
                    $query->orderBy('created_at', 'desc');
                } elseif ($request->sort_order === 'oldest') {
                    $query->orderBy('created_at', 'asc');
                } elseif ($request->sort_order === 'sponsored') {
                    $query->where('is_promoted', 1);
                }
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('first_image', function (Item $item) {
                    return $item->images->first() ? $item->images->first()->url : null;
                })
                ->addColumn('actions', function ($row) {
                    $btn = '<a href="' . route('items.show', $row->id) . '" class="show btn btn-info btn-sm">
                    <i class="fas fa-eye"></i> Show
                </a>';

                    if ($row->category->title_en === 'Real Estate') {
                        $btn .= ' <button class="btn btn-primary btn-sm" onclick="openMatterportModal(' . $row->id . ')">
                        <i class="fas fa-link"></i> Add Matterport Link
                    </button>';
                    }

                    return $btn;
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        $stores = Store::all();
        $users = User::all();
        $categories = Category::all();

        // Counts for filters
        $promotedCount = Item::where('is_promoted', 1)->count();
        $notPromotedCount = Item::where('is_promoted', 0)->count();
        $approvedCount = Item::where('status', eItemStatus::APPROVED)->count();
        $notApprovedCount = Item::where('status', eItemStatus::CREATED)->count();

        return view('backoffice.items.index', compact('stores', 'users', 'categories', 'promotedCount', 'notPromotedCount', 'approvedCount', 'notApprovedCount'));
    }

    public function addMatterportLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'item_id' => 'required|integer|exists:item,id',
            'matterport_link' => 'required|url',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $item = Item::find($request->item_id);
        $item->matterport_link = $request->matterport_link;
        $item->save();

        return response()->json(['message' => 'Matterport link added successfully.']);
    }

    public function show($id)
    {
        try {
            $item = Item::with(['categoryDetails', 'images', 'payments'])->findOrFail($id);

            // Transform the item data as needed
            $transformedItem = [
                'id' => $item->id,
                'title' => $item->title,
                'title_ar' => $item->title_ar,
                'description' => $item->description,
                'description_ar' => $item->description_ar,
                'price' => $item->price,
                'condition' => $item->condition,
                'brand_id' => $item->brand_id,
                'category_id' => $item->category_id,
                'category_detail_id' => $item->category_detail_id,
                'category_item_details' => $item->categoryDetails,
                'images' => $item->images->pluck('url')->toArray(),
            ];

            return view('backoffice.items.show', compact('item', 'transformedItem'));
        } catch (\Exception $e) {
            Log::error('Error fetching item: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch item'], 500);
        }
    }

    public function updateStatusBo(Request $request, Item $item)
    {

        // Log request data
        Log::info('Request data: ' . json_encode($request->all()));

        Log::info('item : ' . json_encode($item));

        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:' . implode(',', eItemStatus::getAll(eItemStatus::class)),
            'rejection_reason' => 'nullable|string|required_if:status,' . eItemStatus::REJECTED,
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();

            $item->status = $data['status'];

            if ($data['status'] === eItemStatus::REJECTED) {
                $item->rejection_reason = $data['rejection_reason'];
                $item->is_promoted = false;
            } else {
                $item->rejection_reason = null;
            }

            $item->save();


            // Send localized notification
            $this->sendLocalizedNotification(
                $item->user,
                'item_status_update',
                ['status' => $item->status]
            );

            $message = 'Item status updated successfully to ' . $data['status'];
            return redirect()->route('items.show', $item->id)->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Error updating item status in updateStatusBo: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update item status');
        }
    }

    public function togglePromote(Item $item)
    {
        try {
            $item->is_promoted = !$item->is_promoted;
            $item->save();

            // Send localized notification
            $this->sendLocalizedNotification(
                $item->user,
                'item_promoted',
                ['is_promoted' => $item->is_promoted]
            );

            $message = $item->is_promoted ? 'Item promoted successfully' : 'Item unpromoted successfully';
            return redirect()->route('items.show', $item->id)->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Error toggling item promotion: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update item promotion status');
        }
    }

    /**
     * Get general recommended items.
     *
     * @OA\Get(
     *     path="/api/items/general-recommended",
     *     tags={"Item"},
     *     summary="Get general recommended items",
     *     description="Retrieve a list of general recommended items.",
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(property="status", type="string", example="APPROVED"),
     *                 @OA\Property(property="brand", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Brand Name")
     *                 ),
     *                 @OA\Property(property="category", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Category Title")
     *                 ),
     *                 @OA\Property(property="user", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="User Name")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function getGeneralRecommendedItems(Request $request)
    {
        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 10);
        $offset = ($page - 1) * $perPage;

        // Base query for all items
        $query = Item::where('status', eItemStatus::APPROVED)
            ->whereNull('store_id')
            ->with(['brand', 'category', 'user']);

        // Get promoted items first
        $promotedItems = clone $query;
        $promotedItems = $promotedItems->where('is_promoted', true)
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($perPage / 2)
            ->get();

        $remainingCount = $perPage - $promotedItems->count();

        // Get regular items
        $regularItems = clone $query;
        $regularItems = $regularItems
            ->where('is_promoted', false)
            ->orderBy('created_at', 'desc')
            ->skip($offset)
            ->take($remainingCount)
            ->get();

        // Merge items
        $items = $promotedItems->concat($regularItems);

        // Get total count for pagination
        $totalItems = Item::where('status', eItemStatus::APPROVED)
            ->whereNull('store_id')
            ->count();

        // Transform items to include Arabic title and description
        $transformedItems = $items->map(function ($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'title_ar' => $item->title_ar,
                'description' => $item->description,
                'description_ar' => $item->description_ar,
                'price' => $item->price,
                'condition' => $item->condition,
                'brand_id' => $item->brand_id,
                'category_id' => $item->category_id,
                'is_promoted' => $item->is_promoted,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
                'brand' => $item->brand,
                'category' => $item->category,
                'user' => [
                    'id' => $item->user->id,
                    'name' => $item->user->firstname . ' ' . $item->user->lastname
                ],
                'images' => $item->images->map(function ($image) {
                    return [
                        'id' => $image->id,
                        'url' => $image->url
                    ];
                })
            ];
        });

        return response()->json([
            'items' => $transformedItems,
            'total' => $totalItems,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage
        ]);
    }

    public function destroy(int $id): \Illuminate\Http\RedirectResponse
    {
        $item = Item::query()->findOrFail($id);
        $item->delete();

        return redirect()->route('items.index')->with('success', 'Item deleted successfully');
    }

    public function edit($id): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $item = Item::query()->findOrFail($id);
        $categories = Category::all();
        $brands = Brand::all();
        $stores = Store::all();
        $users = User::all();

        return view('backoffice.items.edit', compact('item', 'categories', 'brands', 'stores', 'users'));
    }

    public function updateBo(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'condition' => 'required|string',
            'brand_id' => 'required|integer|exists:brand,id',
            'category_id' => 'required|integer|exists:category,id',
            'category_item_details' => 'nullable|array',
            'category_item_details.*.id' => 'nullable|integer|exists:category_item_detail,id',
            'category_item_details.*.value' => 'nullable|string',
            'images' => 'nullable|array',

            'images.*' => 'mimes:jpeg,png,jpg,gif,svg,heif',

        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Update the item
            $item = Item::query()->findOrFail($id);
            $item->update($validator->validated());

            // Remove old category item details if any
            CategoryItemDetail::query()->where('item_id', $item->id)->delete();




            if ($request->has('category_item_details')) {
                foreach ($request->input('category_item_details') as $detail) {
                    CategoryItemDetail::updateOrCreate(
                        ['id' => $detail['id'],
                        'label_en' => $detail['label_en'],
                        'label_ar' => $detail['label_ar'],
                        'label_fr' => $detail['label_fr'],
                        'item_id' => $item->id],
                        ['value' => $detail['value']]
                    );
                }
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                // Remove old images if necessary
                ItemMedia::query()->where('item_id', $item->id)->delete();
                Media::query()->whereIn('id', ItemMedia::query()->where('item_id', $item->id)->pluck('media_id'))->delete();

                foreach ($request->file('images') as $image) {
                    // Use the ImageOptimizationService to optimize and store the image
                    $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'item_images');


                    
                    $media = Media::query()->create([
                        'path' => $optimizedImageData['path'],
                        'url' => $optimizedImageData['url'],
                    ]);

                    ItemMedia::query()->create([
                        'item_id' => $item->id,
                        'media_id' => $media->id,
                    ]);
                }
            }

            // Commit the transaction
            DB::commit();

            return redirect()->route('items.show', $item->id)->with('success', 'Item updated successfully');
        } catch (\Exception $e) {
            // Rollback the transaction if something went wrong
            DB::rollback();
            Log::error('Error updating item: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to update item');
        }
    }

    /**
     * @OA\Get(
     *     path="/api/user-items",
     *     tags={"Item"},
     *     summary="Get user items",
     *     description="Retrieve a list of items created by the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(property="status", type="string", example="APPROVED")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized")
     * )
     */
    public function getUserItems(Request $request): JsonResponse
    {
        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user();
        Log::info("User: " . json_encode($user));

        // Fetch items created by the authenticated user
        $items = Item::query()->where('user_id', $user->id)->get();

        return response()->json($items, 200);
    }

    /**
     * @OA\Delete(
     *     path="/api/items/{item}",
     *     tags={"Item"},
     *     summary="Delete an item",
     *     description="Delete an item created by the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="item",
     *         in="path",
     *         required=true,
     *         description="The item's ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Successful operation"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Item not found")
     * )
     */
    public function destroyItem(int $id): JsonResponse
    {
        try {
            $item = Item::query()->findOrFail($id);
            $user = auth()->user();

            if ($item->user_id !== $user->id) {
                return response()->json(['error' => 'Unauthorized'], 401);
            }

            $item->delete();

            return response()->json(['message' => 'Item deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Item not found'], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/items/{item}",
     *     tags={"Item"},
     *     summary="Update an item",
     *     description="Update an item with the provided details.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="item",
     *         in="path",
     *         required=true,
     *         description="The item's ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 type="object",
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="description", type="string", example="This is a sample item."),
     *                 @OA\Property(property="price", type="number", format="float", example=99.99),
     *                 @OA\Property(property="condition", type="string", example="new"),
     *                 @OA\Property(property="brand_id", type="integer", example=1),
     *                 @OA\Property(property="category_id", type="integer", example=1),
     *                 @OA\Property(property="category_item_details", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="value", type="string", example="Sample value")
     *                     )
     *                 ),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(type="string", format="binary")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=200, description="Successful operation"),
     *     @OA\Response(response=400, description="Validation error"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=500, description="Failed to update item")
     * )
     */
    public function updateItem(Request $request): JsonResponse
    {


        Log::info('in request update ' . json_encode($request->all()));

        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'description_ar' => 'nullable|string',
            'description' => 'required|string',
            'price' => 'required|numeric',
            'condition' => 'required|string',
            'brand_id' => 'required|integer|exists:brand,id',
            'category_id' => 'required|integer|exists:category,id',
            'category_item_details' => 'nullable|array',
            'category_item_details.*.id' => 'nullable|integer|exists:category_item_detail,id',
            'category_item_details.*.value' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'mimes:jpeg,png,jpg,gif,svg,heif',
            'delete_image_ids' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            $item = Item::find($request->id);

            // Store the old item details before updating
            $oldItem = Item::with(['categoryDetails', 'images'])->find($item->id);
            $oldItemData = $oldItem->toArray();

            // Create an item history record
            $itemHistory = ItemHistory::create([
                'item_id' => $item->id,
                'old_data' => json_encode($oldItemData),
                'user_id' => $user->id,
                'updated_at' => now(),
            ]);

            // Update the item
            $item->update($validator->validated());



            $item->status = eItemStatus::CREATED;

            CategoryItemDetail::where('item_id', $item->id)->delete();


            // If there are additional category item details from the request, insert them
            if ($request->has('category_item_details')) {
                foreach ($request->input('category_item_details') as $detail) {
                    CategoryItemDetail::updateOrCreate(
                        ['id' => $detail['id'],
                        'label_en' => $detail['label_en'],
                        'label_ar' => $detail['label_ar'],
                        'label_fr' => $detail['label_fr'],
                        'item_id' => $item->id],
                        ['value' => $detail['value']]
                    );
                }
            }

            // Handle image deletions if specified
            if ($request->has('delete_image_ids') && !empty($request->input('delete_image_ids'))) {
                $imageIdsToDelete = explode(',', $request->input('delete_image_ids'));
                Log::info('Deleting image IDs: ' . json_encode($imageIdsToDelete));

                foreach ($imageIdsToDelete as $imageId) {
                    if (!empty($imageId)) {
                        $itemMedia = ItemMedia::find($imageId);
                        if ($itemMedia) {
                            $mediaId = $itemMedia->media_id;
                            $itemMedia->delete();

                            // Delete the media file if no other items are using it
                            if (ItemMedia::where('media_id', $mediaId)->count() === 0) {
                                $media = Media::find($mediaId);
                                if ($media) {
                                    // Delete the file from storage
                                    $path = str_replace('/storage', 'public', $media->url);
                                    if (Storage::exists($path)) {
                                        Storage::delete($path);
                                    }
                                    $media->delete();
                                }
                            }
                        }
                    }
                }
            }


            if ($request->has('image_order_updated') && $request->input('image_order_updated') === 'true') {
                Log::info("in image_order_updated");
                if ($request->has('image_orders')) {
                    // Decode the JSON string to an array
                    $imageOrders = json_decode($request->input('image_orders'), true);

                    if (is_array($imageOrders)) {
                        foreach ($imageOrders as $imageId => $order) {
                            $itemMedia = ItemMedia::where('media_id', (int)$imageId)
                                                ->where('item_id', $item->id)
                                                ->first();
                            if ($itemMedia) {
                                $itemMedia->update(['order' => (int)$order]);
                            }
                        }
                    } else {
                        Log::warning('Invalid image_orders format: ' . $request->input('image_orders'));
                    }
                }

                Log::info('Processing image orders for item ' . $item->id);
                Log::info('Image orders: ' . $request->input('image_orders'));
            }


            // Handle new image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    // Use the ImageOptimizationService to optimize and store the image
                    $optimizedImageData = $this->imageOptimizationService->optimizeAndStore($image, 'item_images');

                    $media = Media::query()->create([
                        'path' => $optimizedImageData['path'],
                        'url' => $optimizedImageData['url'],
                    ]);

                    ItemMedia::query()->create([
                        'item_id' => $item->id,
                        'media_id' => $media->id,
                    ]);
                }
            }

            // Commit the transaction
            DB::commit();

            return response()->json($item, 200);
        } catch (\Exception $e) {
            // Rollback the transaction if something went wrong
            DB::rollback();
            Log::error('Failed to update item: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update item: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/user-items-status",
     *     tags={"Item"},
     *     summary="Get user items statuses",
     *     description="Retrieve a list of items created by the authenticated user with their status, first image, updated status date, and whether the owner has updated the item.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Sample Item"),
     *                 @OA\Property(property="status", type="string", example="approved"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-07-25T15:32:07Z"),
     *                 @OA\Property(property="is_promoted", type="boolean", example=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-07-25T15:32:07Z"),
     *                 @OA\Property(property="images", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="url", type="string", example="http://example.com/image.jpg")
     *                     )
     *                 ),
     *                 @OA\Property(property="status_message", type="string", example="Approved at 2024-07-25 15:32:07"),
     *                 @OA\Property(property="owner_updated", type="boolean", example=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized")
     * )
     */
    public function getUserItemsStatuses(Request $request): JsonResponse
    {
        // Log request headers
        $headers = $request->headers->all();
        Log::info("Headers: " . json_encode($headers));

        $user = auth()->user();
        Log::info("User: " . json_encode($user));

        $items = Item::query()->where('user_id', $user->id)
            ->select('id', 'title', 'title_ar', 'description', 'description_ar', 'status', 'updated_at', 'is_promoted', 'created_at', 'rejection_reason')
            ->with(['images', 'payments'])
            ->get();

        // Add custom messages for status changes and owner updated flag
        $items->each(function ($item) {
            $item->status_message = ucfirst($item->status) . ' at ' . $item->updated_at->format('Y-m-d H:i:s');
            if ($item->status === 'approved') {
                $item->status_message = 'Item approved at ' . $item->updated_at->format('Y-m-d H:i:s');
            }
            $item->owner_updated = $item->updated_at != $item->created_at;
            $item->is_paid = $item->payments->isNotEmpty();

            $categoryPrice = CategoryPrice::firstWhere('category_id', $item->category_id)->price ?? '50.0';

            $item->category_price = $categoryPrice;

            // override images
            $item->images = $item->images->map(function ($image) {
                $order = ItemMedia::where('item_id', $image->id)
                              ->where('media_id', $image->item_media_id)
                              ->value('order') ?? 0;
                return [
                    'id' => $image->id,
                    'url' => $image->url,
                    'order' => $order,
                ];
            });

            $item->images = $item->images->sortBy('order')->values();
        });

        return response()->json($items, 200);
    }

    /**
     * @OA\Get(
     *     path="/api/items/{id}",
     *     tags={"Item"},
     *     summary="Get item",
     *     description="Retrieve an item by ID.",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="The item's ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="title", type="string", example="Sample Item"),
     *             @OA\Property(property="description", type="string", example="This is a sample item."),
     *             @OA\Property(property="price", type="number", format="float", example=99.99),
     *             @OA\Property(property="is_promoted", type="boolean", example=true),
     *             @OA\Property(property="status", type="string", example="APPROVED"),
     *             @OA\Property(property="brand", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Brand Name")
     *             ),
     *             @OA\Property(property="category", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Category Title")
     *             ),
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="User Name")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Item not found")
     * )
     */
    public function getItem(int $id): JsonResponse
    {
        try {
            $item = Item::query()->with([ 'categoryDetails', 'brand', 'category', 'user', 'images'])->findOrFail($id);
            // Transform images to include both ID and URL
            $transformedImages = [];
            foreach ($item->images as $image) {


                Log::info("itemMedia: " . json_encode($image));
                $transformedImages[] = [
                    'id' => $image->id, // Use the item_media_id from the images relationship
                    'url' => $image->url,
                    'order' => ItemMedia::where('item_id', $item->id)->where('media_id', $image->item_media_id)->value('order'),
                ];
            }



            usort($transformedImages, function($a, $b) {
                return $a['order'] <=> $b['order'];
            });

            $item->transformed_images = $transformedImages;

            return response()->json($item, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Item not found: ' . $e->getMessage()], 404);
        }
    }

    /**
     * Update an item in the authenticated user's store.
     *
     * @OA\Put(
     *     path="/api/stores/my-store/items/{itemId}",
     *     tags={"Store"},
     *     summary="Update an item in the authenticated user's store",
     *     description="Update an item in the authenticated user's store by item ID.",
     *     operationId="updateItemInMyStore",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="itemId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer"),
     *         description="Item ID"
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="title", type="string"),
     *             @OA\Property(property="price", type="number", format="float"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="condition", type="string"),
     *             @OA\Property(property="quantity", type="integer"),
     *             @OA\Property(property="brand_id", type="integer"),
     *             @OA\Property(property="category_id", type="integer")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Item updated successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Item updated successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Item not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function updateItemInMyStore(Request $request, $itemId): JsonResponse
    {
        $user = Auth::user();
        $store = $user->store;

        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $item = $store->items()->find($itemId);

        if (!$item) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $this->update($request, $item);

        return response()->json(['message' => 'Item updated successfully'], 200);
    }

    /**
     * @OA\Delete(
     *     path="/api/search-history/{id}",
     *     tags={"Search"},
     *     summary="Delete search history",
     *     description="Delete a search history record by ID.",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="The search history ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Successful operation"),
     *     @OA\Response(response=404, description="Search history not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function deleteSearchHistory(int $id): JsonResponse
    {
        try {
            $searchHistory = SearchHistory::query()->findOrFail($id);
            $searchHistory->delete();

            return response()->json(['message' => 'Search history deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Search history not found'], 404);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/public/items/search",
     *     tags={"Item"},
     *     summary="Public search for items",
     *     description="Search for items based on a query string without authentication.",
     *     @OA\Parameter(
     *         name="query",
     *         in="query",
     *         required=true,
     *         description="The search query string",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/Item")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Bad Request"
     *     )
     * )
     */
    public function publicSearch(Request $request)
    {
        $query = $request->input('query');

        if (empty($query)) {
            return response()->json(['error' => 'Query parameter is required'], 400);
        }

        $items = Item::search($query)->with('brand')->get();

        foreach ($items as $item) {
            $category = $item->category;
            if ($category) {
                $category->increment('search_count');
            }
        }

        return response()->json($items, 200);
    }

   /**
     * @OA\Post(
     *     path="/items/add-payment",
     *     summary="Add a payment",
     *     description="Allows a user to add a payment with an uploaded image",
     *     operationId="addPayment",
     *     tags={"Payments"},
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"user_id", "provider_id", "amount", "payment_type", "payment_image"},
     *                 @OA\Property(property="user_id", type="integer", description="User ID"),
     *                 @OA\Property(property="provider_id", type="integer", description="Provider ID"),
     *                 @OA\Property(property="amount", type="number", format="float", description="Payment amount"),
     *                 @OA\Property(property="payment_type", type="string", description="Type of payment (e.g., 'photo')"),
     *                 @OA\Property(
     *                     property="payment_image",
     *                     type="string",
     *                     format="binary",
     *                     description="Payment proof image (JPEG, PNG, JPG, GIF, max 2MB)"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Payment added successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Payment added successfully"),
     *             @OA\Property(
     *                 property="payment",
     *                 type="object",
     *                 @OA\Property(property="user_id", type="integer", example=1),
     *                 @OA\Property(property="provider_id", type="integer", example=2),
     *                 @OA\Property(property="amount", type="number", format="float", example=150.75),
     *                 @OA\Property(property="payment_type", type="string", example="photo"),
     *                 @OA\Property(property="payment_image", type="string", example="payments_screen_shots/image123.jpg"),
     *                 @OA\Property(property="status", type="string", example="pending")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation errors",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="object", example={"user_id": {"The user_id field is required."}})
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Internal Server Error",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Failed to process payment")
     *         )
     *     )
     * )
     */
    public function addPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'item_id' => 'required',
            'provider_id' => 'nullable',
            'amount' => 'required|numeric|min:0',
            'payment_type' => 'required|string',
            'payment_image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'promo_code' => 'nullable|string',
        ]);

        Log::info('validator errors ' . json_encode($validator->errors()));
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            DB::beginTransaction();

            // Handle promo code if provided
            if ($request->has('promo_code')) {
                $promoCode = PromoCode::where('code', $request->promo_code)
                    ->where('is_active', true)
                    ->where(function ($query) {
                        $query->where('expires_at', '>', now())
                            ->orWhereNull('expires_at');
                    })
                    ->first();

                if (!$promoCode) {
                    return response()->json(['message' => 'Invalid or expired promo code'], 422);
                }

                // Check usage limit
                if ($promoCode->limit_usages && $promoCode->times_used >= $promoCode->limit_usages) {
                    return response()->json(['message' => 'Promo code usage limit reached'], 422);
                }

                // Increment the usage counter
                $promoCode->increment('times_used');
            }

            if ($request->payment_type == 'photo') {
                // Use the ImageOptimizationService to optimize and store the payment image
                $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                    $request->file('payment_image'),
                    'payments_screen_shots'
                );

                $payment = ItemPayment::create([
                    'item_id' => $request->item_id,
                    'provider_id' => $request->provider_id,
                    'amount' => $request->amount,
                    'type' => $request->payment_type,
                    'photo_url' => $optimizedImageData['path'],
                    'promo_code' => $request->promo_code,  // Add this line to store the promo code
                ]);
            }

            DB::commit();

            return response()->json(['message' => 'Payment added successfully', 'payment' => $payment], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment creation failed', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Failed to process payment'], 500);
        }
    }
}
