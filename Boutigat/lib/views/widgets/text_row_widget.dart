import 'package:flutter/material.dart';
import '/constants/app_colors.dart';


class TextRow extends StatelessWidget {
  final String label;
  final String value;
  const TextRow({
    super.key,
    required this.label,
    required this.value,
    
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: const TextStyle(fontSize: AppTextSizes.subtitle, fontWeight: AppFontWeights.bold)),
        Text(value, style: const TextStyle(fontSize: AppTextSizes.subtitle, fontWeight : AppFontWeights.bold, color: AppColors.disabled)),
      ],
    );
  }
}