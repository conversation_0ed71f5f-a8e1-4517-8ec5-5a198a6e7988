@extends('backoffice.layouts.layout')

@section('title', 'Edit Advertisement')

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            
            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">Edit Advertisement</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('ads.index') }}">Advertisements</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="ri-edit-line me-2"></i>
                                Edit Advertisement: {{ $ad->title }}
                            </h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('ads.update', $ad->id) }}" method="POST" enctype="multipart/form-data" id="editAdForm">
                                @csrf
                                @method('PUT')
                                
                                <div class="row">
                                    <!-- Title -->
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $ad->title) }}" 
                                                   placeholder="Enter advertisement title" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Current Image Display -->
                                    @if($ad->image_url)
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label class="form-label">Current Image</label>
                                            <div class="mt-2">
                                                <img src="{{ $ad->image_url }}"
                                                     alt="Current Ad Image" class="img-thumbnail" style="max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Image Upload -->
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">
                                                {{ $ad->image_url ? 'Replace Image (Optional)' : 'Advertisement Image' }}
                                                @if(!$ad->image_url) <span class="text-danger">*</span> @endif
                                            </label>
                                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                                   id="image" name="image" accept="image/*" {{ !$ad->image_url ? 'required' : '' }}>
                                            <div class="form-text">Supported formats: JPEG, PNG, JPG, GIF. Max size: 2MB</div>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="mt-2" id="imagePreview" style="display: none;">
                                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-height: 200px;">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Target Type -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="target_type" class="form-label">Target Type <span class="text-danger">*</span></label>
                                            <select class="form-select @error('target_type') is-invalid @enderror"
                                                    id="target_type" name="target_type" required>
                                                <option value="">Select Target Type</option>
                                                <option value="item" {{ old('target_type', $ad->target_type) == 'item' ? 'selected' : '' }}>Item</option>
                                                <option value="store" {{ old('target_type', $ad->target_type) == 'store' ? 'selected' : '' }}>Store</option>
                                                <option value="item-store" {{ old('target_type', $ad->target_type) == 'item-store' ? 'selected' : '' }}>Store Items</option>
                                                <option value="url" {{ old('target_type', $ad->target_type) == 'url' ? 'selected' : '' }}>External URL</option>
                                            </select>
                                            @error('target_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Target Item -->
                                    <div class="col-md-6" id="target_item_div" style="display: none;">
                                        <div class="mb-3">
                                            <label for="target_id_item" class="form-label">Target Item</label>
                                            <select class="form-select @error('target_id') is-invalid @enderror"
                                                    id="target_id_item" name="target_id_item">
                                                <option value="">Select Item</option>
                                                @foreach($items as $item)
                                                    <option value="{{ $item->id }}"
                                                        {{ old('target_id_item', $ad->target_type === 'item' ? $ad->target_id : '') == $item->id ? 'selected' : '' }}>
                                                        {{ $item->title }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Target Store -->
                                    <div class="col-md-6" id="target_store_div" style="display: none;">
                                        <div class="mb-3">
                                            <label for="target_id_store" class="form-label">Target Store</label>
                                            <select class="form-select @error('target_id') is-invalid @enderror"
                                                    id="target_id_store" name="target_id_store">
                                                <option value="">Select Store</option>
                                                @foreach($stores as $store)
                                                    <option value="{{ $store->id }}"
                                                        {{ old('target_id_store', $ad->target_type === 'store' ? $ad->target_id : '') == $store->id ? 'selected' : '' }}>
                                                        {{ $store->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Target URL -->
                                    <div class="col-md-6" id="target_url_div" style="display: none;">
                                        <div class="mb-3">
                                            <label for="target_url" class="form-label">Target URL</label>
                                            <input type="url" class="form-control @error('target_url') is-invalid @enderror"
                                                   id="target_url" name="target_url" value="{{ old('target_url', $ad->target_url) }}"
                                                   placeholder="https://example.com">
                                            @error('target_url')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Target Store for Item-Store -->
                                    <div class="col-md-6" id="target_item_store_div" style="display: none;">
                                        <div class="mb-3">
                                            <label for="target_store_id" class="form-label">Target Store <span class="text-danger">*</span></label>
                                            <select class="form-select @error('target_store_id') is-invalid @enderror"
                                                    id="target_store_id" name="target_store_id">
                                                <option value="">Select Store</option>
                                                @foreach($stores as $store)
                                                    <option value="{{ $store->id }}"
                                                        {{ old('target_store_id', $ad->target_type === 'item-store' ? $ad->store_id : '') == $store->id ? 'selected' : '' }}>
                                                        {{ $store->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('target_store_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Target Item from Store -->
                                    <div class="col-md-6" id="target_store_item_div" style="display: none;">
                                        <div class="mb-3">
                                            <label for="target_store_item_id" class="form-label">Specific Item (Optional)</label>
                                            <select class="form-select" id="target_store_item_id" name="target_store_item_id">
                                                <option value="">All Items from Store</option>
                                                @if($ad->target_type === 'item-store' && $ad->target_id)
                                                    <option value="{{ $ad->target_id }}" selected>{{ $ad->item ? $ad->item->title : 'Selected Item' }}</option>
                                                @endif
                                            </select>
                                            <div class="form-text">Leave empty to show all items from the selected store</div>
                                        </div>
                                    </div>

                                    <!-- Start Date -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                                   id="start_date" name="start_date" 
                                                   value="{{ old('start_date', $ad->start_date ? $ad->start_date->format('Y-m-d') : '') }}">
                                            @error('start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- End Date -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date" class="form-control @error('end_date') is-invalid @enderror" 
                                                   id="end_date" name="end_date" 
                                                   value="{{ old('end_date', $ad->end_date ? $ad->end_date->format('Y-m-d') : '') }}">
                                            @error('end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Store ID (for store-created ads) -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="store_id" class="form-label">Created by Store (Optional)</label>
                                            <select class="form-select @error('store_id') is-invalid @enderror" 
                                                    id="store_id" name="store_id">
                                                <option value="">Admin Created</option>
                                                @foreach($stores as $store)
                                                    <option value="{{ $store->id }}" 
                                                        {{ old('store_id', $ad->store_id) == $store->id ? 'selected' : '' }}>
                                                        {{ $store->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('store_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <!-- Active Status -->
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is_active" 
                                                       name="is_active" {{ old('is_active', $ad->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Active Advertisement
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-end gap-2">
                                            <a href="{{ route('ads.index') }}" class="btn btn-secondary">
                                                <i class="ri-arrow-left-line me-1"></i>
                                                Cancel
                                            </a>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="ri-save-line me-1"></i>
                                                Update Advertisement
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
$(document).ready(function() {
    // Handle target type change
    $('#target_type').change(function() {
        var targetType = $(this).val();

        // Hide all target divs
        $('#target_item_div, #target_store_div, #target_url_div, #target_item_store_div, #target_store_item_div').hide();

        // Clear all target inputs
        $('#target_id_item, #target_id_store, #target_url, #target_store_id, #target_store_item_id').val('');

        // Show relevant div based on selection
        if (targetType === 'item') {
            $('#target_item_div').show();
            // Set the current value if editing
            @if($ad->target_type === 'item' && $ad->target_id)
                $('#target_id_item').val('{{ $ad->target_id }}');
            @endif
        } else if (targetType === 'store') {
            $('#target_store_div').show();
            // Set the current value if editing
            @if($ad->target_type === 'store' && $ad->target_id)
                $('#target_id_store').val('{{ $ad->target_id }}');
            @endif
        } else if (targetType === 'item-store') {
            $('#target_item_store_div').show();
            $('#target_store_item_div').show();
            // Set the current values if editing
            @if($ad->target_type === 'item-store')
                @if($ad->store_id)
                    $('#target_store_id').val('{{ $ad->store_id }}').trigger('change');
                @endif
            @endif
        } else if (targetType === 'url') {
            $('#target_url_div').show();
        }
    });

    // Trigger change event on page load to show correct div
    $('#target_type').trigger('change');

    // Handle store selection for item-store type
    $('#target_store_id').change(function() {
        var storeId = $(this).val();
        var $storeItemSelect = $('#target_store_item_id');

        // Clear existing options except the first one
        $storeItemSelect.html('<option value="">All Items from Store</option>');

        if (storeId) {
            // Load items from selected store
            $.ajax({
                url: '/ads/store-items/' + storeId,
                type: 'GET',
                success: function(items) {
                    $.each(items, function(index, item) {
                        var selected = '';
                        @if($ad->target_type === 'item-store' && $ad->target_id)
                            if (item.id == {{ $ad->target_id }}) {
                                selected = 'selected';
                            }
                        @endif
                        $storeItemSelect.append('<option value="' + item.id + '" ' + selected + '>' + item.title + '</option>');
                    });
                },
                error: function() {
                    alert('Failed to load store items. Please try again.');
                }
            });
        }
    });

    // Image preview
    $('#image').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
            }
            reader.readAsDataURL(file);
        } else {
            $('#imagePreview').hide();
        }
    });

    // Form validation and debugging
    $('#editAdForm').submit(function(e) {
        var targetType = $('#target_type').val();
        var isValid = true;

        // Log form data for debugging
        console.log('Edit form submission data:', {
            ad_id: '{{ $ad->id }}',
            title: $('#title').val(),
            target_type: targetType,
            target_id_item: $('#target_id_item').val(),
            target_id_store: $('#target_id_store').val(),
            target_store_id: $('#target_store_id').val(),
            target_store_item_id: $('#target_store_item_id').val(),
            target_url: $('#target_url').val(),
            is_active: $('#is_active').is(':checked'),
            start_date: $('#start_date').val(),
            end_date: $('#end_date').val(),
            store_id: $('#store_id').val(),
            image: $('#image')[0].files[0] ? $('#image')[0].files[0].name : 'No new file'
        });

        if (targetType === 'item' && !$('#target_id_item').val()) {
            alert('Please select a target item.');
            isValid = false;
        } else if (targetType === 'store' && !$('#target_id_store').val()) {
            alert('Please select a target store.');
            isValid = false;
        } else if (targetType === 'item-store' && !$('#target_store_id').val()) {
            alert('Please select a target store for store items.');
            isValid = false;
        } else if (targetType === 'url' && !$('#target_url').val()) {
            alert('Please enter a target URL.');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
        } else {
            console.log('Edit form validation passed, submitting...');
        }
    });
});
</script>
@endpush
