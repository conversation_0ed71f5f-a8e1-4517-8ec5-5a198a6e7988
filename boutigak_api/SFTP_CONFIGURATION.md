# SFTP Image Configuration

This document outlines the changes made to configure your Laravel application to load **ONLY IMAGES** from the SFTP server at `https://storage.boutigak.com /`. CSS and JS assets remain loaded from the local server.

## Changes Made

### 1. Filesystem Configuration
- **File**: `config/filesystems.php`
- **Changes**:
  - Updated SFTP disk URL from `http://https://storage.boutigak.com/` to `https://storage.boutigak.com /`
  - Kept default filesystem disk as `local` (only images use SFTP)

### 2. Asset Service Provider
- **File**: `app/Providers/AssetServiceProvider.php` (new)
- **Purpose**: Provides macros for generating SFTP URLs
- **Registered in**: `config/app.php`

### 3. Helper Functions
- **File**: `app/helpers.php` (new)
- **Functions**:
  - `sftp_image($path)`: Generate SFTP URL for images only
  - `sftp_storage_url($path)`: Generate SFTP URL for storage files (adds 'storage/' prefix if needed)
  - `sftp_url($path)`: Generate direct SFTP URL without adding storage prefix
  - `image_url($path)`: Smart function that uses SFTP for images when configured
- **Registered in**: `composer.json` autoload files

### 4. Configuration File
- **File**: `config/assets.php` (new)
- **Purpose**: Centralized configuration for SFTP settings
- **Environment variables**:
  - `SFTP_BASE_URL`: Base URL for SFTP server
  - `SFTP_STORAGE_PATH`: Storage path prefix
  - `USE_SFTP_FOR_IMAGES`: Enable/disable SFTP for images only

### 5. Blade Template Updates
Updated the following templates to use SFTP URLs for images only:
- `resources/views/backoffice/items/show.blade.php` (payment screenshots, item images)
- `resources/views/backoffice/items/edit.blade.php` (item images)
- `resources/views/backoffice/stores/show.blade.php` (store images)
- CSS/JS assets in layout files remain using local `asset()` helper

### 6. Model Updates
Updated models to generate SFTP URLs:
- `app/Models/EPaymentProvider.php`: Updated `getLogoUrlAttribute()`
- `app/Models/PaymentProof.php`: Updated `getScreenshotUrlAttribute()`

### 7. Service Updates
- `app/services/ImageOptimizationService.php`: Updated to use SFTP disk

## Environment Configuration

Add these variables to your `.env` file:

```env
# SFTP Image Configuration (Images only, not CSS/JS)
SFTP_BASE_URL=https://storage.boutigak.com 
SFTP_STORAGE_PATH=storage
USE_SFTP_FOR_IMAGES=true
```

## Usage

### In Blade Templates
```php
<!-- For images with smart detection -->
<img src="{{ image_url($image->path) }}" alt="Image">

<!-- For storage files (adds storage/ prefix if needed) -->
<img src="{{ sftp_storage_url($path) }}" alt="Image">

<!-- For direct URLs (no storage/ prefix added) -->
<img src="{{ sftp_url($image->url) }}" alt="Image">

<!-- For CSS/JS assets (remain local) -->
<link href="{{ asset('css/app.css') }}" rel="stylesheet">
```

### In Controllers/Models
```php
// Generate SFTP URL for storage file (adds storage/ prefix if needed)
$imageUrl = sftp_storage_url($imagePath);

// Generate direct SFTP URL (no storage/ prefix added)
$imageUrl = sftp_url($fullImagePath);

// Generate SFTP URL for images
$imageUrl = sftp_image('images/logo.png');

// Smart image URL (uses SFTP if configured)
$imageUrl = image_url($imagePath);
```

## Next Steps

1. **Run Composer Autoload**: `composer dump-autoload`
2. **Clear Config Cache**: `php artisan config:clear`
3. **Test Image Loading**: Verify images load from SFTP server
4. **Update Existing Data**: If needed, update existing image URLs in database

## Notes

- All new images uploaded through the ImageOptimizationService will be stored on SFTP
- Existing Blade templates have been updated to use SFTP URLs
- The configuration is flexible and can be toggled via environment variables
- Helper functions provide a consistent way to generate SFTP URLs throughout the application
