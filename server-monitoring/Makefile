# Server Monitoring Docker Management

.PHONY: help build up down restart logs shell clean test

# Default target
help:
	@echo "Server Monitoring Docker Commands:"
	@echo "=================================="
	@echo "  make build     - Build the Docker images"
	@echo "  make up        - Start the development environment"
	@echo "  make down      - Stop all containers"
	@echo "  make restart   - Restart all containers"
	@echo "  make logs      - Show container logs"
	@echo "  make shell     - Access the app container shell"
	@echo "  make clean     - Clean up containers and volumes"
	@echo "  make test      - Run tests"
	@echo ""
	@echo "Production commands:"
	@echo "  make prod      - Start with Nginx proxy"
	@echo "  make ssl       - Generate self-signed SSL certificates"
	@echo ""
	@echo "Development tools:"
	@echo "  make tools     - Start with Adminer database tool"
	@echo "  make debug     - Start with debug mode enabled"
	@echo ""
	@echo "Access URLs:"
	@echo "  App:           http://localhost:8080"
	@echo "  Nginx:         http://localhost (with prod profile)"
	@echo "  Adminer:       http://localhost:8081 (with tools profile)"
	@echo "  Debug:         http://localhost:8080/debug.php"

# Build Docker images
build:
	@echo "🔨 Building Docker images..."
	docker-compose build --no-cache

# Start development environment
up:
	@echo "🚀 Starting development environment..."
	docker-compose up -d monitoring-app redis-cache
	@echo "✅ Environment started!"
	@echo "📊 Dashboard: http://localhost:8080"
	@echo "🔍 Debug: http://localhost:8080/debug.php"

# Start with production profile (includes Nginx)
prod:
	@echo "🚀 Starting production environment..."
	docker-compose --profile production up -d
	@echo "✅ Production environment started!"
	@echo "📊 Dashboard: http://localhost"
	@echo "🔒 HTTPS: https://localhost (if SSL configured)"

# Start with tools (includes Adminer)
tools:
	@echo "🛠️ Starting with development tools..."
	docker-compose --profile tools up -d
	@echo "✅ Tools started!"
	@echo "📊 Dashboard: http://localhost:8080"
	@echo "🗄️ Adminer: http://localhost:8081"

# Start with debug mode
debug:
	@echo "🐛 Starting in debug mode..."
	ENVIRONMENT=development docker-compose up -d monitoring-app redis-cache
	@echo "✅ Debug mode started!"
	@echo "🔍 Debug console: http://localhost:8080/debug.php"

# Stop all containers
down:
	@echo "🛑 Stopping all containers..."
	docker-compose down

# Restart containers
restart: down up

# Show logs
logs:
	@echo "📋 Showing container logs..."
	docker-compose logs -f --tail=100

# Access app container shell
shell:
	@echo "🐚 Accessing app container shell..."
	docker-compose exec monitoring-app bash

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup complete!"

# Run tests
test:
	@echo "🧪 Running tests..."
	docker-compose exec monitoring-app php test-postgres.php
	docker-compose exec monitoring-app php test-api.php

# Generate SSL certificates for development
ssl:
	@echo "🔐 Generating self-signed SSL certificates..."
	mkdir -p docker/ssl
	openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
		-keyout docker/ssl/monitoring.key \
		-out docker/ssl/monitoring.crt \
		-subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
	@echo "✅ SSL certificates generated!"

# Create log directories
setup-logs:
	@echo "📁 Creating log directories..."
	mkdir -p docker/logs/apache
	mkdir -p docker/logs/nginx
	chmod 755 docker/logs
	chmod 755 docker/logs/apache
	chmod 755 docker/logs/nginx

# Initialize environment
init: setup-logs
	@echo "🔧 Initializing environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "📝 Created .env file from .env.example"; \
		echo "⚠️  Please update .env with your actual credentials"; \
	fi
	@echo "✅ Environment initialized!"

# Show container status
status:
	@echo "📊 Container status:"
	docker-compose ps

# Show resource usage
stats:
	@echo "📈 Resource usage:"
	docker stats --no-stream

# Backup volumes
backup:
	@echo "💾 Creating backup..."
	docker run --rm -v server-monitoring_redis-data:/data -v $(PWD)/backups:/backup alpine tar czf /backup/redis-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .
	@echo "✅ Backup created in ./backups/"

# Update containers
update:
	@echo "🔄 Updating containers..."
	docker-compose pull
	docker-compose build --pull
	@echo "✅ Containers updated!"

# Development workflow
dev: init build up
	@echo "🎉 Development environment ready!"
	@echo "📊 Dashboard: http://localhost:8080"
	@echo "🔍 Debug: http://localhost:8080/debug.php"
	@echo "📋 Logs: make logs"
