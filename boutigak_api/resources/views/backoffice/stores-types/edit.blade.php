@extends('backoffice.layouts.layout')

@section('title', 'Edit Store Type | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Edit Store Type</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('store-types.update', $storeType->id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label for="name_en" class="form-label">Name (EN)</label>
                                    <input type="text" class="form-control" id="name_en" name="name_en" value="{{ $storeType->name_en }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="name_fr" class="form-label">Name (FR)</label>
                                    <input type="text" class="form-control" id="name_fr" name="name_fr" value="{{ $storeType->name_fr }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="name_ar" class="form-label">Name (AR)</label>
                                    <input type="text" class="form-control" id="name_ar" name="name_ar" value="{{ $storeType->name_ar }}" required>
                                </div>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
