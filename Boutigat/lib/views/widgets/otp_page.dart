import 'dart:async';

import 'package:boutigak/controllers/otp_controller.dart';
import 'package:boutigak/controllers/register_controller.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '/constants/app_colors.dart';
import 'package:flutter/material.dart';


class OtpPage extends StatefulWidget {
  final String phoneNumber;

  const OtpPage({Key? key, required this.phoneNumber}) : super(key: key);

  @override
  _OtpPageState createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage> {
  bool _isResendButtonEnabled = false;
  bool _isVerifyButtonEnabled = false;
  bool _showErrorMessage = false;
  Timer? _timer;
  int _secondsRemaining = 10;

  final List<String> _otpValues = List.filled(6, '');
  // Remplacez par votre propre OtpController
  final OtpController _controller = Get.put(OtpController());

  @override
  void initState() {
    super.initState();
    _startResendCooldown();
  }

  void _startResendCooldown() {
    setState(() {
      _isResendButtonEnabled = false;
      _secondsRemaining = 10;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsRemaining > 0) {
        setState(() {
          _secondsRemaining--;
        });
      } else {
        setState(() {
          _isResendButtonEnabled = true;
        });
        timer.cancel();
      }
    });
  }
//27480890
  void _checkOtpCompletion(List<String> otpValues) {
    setState(() {
      _isVerifyButtonEnabled = otpValues.every((digit) => digit.isNotEmpty);
      _otpValues.setAll(0, otpValues);
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _onVerify() async {
    final otpCode = _otpValues.join();
    final isVerified = await _controller.verifyOtp(widget.phoneNumber, otpCode);
    if (isVerified) {
      Get.offAll(() => const NavigationBarPage());
    } else {
      setState(() {
        _showErrorMessage = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Phone Number Verification",
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              children: [
                const Text(
                  "We have sent an SMS to ",
                  style: TextStyle(fontSize: 16),
                ),
                Text(
                  "+222(${widget.phoneNumber})",
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    // Remplacez par votre couleur AppColors.primary
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            const Text(
              "Enter the 6-digit code",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 10),
            OtpForm(
              onOtpChanged: (otpValues) {
                _checkOtpCompletion(otpValues);
              },
            ),
            if (_showErrorMessage)
              const Padding(
                padding: EdgeInsets.only(top: 8.0),
                child: Text(
                  "The code is incorrect or incomplete.",
                  // Remplacez par votre couleur AppColors.error
                  style: TextStyle(color: Colors.red),
                ),
              ),
            const Spacer(),
            Row(
              children: [
                // const Spacer(),
                // ElevatedButton(
                //   onPressed: _isResendButtonEnabled
                //       ? () async {
                //           _startResendCooldown();
                //           await _controller.sendOtp(widget.phoneNumber);
                //         }
                //       : null,
                //   style: ElevatedButton.styleFrom(
                //     // Remplacez par votre couleur AppColors.onSurface
                //     backgroundColor: Colors.grey,
                //     shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(16),
                //     ),
                //     padding: const EdgeInsets.symmetric(
                //       horizontal: 42,
                //       vertical: 16,
                //     ),
                //   ),
                //   child: Text(
                //     _isResendButtonEnabled
                //         ? "Resend"
                //         : "Resend in $_secondsRemaining s",
                //     style: const TextStyle(
                //       fontSize: 16,
                //       fontWeight: FontWeight.bold,
                //       color: Colors.white,
                //     ),
                //   ),
                // ),
                // const Spacer(),
                ElevatedButton(
                  onPressed: _isVerifyButtonEnabled ? _onVerify : null,
                  style: ElevatedButton.styleFrom(
                    // Remplacez par votre couleur AppColors.primary
                    backgroundColor: AppColors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 42,
                      vertical: 16,
                    ),
                  ),
                  child: const Text(
                    "Verify",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 26),
          ],
        ),
      ),
    );
  }
}

class OtpForm extends StatefulWidget {
  final Function(List<String>) onOtpChanged;

  const OtpForm({Key? key, required this.onOtpChanged}) : super(key: key);

  @override
  _OtpFormState createState() => _OtpFormState();
}

class _OtpFormState extends State<OtpForm> {
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());
  final List<TextEditingController> _controllers =
      List.generate(6, (index) => TextEditingController());

  @override
  void initState() {
    super.initState();

    for (int i = 0; i < 6; i++) {
      _controllers[i].addListener(() {
        widget.onOtpChanged(
          _controllers.map((c) => c.text).toList(),
        );
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  /// Méthode déclenchée à chaque changement dans un champ
  void _onChanged(String value, int index) {
    // Si l'utilisateur ou iOS a collé/inséré plusieurs chiffres d'un coup :
    if (value.length > 1) {
      final characters = value.split('');
      for (int i = 0; i < characters.length; i++) {
        final pos = index + i;
        if (pos < 6) {
          _controllers[pos].text = characters[i];
        }
      }
      // Place le focus sur le prochain champ libre ou sur le dernier
      final nextPos = index + value.length;
      if (nextPos < 6) {
        FocusScope.of(context).requestFocus(_focusNodes[nextPos]);
      } else {
        FocusScope.of(context).unfocus();
      }
    } else {
      // Cas normal : un seul caractère
      if (value.isNotEmpty && index < 5) {
        FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
      } 
      // Si on supprime le chiffre (value.isEmpty) on se replace sur le champ précédent
      else if (value.isEmpty && index > 0) {
        FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
      }
    }
    // Notifie le parent
    widget.onOtpChanged(_controllers.map((c) => c.text).toList());
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(6, (index) {
          return SizedBox(
            width: 52,
            height: 64,
            child: TextFormField(
              controller: _controllers[index],
              focusNode: _focusNodes[index],
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              // Tentez de mettre visiblePassword si le oneTimeCode ne fonctionne pas
              keyboardType: TextInputType.number,
              textInputAction:
                  index < 5 ? TextInputAction.next : TextInputAction.done,
              autofillHints: const [AutofillHints.oneTimeCode],
              inputFormatters: [
                LengthLimitingTextInputFormatter(6), 
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) => _onChanged(value, index),
              decoration: InputDecoration(
                counterText: "",
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  // Remplacez par votre couleur de bordure inactive
                  borderSide: const BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  // Remplacez par votre couleur AppColors.primary
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
