version: '3.8'

services:
  # Server Monitoring Application
  monitoring-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: server-monitoring-app
    ports:
      - "8080:80" 
    volumes:
      - './:/var/www/html'
      - ./docker/logs:/var/log/monitoring:rw
      - ./docker/logs/apache:/var/log/apache2:rw
    environment:
      # Development environment
      - ENVIRONMENT=development
      - PHP_DISPLAY_ERRORS=1
      - PHP_ERROR_REPORTING=E_ALL
      # Database configuration
      - DB_HOST=************
      - DB_PORT=5432
      - DB_NAME=boutigak_db
      - DB_USERNAME=postgress
      - DB_PASSWORD=adminboutigak
      # SSH configuration (update with your credentials)
      - SSH_HOST=************
      - SSH_PORT=22
      - SSH_USERNAME=your_ssh_username
      - SSH_PASSWORD=your_ssh_password
    networks:
      - monitoring-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/database.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis-cache

  # Redis for caching (optional but recommended)
  redis-cache:
    image: redis:7-alpine
    container_name: server-monitoring-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - monitoring-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional, for production-like setup)
  nginx-proxy:
    image: nginx:alpine
    container_name: server-monitoring-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx-sites:/etc/nginx/conf.d:ro
      - ./docker/logs/nginx:/var/log/nginx:rw
      # SSL certificates (create self-signed for development)
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - monitoring-app
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - production  # Only start with: docker-compose --profile production up

  # PostgreSQL for testing (optional - you already have external DB)
  postgres-test:
    image: postgres:15
    container_name: server-monitoring-postgres-test
    ports:
      - "5433:5432"  # Different port to avoid conflict
    environment:
      POSTGRES_DB: monitoring_test
      POSTGRES_USER: monitoring
      POSTGRES_PASSWORD: monitoring123
    volumes:
      - postgres-test-data:/var/lib/postgresql/data
      - ./docker/postgres-init:/docker-entrypoint-initdb.d:ro
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - testing  # Only start with: docker-compose --profile testing up

  # Adminer for database management (optional)
  adminer:
    image: adminer:latest
    container_name: server-monitoring-adminer
    ports:
      - "8081:8080"  # Access via http://localhost:8081
    environment:
      ADMINER_DEFAULT_SERVER: ************:5432
      ADMINER_DESIGN: pepa-linha
    networks:
      - monitoring-network
    restart: unless-stopped
    profiles:
      - tools  # Only start with: docker-compose --profile tools up

volumes:
  redis-data:
    driver: local
  postgres-test-data:
    driver: local

networks:
  monitoring-network:
    driver: bridge
