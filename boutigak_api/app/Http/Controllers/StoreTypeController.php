<?php

namespace App\Http\Controllers;

use App\Models\StoreType;
use Exception;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;

class StoreTypeController extends Controller
{
    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $data = StoreType::all();
            return DataTables::of($data)
                ->addColumn('actions', function ($row) {
                    $btn = '<a href="' . route('store-types.edit', $row->id) . '" class="edit btn btn-warning btn-sm me-1">Edit</a>';
                    $btn .= '<button type="button" class="delete btn btn-danger btn-sm" data-id="' . $row->id . '">
                <i class="fa fa-trash"></i> Delete
             </button>';
                    return $btn;
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('backoffice.stores-types.index');
    }

    public function create()
    {
        return view('backoffice.stores-types.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_fr' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
        ]);

        StoreType::create($request->all());
        return redirect()->route('store-types.index')->with('success', 'Store Type created successfully.');
    }

    public function edit(StoreType $storeType)
    {
        return view('backoffice.stores-types.edit', compact('storeType'));
    }

    public function update(Request $request, StoreType $storeType)
    {
        $request->validate([
            'name_en' => 'required|string|max:255',
            'name_fr' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
        ]);

        $storeType->update($request->all());
        return redirect()->route('store-types.index')->with('success', 'Store Type updated successfully.');
    }

    public function destroy(StoreType $storeType)
    {
        $storeType->delete();
        return redirect()->route('store-types.index')->with('success', 'Store Type deleted successfully.');
    }
}
