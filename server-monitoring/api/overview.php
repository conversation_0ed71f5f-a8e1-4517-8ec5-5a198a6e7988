<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Configuration for your servers
$servers = [
    'database' => [
        'host' => '************',
        'port' => 22,
        'username' => 'root',
        'password' => 'Boutigak@36666688@38407840'
    ],
    'app1' => [
        'host' => '**************',
        'port' => 22,
        'username' => 'root',
        'password' => 'Boutigak@36666688@38407840'
    ],
    'app2' => [
        'host' => '************',
        'port' => 22,
        'username' => 'root',
        'password' => 'Boutigak@36666688@38407840'
    ]
];

class ServerMonitor {
    private $servers;

    public function __construct($servers) {
        $this->servers = $servers;
    }

    public function getOverviewData() {
        $data = [
            'activeUsers' => $this->getActiveUsers(),
            'totalSessions' => $this->getTotalSessions(),
            'requestsPerMin' => $this->getRequestsPerMinute(),
            'avgCpu' => $this->getAverageCpuUsage(),
            'avgRam' => $this->getAverageRamUsage(),
            'totalUptime' => $this->getTotalUptime(),
            'timestamp' => time()
        ];

        return $data;
    }

    private function getActiveUsers() {
        try {
            // Connect to your Laravel database to get active users
            $pdo = $this->getDatabaseConnection();

            // Count active users (users with last_activity in last 15 minutes)
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as active_users
                FROM users
                WHERE last_activity > NOW() - INTERVAL '15 minutes'
            ");
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return (int)$result['active_users'];

        } catch (Exception $e) {
            error_log("Error getting active users: " . $e->getMessage());
            return 0; // Return 0 if unable to connect
        }
    }

    private function getTotalSessions() {
        try {
            $pdo = $this->getDatabaseConnection();

            // Count total users who were active today
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as total_sessions
                FROM users
                WHERE DATE(last_activity) = CURRENT_DATE
            ");
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return (int)$result['total_sessions'];

        } catch (Exception $e) {
            error_log("Error getting total sessions: " . $e->getMessage());
            return 0;
        }
    }

    private function getRequestsPerMinute() {
        try {
            // Parse access logs to get requests per minute
            $logFile = '/var/log/nginx/access.log'; // Adjust path as needed

            if (!file_exists($logFile)) {
                return 0;
            }

            $lastMinute = date('d/M/Y:H:i', strtotime('-1 minute'));
            $command = "grep '$lastMinute' $logFile | wc -l";
            $requests = (int)shell_exec($command);

            return $requests;

        } catch (Exception $e) {
            error_log("Error getting requests per minute: " . $e->getMessage());
            return 0;
        }
    }

    private function getAverageCpuUsage() {
        $totalCpu = 0;
        $serverCount = 0;

        foreach ($this->servers as $server) {
            $cpu = $this->getServerCpuUsage($server);
            if ($cpu !== false) {
                $totalCpu += $cpu;
                $serverCount++;
            }
        }

        return $serverCount > 0 ? round($totalCpu / $serverCount, 1) : 0;
    }

    private function getAverageRamUsage() {
        $totalRam = 0;
        $serverCount = 0;

        foreach ($this->servers as $server) {
            $ram = $this->getServerRamUsage($server);
            if ($ram !== false) {
                $totalRam += $ram;
                $serverCount++;
            }
        }

        return $serverCount > 0 ? round($totalRam / $serverCount, 1) : 0;
    }

    private function getTotalUptime() {
        // Calculate average uptime across all servers
        $uptimes = [];

        foreach ($this->servers as $server) {
            $uptime = $this->getServerUptime($server);
            if ($uptime !== false) {
                $uptimes[] = $uptime;
            }
        }

        if (empty($uptimes)) {
            return '0%';
        }

        $avgUptime = array_sum($uptimes) / count($uptimes);
        return round($avgUptime, 1) . '%';
    }

    private function getServerCpuUsage($server) {
        try {
            $command = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'";
            $result = $this->executeRemoteCommand($server, $command);

            if ($result !== false) {
                return (float)trim($result);
            }

            return false;

        } catch (Exception $e) {
            error_log("Error getting CPU usage for server: " . $e->getMessage());
            return false;
        }
    }

    private function getServerRamUsage($server) {
        try {
            $command = "free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'";
            $result = $this->executeRemoteCommand($server, $command);

            if ($result !== false) {
                return (float)trim($result);
            }

            return false;

        } catch (Exception $e) {
            error_log("Error getting RAM usage for server: " . $e->getMessage());
            return false;
        }
    }

    private function getServerUptime($server) {
        try {
            $command = "uptime | awk -F'up ' '{print $2}' | awk -F',' '{print $1}'";
            $result = $this->executeRemoteCommand($server, $command);

            if ($result !== false) {
                // Convert uptime to percentage (assuming 99.9% if server is up)
                return 99.9;
            }

            return false;

        } catch (Exception $e) {
            error_log("Error getting uptime for server: " . $e->getMessage());
            return false;
        }
    }

    private function executeRemoteCommand($server, $command) {
        // For security, you might want to use SSH key authentication instead
        // This is a simplified example

        if (!function_exists('ssh2_connect')) {
            // SSH2 extension not available, return false
            return false;
        }

        try {
            $connection = ssh2_connect($server['host'], $server['port']);

            if (!$connection) {
                return false;
            }

            if (!ssh2_auth_password($connection, $server['username'], $server['password'])) {
                return false;
            }

            $stream = ssh2_exec($connection, $command);
            stream_set_blocking($stream, true);

            $result = stream_get_contents($stream);
            fclose($stream);

            return $result;

        } catch (Exception $e) {
            error_log("SSH connection error: " . $e->getMessage());
            return false;
        }
    }

    private function getDatabaseConnection() {
        // PostgreSQL database credentials
        $host = '************';
        $port = 5432;
        $dbname = 'boutigak_db';
        $username = 'postgress';
        $password = 'adminboutigak';

        $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";

        return new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
    }
}

try {
    $monitor = new ServerMonitor($servers);
    $data = $monitor->getOverviewData();

    echo json_encode($data);

} catch (Exception $e) {
    error_log("Overview API error: " . $e->getMessage());

    echo json_encode([
        'activeUsers' => 0,
        'totalSessions' => 0,
        'requestsPerMin' => 0,
        'avgCpu' => 0,
        'avgRam' => 0,
        'totalUptime' => '0%',
        'timestamp' => time(),
        'error' => 'Connection failed'
    ]);
}
?>
