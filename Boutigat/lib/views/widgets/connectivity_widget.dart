import 'package:boutigak/controllers/connectivity_controlleur.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class ConnectivityPopup {
  static void show(BuildContext context) {
    final connectivityController = Get.find<ConnectivityController>();
    
    if (!connectivityController.isConnected.value) {
      Get.dialog(
        AlertDialog(
          title: Text("Pas de connexion Internet"),
          content: Text("Veuillez vérifier votre connexion et réessayer."),
          actions: [
            TextButton(
              onPressed: () {
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop(); 
                }
              },
              child: Text("OK"),
            ),
          ],
        ),
        barrierDismissible: false,
      );
    }
  }
}