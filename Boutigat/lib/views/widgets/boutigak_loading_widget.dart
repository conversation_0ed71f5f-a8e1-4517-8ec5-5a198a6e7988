import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LottieLoopingLoadingWidget extends StatefulWidget {
  final String animationPath; 
  final double width;
  final double height;

  const LottieLoopingLoadingWidget({
    Key? key,
    required this.animationPath,
    this.width = 300.0,
    this.height = 300.0,
  }) : super(key: key);

  @override
  _LottieLoopingLoadingWidgetState createState() => _LottieLoopingLoadingWidgetState();
}

class _LottieLoopingLoadingWidgetState extends State<LottieLoopingLoadingWidget> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1), 
    )..repeat(min: 0.0, max: 0.4);
  }

  @override
  void dispose() {
    _controller.dispose(); 
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Lottie.asset(
        widget.animationPath,
        width: widget.width,
        height: widget.height,
        controller: _controller,
       delegates: LottieDelegates(
        values: [
          ValueDelegate.color(
             const ['Shape Layer 2', '**'],
            value: AppColors.primary, 
          ),
        ],
      ),
        onLoaded: (composition) {
          _controller.duration = const Duration(seconds: 1); 
        },
      ),
    );
  }
}
