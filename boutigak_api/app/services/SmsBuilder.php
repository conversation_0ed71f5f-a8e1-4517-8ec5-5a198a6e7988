<?php

namespace App\services;

use smpp\{ Address, SMPP, Client as SmppClient, transport\Socket};
use Exception;
use Illuminate\Support\Facades\Log;

class SmsBuilder
{
    const DEFAULT_SENDER = 'Boutigak';
    protected Socket $transport;
    protected SmppClient $smppClient;
    protected $debug = false;
    protected $from;
    protected $to;
    protected $login;
    protected $password;

    public function __construct(
        string $address,
        int $port,
        string $login,
        string $password,
        int $timeout = 5000,
        bool $debug = false
    )
    {
        $this->transport = new Socket([$address], $port);
        $this->transport->setRecvTimeout($timeout);
        $this->smppClient = new SmppClient($this->transport);

        $this->smppClient->debug = $debug;
        $this->transport->debug = $debug;

        $this->login = $login;
        $this->password = $password;

        $this->from = new Address(self::DEFAULT_SENDER, SMPP::TON_ALPHANUMERIC);
    }

    public function setSender($sender, $ton)
    {
        return $this->setAddress($sender, 'from', $ton);
    }

    public function setRecipient($address, $ton)
    {
        return $this->setAddress($address, 'to', $ton);
    }

    protected function setAddress($address, string $type, int $ton = SMPP::TON_UNKNOWN, $npi = SMPP::NPI_UNKNOWN)
    {
        if ($ton === SMPP::TON_INTERNATIONAL) {
            $npi = SMPP::NPI_E164;
        }
        $this->$type = new Address($address, $ton, $npi);
        return $this;
    }

    public function sendMessage(string $message): void
    {
        try {
            // Attempt to open the transport connection
            $this->transport->open();

            // Check if the connection is open
            if (!$this->transport->isOpen()) {
                throw new Exception('Failed to open socket connection to SMPP server.');
            }
            Log::info('SMPP transport connection successfully opened.');

            // Bind as a transceiver
            $this->smppClient->bindTransceiver($this->login, $this->password);
            Log::info('SMPP client successfully bound as transceiver.');

            // Send SMS with UCS2 encoding
            $response = $this->smppClient->sendSMS($this->from, $this->to, $message, null, SMPP::DATA_CODING_UCS2);

            if ($response) {
                Log::info("SMS sent successfully to {$this->to->value}. Response: " . print_r($response, true));
            } else {
                Log::warning("SMPP server returned no valid response for the SMS.");
            }

        } catch (\Exception $e) {
            Log::error("Error in sendMessage: " . $e->getMessage());
            throw new Exception("Failed to send SMS: " . $e->getMessage());
        } finally {
            // Ensure the connection is closed
            if ($this->transport->isOpen()) {
                $this->smppClient->close();
                Log::info('SMPP transport connection closed.');
            }
        }
    }
}
