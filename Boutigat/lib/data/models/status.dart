class Status {
  final String id;
  final String title;
  final String? status;
  final String statusMessage;
  final bool isPromoted;
  final List<String> images;
  final String? price;
  final bool isPaid;
  final String? categoryPrice;
  final String? rejectionReason;
  
  Status({
    required this.id,
    required this.title,
    this.status,
    required this.statusMessage,
    required this.isPromoted,
    required this.images,
    this.price,
    this.isPaid = false,
    this.categoryPrice,
    this.rejectionReason,
  });

  factory Status.fromJson(Map<String, dynamic> json) {
    return Status(
      id: json['id'].toString(),
      title: json['title'] ?? '',
      status: json['status'],
      statusMessage: json['status_message'] ?? '',
      isPromoted: json['is_promoted'] ?? false,
      images: json['images'] != null 
          ? List<String>.from(json['images'].map((image) => image['url'] ?? ''))
          : [],
      price: json['price']?.toString(),
      isPaid: json['is_paid'] ?? false,
      categoryPrice: json['category_price']?.toString(),
      rejectionReason: json['rejection_reason'] ?? '',
    );
  }
}
