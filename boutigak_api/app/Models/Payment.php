<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    protected $table = 'payment';

    protected $fillable = [
        'type_id',
        'amount',
        'order_id',
        'transaction_id',
        'passcode',
        'payer_phone_number',
        
    ];

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class, 'type_id');
    }
}
