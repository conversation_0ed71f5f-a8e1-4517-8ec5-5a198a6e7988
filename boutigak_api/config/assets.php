<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Asset Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for asset URLs and storage settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | SFTP Server Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the SFTP server that hosts images and assets.
    |
    */

    'sftp' => [
        'base_url' => env('SFTP_BASE_URL', 'https://storage.boutigak.com '),
        'storage_path' => env('SFTP_STORAGE_PATH', 'storage'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Image URL Generation
    |--------------------------------------------------------------------------
    |
    | Whether to use SFTP for image URLs only (not CSS/JS assets).
    |
    */

    'use_sftp_for_images' => env('USE_SFTP_FOR_IMAGES', true),

];
