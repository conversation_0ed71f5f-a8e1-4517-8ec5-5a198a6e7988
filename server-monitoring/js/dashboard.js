// Dashboard JavaScript
let charts = {};
let refreshInterval;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    showLoader();
    // initializeCharts();
    refreshData();
    startAutoRefresh();
});

// Show loader
function showLoader() {
    const loader = document.getElementById('loader-overlay');
    if (loader) {
        loader.classList.remove('hidden');
    }
}

// Hide loader
function hideLoader() {
    const loader = document.getElementById('loader-overlay');
    if (loader) {
        loader.classList.add('hidden');
    }
}

// Switch between tabs
function switchTab(tabName, event) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.server-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab and content
    if (event && event.target) {
        event.target.classList.add('active');
    }
    document.getElementById(tabName).classList.add('active');
}

// Initialize charts
function initializeCharts() {
    // Overview chart
    const overviewCtx = document.getElementById('overviewChart').getContext('2d');
    charts.overview = new Chart(overviewCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'CPU Usage (%)',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'RAM Usage (%)',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Active Users',
                    data: [],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Percentage (%)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Users'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
}

// Refresh all data
async function refreshData() {
    try {
        // Show loading state
        const refreshBtn = document.querySelector('.refresh-btn i');
        refreshBtn.classList.add('fa-spin');

        // Fetch data from all servers
        await Promise.all([
            fetchOverviewData(),
            fetchDatabaseData(),
            fetchAppServerData(1),
            fetchAppServerData(2)
        ]);

        // Update last updated time
        document.getElementById('last-updated').textContent = new Date().toLocaleString();

        // Stop loading animation
        refreshBtn.classList.remove('fa-spin');

        // Hide loader
        hideLoader();

    } catch (error) {
        console.error('Error refreshing data:', error);
        showAlert('Error fetching server data', 'danger');
    }
}

// Fetch overview data
async function fetchOverviewData() {
    try {
        // Simulate API call - replace with actual endpoint
        const response = await fetch('api/overview.php');
        const data = await response.json();

        // Update overview metrics

        console.log(data);


        document.getElementById('active-users').textContent = data.activeUsers || 0;
        document.getElementById('total-sessions').textContent = data.totalSessions || 0;
        document.getElementById('requests-per-min').textContent = data.requestsPerMin || 0;
        document.getElementById('avg-cpu').textContent = (data.avgCpu || 0) + '%';
        document.getElementById('avg-ram').textContent = (data.avgRam || 0) + '%';
        document.getElementById('total-uptime').textContent = data.totalUptime || '0%';

        // Update chart
        // updateOverviewChart(data);

    } catch (error) {
        // Use error data if API fails
        console.warn('Error fetching overview data');
        updateWithErrorData();
    }
}

// Fetch database server data
async function fetchDatabaseData() {
    try {
        const response = await fetch('api/database.php');
        const data = await response.json();

        updateServerStatus('db', data.status || 'offline');
        document.getElementById('db-cpu').textContent = (data.cpu || 0) + '%';
        document.getElementById('db-ram').textContent = (data.ram || 0) + '%';
        document.getElementById('db-disk').textContent = (data.disk || 0) + '%';
        document.getElementById('db-connections').textContent = data.connections || 0;
        document.getElementById('db-queries').textContent = data.queries || 0;
        document.getElementById('db-slow-queries').textContent = data.slowQueries || 0;
        document.getElementById('db-size').textContent = data.size || 'Unknown';

        // Update progress bars
        updateProgressBar('db-cpu-bar', data.cpu || 0);
        updateProgressBar('db-ram-bar', data.ram || 0);
        updateProgressBar('db-disk-bar', data.disk || 0);

    } catch (error) {
        console.warn('Error fetching database server data');
        updateDatabaseErrorData();
    }
}

// Fetch app server data
async function fetchAppServerData(serverNum) {
    try {
        // Use mock API temporarily to avoid SSH timeout issues
        const response = await fetch(`api/app-server-${serverNum}-mock.php`);
        const data = await response.json();

        updateServerStatus(`app${serverNum}`, data.status || 'offline');

        // Helper function to safely update element
        function safeUpdate(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }

        // Update system resources
        safeUpdate(`app${serverNum}-cpu`, (data.cpu || 0) + '%');
        safeUpdate(`app${serverNum}-ram`, (data.ram || 0) + '%');
        safeUpdate(`app${serverNum}-disk`, (data.disk || 0) + '%');
        safeUpdate(`app${serverNum}-uptime`, data.uptime || 'Unknown');

        // Update progress bars
        updateProgressBar(`app${serverNum}-cpu-bar`, data.cpu || 0);
        updateProgressBar(`app${serverNum}-ram-bar`, data.ram || 0);
        updateProgressBar(`app${serverNum}-disk-bar`, data.disk || 0);

        // Update application metrics
        safeUpdate(`app${serverNum}-php-processes`, data.phpProcesses || 0);
        safeUpdate(`app${serverNum}-nginx-status`, data.nginxStatus || 'Unknown');
        safeUpdate(`app${serverNum}-laravel-logs`, data.laravelLogs || 0);
        safeUpdate(`app${serverNum}-sessions`, data.sessionCount || 0);

        // Update network & I/O
        const networkRx = data.networkIO ? formatBytes(data.networkIO.rx_bytes) : '0 B';
        const networkTx = data.networkIO ? formatBytes(data.networkIO.tx_bytes) : '0 B';
        const diskRead = data.diskIO ? data.diskIO.read_kb_s + ' KB/s' : '0 KB/s';
        const diskWrite = data.diskIO ? data.diskIO.write_kb_s + ' KB/s' : '0 KB/s';

        safeUpdate(`app${serverNum}-network-rx`, networkRx);
        safeUpdate(`app${serverNum}-network-tx`, networkTx);
        safeUpdate(`app${serverNum}-disk-read`, diskRead);
        safeUpdate(`app${serverNum}-disk-write`, diskWrite);

        // Update performance metrics
        safeUpdate(`app${serverNum}-load-avg`, data.loadAverage || 'Unknown');
        safeUpdate(`app${serverNum}-error-rate`, data.errorRate || '0%');
        safeUpdate(`app${serverNum}-response-time`, data.responseTime || '0ms');

        // Update queue jobs
        let queueText = '0';
        if (data.queueJobs) {
            queueText = `${data.queueJobs.pending || 0} pending, ${data.queueJobs.processing || 0} processing, ${data.queueJobs.failed || 0} failed`;
        }
        safeUpdate(`app${serverNum}-queue-jobs`, queueText);

    } catch (error) {
        console.warn(`Error fetching data for app server ${serverNum}`);
        updateServerStatus(`app${serverNum}`, 'offline');
        updateAppServerErrorData(serverNum);
    }
}

// Update server status
function updateServerStatus(server, status) {
    const statusElement = document.getElementById(`${server}-status`);
    const lastCheckElement = document.getElementById(`${server}-last-check`);
    const statusItem = statusElement.closest('.status-item');

    statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    lastCheckElement.textContent = new Date().toLocaleTimeString();

    // Update status styling
    statusItem.className = 'status-item ' + status;
}

// Update progress bar
function updateProgressBar(barId, percentage) {
    const bar = document.getElementById(barId);
    if (!bar) {
        return; // Element doesn't exist, skip update
    }

    bar.style.width = percentage + '%';

    // Update color based on percentage
    bar.className = 'progress-fill';
    if (percentage > 80) {
        bar.classList.add('danger');
    } else if (percentage > 60) {
        bar.classList.add('warning');
    }
}

// Update overview chart
function updateOverviewChart(data) {
    const chart = charts.overview;
    const now = new Date().toLocaleTimeString();

    // Add new data point
    chart.data.labels.push(now);
    chart.data.datasets[0].data.push(data.avgCpu || 0);
    chart.data.datasets[1].data.push(data.avgRam || 0);
    chart.data.datasets[2].data.push(data.activeUsers || 0);

    // Keep only last 20 data points
    if (chart.data.labels.length > 20) {
        chart.data.labels.shift();
        chart.data.datasets.forEach(dataset => dataset.data.shift());
    }

    chart.update();
}

// Update with error data when API fails
function updateWithErrorData() {
    document.getElementById('active-users').textContent = 0;
    document.getElementById('total-sessions').textContent = 0;
    document.getElementById('requests-per-min').textContent = 0;
    document.getElementById('avg-cpu').textContent = '0%';
    document.getElementById('avg-ram').textContent = '0%';
    document.getElementById('total-uptime').textContent = '0%';

    // updateOverviewChart({});
}

// Update database error data
function updateDatabaseErrorData() {
    document.getElementById('db-cpu').textContent = '0%';
    document.getElementById('db-ram').textContent = '0%';
    document.getElementById('db-disk').textContent = '0%';
    document.getElementById('db-connections').textContent = 0;
    document.getElementById('db-queries').textContent = 0;
    document.getElementById('db-slow-queries').textContent = 0;
    document.getElementById('db-size').textContent = 'Unknown';

    updateProgressBar('db-cpu-bar', 0);
    updateProgressBar('db-ram-bar', 0);
    updateProgressBar('db-disk-bar', 0);

    updateServerStatus('db', 'offline');
}

// Update app server error data
function updateAppServerErrorData(serverNum) {
    // Helper function to safely update element
    function safeUpdate(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    // System resources
    safeUpdate(`app${serverNum}-cpu`, '0%');
    safeUpdate(`app${serverNum}-ram`, '0%');
    safeUpdate(`app${serverNum}-disk`, '0%');
    safeUpdate(`app${serverNum}-uptime`, 'Unknown');

    // Progress bars
    updateProgressBar(`app${serverNum}-cpu-bar`, 0);
    updateProgressBar(`app${serverNum}-ram-bar`, 0);
    updateProgressBar(`app${serverNum}-disk-bar`, 0);

    // Application metrics
    safeUpdate(`app${serverNum}-php-processes`, '0');
    safeUpdate(`app${serverNum}-nginx-status`, 'Unknown');
    safeUpdate(`app${serverNum}-laravel-logs`, '0');
    safeUpdate(`app${serverNum}-sessions`, '0');

    // Network & I/O
    safeUpdate(`app${serverNum}-network-rx`, '0 B');
    safeUpdate(`app${serverNum}-network-tx`, '0 B');
    safeUpdate(`app${serverNum}-disk-read`, '0 KB/s');
    safeUpdate(`app${serverNum}-disk-write`, '0 KB/s');

    // Performance metrics
    safeUpdate(`app${serverNum}-load-avg`, 'Unknown');
    safeUpdate(`app${serverNum}-error-rate`, '0%');
    safeUpdate(`app${serverNum}-response-time`, '0ms');
    safeUpdate(`app${serverNum}-queue-jobs`, '0');
}

// Format bytes to human readable format
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Show alert
function showAlert(message, type = 'info') {
    const alertsContainer = document.getElementById('alerts-container');

    // If alerts container doesn't exist, just log to console
    if (!alertsContainer) {
        console.warn(`Alert (${type}): ${message}`);
        return;
    }

    const alertElement = document.createElement('div');
    alertElement.className = 'metric-row';
    alertElement.innerHTML = `
        <span class="metric-label">${message}</span>
        <span class="metric-value" style="color: ${type === 'danger' ? '#ef4444' : '#f59e0b'};">!</span>
    `;

    alertsContainer.appendChild(alertElement);

    // Remove alert after 5 seconds
    setTimeout(() => {
        if (alertElement.parentElement) {
            alertElement.remove();
        }
    }, 5000);
}

// Start auto refresh
function startAutoRefresh() {
    refreshInterval = setInterval(refreshData, 30000); // Refresh every 30 seconds
}

// Stop auto refresh
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

// Handle page visibility change
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
        refreshData();
    }
});
