<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $table = 'category';

    protected $fillable = [
        'title_en',
        'title_ar',
        'title_fr',
        'parent_id',
        'search_count'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function details()
    {
        return $this->hasMany(CategoryDetail::class, 'category_id');
    }
}
