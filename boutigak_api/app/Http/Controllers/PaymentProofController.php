<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Order;
use App\Models\PromoCode;
use App\Models\PaymentProof;
use Illuminate\Http\Request;
use App\Models\CategoryPrice;
use App\services\FCMService;
use App\services\ImageOptimizationService;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="Payment Proof",
 *     description="API Endpoints for payment proof management"
 * )
 */
class PaymentProofController extends Controller
{


    protected FCMService $firebaseService;
    protected ImageOptimizationService $imageOptimizationService;

    public function __construct(FCMService $firebaseService, ImageOptimizationService $imageOptimizationService)
    {
        $this->firebaseService = $firebaseService;
        $this->imageOptimizationService = $imageOptimizationService;
    }


    private function validatePaymentProof(Request $request)
    {
        // Check if promo code exists and has 100% discount
        $hasFullDiscount = false;
        if ($request->has('promo_code')) {
            $promoCode = PromoCode::where('code', $request->promo_code)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if ($promoCode && $promoCode->discount === 100) {
                $hasFullDiscount = true;
            }
        }

        // Adjust validation rules based on promo code discount
        $rules = [
            'to_store' => 'required|boolean',
            'item_id' => 'required_without:order_id|exists:items,id|nullable',
            'order_id' => 'required_without:item_id|exists:orders,id|nullable',
            'promo_code' => 'nullable|string|exists:promo_codes,code',
        ];

        // Only require screenshot and provider_id if no 100% discount
        if (!$hasFullDiscount) {
            $rules['screenshot'] = 'required|image';
            $rules['provider_id'] = 'required|exists:e_payment_providers,id';
        }

        return Validator::make($request->all(), $rules);
    }


    private function getLocalizedNotificationMessages(string $type, array $params = []): array
{
    // Helper function for Arabic text formatting
    $formatArabic = function(string $text, array $values = []): string {
        return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
    };

    $messages = [
        'payment_received' => [
            'en' => [
                'title' => 'Payment Received',
                'body' => sprintf("Payment received from %s", $params['customer_name'])
            ],
            'fr' => [
                'title' => 'Paiement Reçu',
                'body' => sprintf("Paiement reçu de %s", $params['customer_name'])
            ],
            'ar' => [
                'title' => 'تم استلام الدفع',
                'body' => $formatArabic("%s تم استلام الدفع من", [$params['customer_name']])
            ]
        ],
        'payment_processed' => [
            'en' => [
                'title' => 'Payment Processed',
                'body' => sprintf("Your payment for %s has been processed", $params['item_name'] ?? 'the order')
            ],
            'fr' => [
                'title' => 'Paiement Traité',
                'body' => sprintf("Votre paiement pour %s a été traité", $params['item_name'] ?? 'la commande')
            ],
            'ar' => [
                'title' => 'تمت معالجة الدفع',
                'body' => $formatArabic("%s تمت معالجة دفعتك لـ", [$params['item_name'] ?? 'الطلب'])
            ]
        ]
    ];

    Log::debug('Payment notification parameters', [
        'type' => $type,
        'params' => $params
    ]);

    return $messages[$type] ?? [];
}

private function sendLocalizedNotification(User $user, string $type, array $params = []): void
{
    // Set default parameters
    $params = array_merge([
        'customer_name' => '',
        'item_name' => '',
        'amount' => '',
        'payment_id' => null,
        'order_id' => null,
        'store_id' => null
    ], array_filter($params));

    $token = $user->deviceToken->token ?? null;
    $userLang = $user->lang ?? 'en';

    try {
        // Get all language versions
        $messages = $this->getLocalizedNotificationMessages($type, $params);

        // Get user's preferred language for push notification
        $userMessage = $messages[$userLang] ?? $messages['en'];

        // Create notification with all language versions
        $notification = Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'payment_id' => $params['payment_id'],
            'order_id' => $params['order_id'],
            'store_id' => $params['store_id'],
            'is_read' => false,
            // Store titles in all languages
            'title_en' => $messages['en']['title'],
            'title_fr' => $messages['fr']['title'],
            'title_ar' => $messages['ar']['title'],
            // Store messages in all languages
            'message_en' => $messages['en']['body'],
            'message_fr' => $messages['fr']['body'],
            'message_ar' => $messages['ar']['body'],
            // Store current language version for backwards compatibility
            // 'title' => $userMessage['title'],
            // 'message' => $userMessage['body'],
            // Store parameters for future reference
            'params' => json_encode($params)
        ]);

        // Send push notification if device token exists
        if ($token) {
            $this->firebaseService->sendNotification(
                $token,
                $userMessage['title'],
                $userMessage['body']
            );

            Log::info('Payment notification sent successfully', [
                'user_id' => $user->id,
                'language' => $userLang,
                'type' => $type,
                'notification_id' => $notification->id,
                'payment_id' => $params['payment_id']
            ]);
        } else {
            Log::warning('No device token found for user, notification stored in database only', [
                'user_id' => $user->id
            ]);
        }
    } catch (\Exception $e) {
        Log::error('Failed to process payment notification', [
            'user_id' => $user->id,
            'error' => $e->getMessage(),
            'type' => $type,
            'params' => $params
        ]);
    }
}

    /**
 * Submit payment proof
 *
 * @OA\Post(
 *     path="/api/payment-proof",
 *     operationId="storePaymentProof",
 *     tags={"Payment Proof"},
 *     summary="Submit a new payment proof",
 *     description="Submit payment proof for either an item or order payment. For non-store payments, promo codes can be applied for discounts.",
 *     security={{"bearerAuth":{}}},
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\MediaType(
 *             mediaType="multipart/form-data",
 *             @OA\Schema(
 *                 required={"to_store", "provider_id", "screenshot"},
 *                 @OA\Property(
 *                     property="to_store",
 *                     type="boolean",
 *                     description="Whether the payment is to a store"
 *                 ),
 *                 @OA\Property(
 *                     property="item_id",
 *                     type="integer",
 *                     description="ID of the item being paid for (required if order_id not provided)"
 *                 ),
 *                 @OA\Property(
 *                     property="order_id",
 *                     type="integer",
 *                     description="ID of the order being paid for (required if item_id not provided)"
 *                 ),
 *                 @OA\Property(
 *                     property="provider_id",
 *                     type="integer",
 *                     description="ID of the payment provider"
 *                 ),
 *                 @OA\Property(
 *                     property="screenshot",
 *                     type="string",
 *                     format="binary",
 *                     description="Payment screenshot (max 5MB)"
 *                 ),
 *                 @OA\Property(
 *                     property="promo_code",
 *                     type="string",
 *                     nullable=true,
 *                     description="Optional promotional code for discount (only applicable when to_store is false)"
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Payment proof submitted successfully",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Payment proof submitted successfully"),
 *             @OA\Property(
 *                 property="data",
 *                 type="object",
 *                 @OA\Property(property="id", type="integer", example=1),
 *                 @OA\Property(property="user_id", type="integer", example=1),
 *                 @OA\Property(property="provider_id", type="integer", example=1),
 *                 @OA\Property(property="to_store", type="boolean", example=true),
 *                 @OA\Property(property="amount", type="integer", example=101),
 *                 @OA\Property(property="original_amount", type="integer", example=120),
 *                 @OA\Property(property="discount_amount", type="integer", example=20),
 *                 @OA\Property(property="promo_code", type="string", nullable=true, example="SUMMER20"),
 *                 @OA\Property(property="screenshot", type="string", example="payment_proofs/abc123.jpg"),
 *                 @OA\Property(property="created_at", type="string", format="date-time"),
 *                 @OA\Property(property="updated_at", type="string", format="date-time")
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=422,
 *         description="Validation error",
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="errors",
 *                 type="object",
 *                 @OA\Property(
 *                     property="promo_code",
 *                     type="array",
 *                     @OA\Items(type="string", example="The promo code is invalid or expired.")
 *                 )
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=403,
 *         description="Unauthorized access",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Unauthorized")
 *         )
 *     ),
 *     @OA\Response(
 *         response=500,
 *         description="Server error",
 *         @OA\JsonContent(
 *             @OA\Property(property="message", type="string", example="Failed to submit payment proof")
 *         )
 *     )
 * )
 */
public function store(Request $request)
{
    $validator = $this->validatePaymentProof($request);

    if ($validator->fails()) {
        return response()->json(['errors' => $validator->errors()], 422);
    }

    try {
        $data = [
            'user_id' => auth()->id(),
            'to_store' => $request->to_store
        ];

        // Check for 100% promo code
        $hasFullDiscount = false;
        if ($request->has('promo_code')) {
            $promoCode = PromoCode::where('code', $request->promo_code)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if ($promoCode && $promoCode->discount === 100) {
                $hasFullDiscount = true;
            }
        }


        if (!$hasFullDiscount) {
            $data['provider_id'] = $request->provider_id;
        }

        if ($request->to_store) {
            $order = Order::with('items')->findOrFail($request->order_id);

            if ($order->user_id !== auth()->id()) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $itemsTotal = $order->items->sum(function ($item) {
                return $item->pivot->price * $item->pivot->quantity;
            });

            $data['amount'] = $itemsTotal + $order->delivery_charge;
            $data['order_id'] = $order->id;
            $data['store_id'] = $order->store_id;

            $order->status = 'PAID';
            $order->save();

            // Send notification to store owner
            if ($order->store && $order->store->user) {
                $this->sendLocalizedNotification(
                    $order->store->user,
                    'payment_received',
                    [
                        'customer_name' => auth()->user()->firstname . ' ' . auth()->user()->lastname
                    ]
                );
            }
        } else {
            $item = Item::findOrFail($request->item_id);
            if ($item->user_id !== auth()->id()) {
                return response()->json(['message' => 'Unauthorized'], 403);
            }

            $categoryPrice = CategoryPrice::where('category_id', $item->category_id)->firstOrFail();
            $originalAmount = $categoryPrice->price;

            if ($request->has('promo_code')) {
                $promoCode = PromoCode::where('code', $request->promo_code)
                    ->where('is_active', true)
                    ->where('expires_at', '>', now())
                    ->first();

                if (!$promoCode) {
                    return response()->json(['message' => 'Invalid or expired promo code'], 422);
                }

                $discountAmount = ($originalAmount * $promoCode->discount) / 100;
                $data['amount'] = $originalAmount - $discountAmount;
                $data['original_amount'] = $originalAmount;
                $data['discount_amount'] = $discountAmount;
                $data['promo_code'] = $promoCode->code;
            } else {
                $data['amount'] = $originalAmount;
            }

            $data['item_id'] = $item->id;
        }

        // Store screenshot only if not 100% discount
        if (!$hasFullDiscount && $request->hasFile('screenshot')) {
            // Use the ImageOptimizationService to optimize and store the screenshot
            $optimizedImageData = $this->imageOptimizationService->optimizeAndStore(
                $request->file('screenshot'),
                'payment_proofs'
            );
            $data['screenshot'] = $optimizedImageData['path'];
        }

        $paymentProof = PaymentProof::create($data);

        return response()->json([
            'message' => 'Payment proof submitted successfully',
            'data' => $paymentProof
        ], 201);

    } catch (\Exception $e) {
        Log::error('Error submitting payment proof: ' . $e->getMessage());
        return response()->json([
            'message' => 'Failed to submit payment proof'
        ], 500);
    }
}

    /**
     * Get payment proof status
     *
     * @OA\Get(
     *     path="/api/payment-proof/{id}",
     *     operationId="showPaymentProof",
     *     tags={"Payment Proof"},
     *     summary="Get payment proof details",
     *     description="Retrieve details of a specific payment proof",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Payment proof ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Payment proof details retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="user_id", type="integer", example=1),
     *                 @OA\Property(property="provider_id", type="integer", example=1),
     *                 @OA\Property(property="to_store", type="boolean", example=true),
     *                 @OA\Property(property="amount", type="number", format="float", example=100.50),
     *                 @OA\Property(property="screenshot", type="string", example="payment_proofs/abc123.jpg"),
     *                 @OA\Property(property="reference_number", type="string", example="REF123"),
     *                 @OA\Property(
     *                     property="provider",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="PayPal")
     *                 ),
     *                 @OA\Property(
     *                     property="item",
     *                     type="object",
     *                     nullable=true,
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Item Name")
     *                 ),
     *                 @OA\Property(
     *                     property="order",
     *                     type="object",
     *                     nullable=true,
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="status", type="string", example="pending")
     *                 ),
     *                 @OA\Property(
     *                     property="store",
     *                     type="object",
     *                     nullable=true,
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Store Name")
     *                 ),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Payment proof not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Payment proof not found")
     *         )
     *     )
     * )
     */
    public function show($id)
    {
        try {
            $paymentProof = PaymentProof::with(['provider', 'item', 'order', 'store'])
                ->where('user_id', auth()->id())
                ->findOrFail($id);

            return response()->json([
                'data' => $paymentProof
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Payment proof not found'
            ], 404);
        }
    }



       /**
     * Get all category prices
     *
     * @OA\Get(
     *     path="/api/category-prices",
     *     operationId="getCategoryPrices",
     *     tags={"Category Prices"},
     *     summary="Get all category prices",
     *     description="Retrieve a list of all categories with their prices (rounded to nearest integer)",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="List of category prices retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="category_id", type="integer", example=1),
     *                     @OA\Property(property="category_name", type="string", example="Electronics"),
     *                     @OA\Property(property="price", type="integer", example=101),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Failed to retrieve category prices")
     *         )
     *     )
     * )
     */
    public function getCategoryPrices()
    {
        try {
            $categoryPrices = CategoryPrice::with('category:id,name')
                ->get()
                ->map(function ($categoryPrice) {
                    return [
                        'id' => $categoryPrice->id,
                        'category_id' => $categoryPrice->category_id,
                        'category_name' => $categoryPrice->category->name,
                        'price' => round($categoryPrice->price), // Rounded to nearest integer
                        'created_at' => $categoryPrice->created_at,
                        'updated_at' => $categoryPrice->updated_at
                    ];
                });

            return response()->json([
                'data' => $categoryPrices
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving category prices: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to retrieve category prices'
            ], 500);
        }
    }

    /**
     * Calculate discounted price with promo code
     *
     * @OA\Post(
     *     path="/api/calculate-price",
     *     operationId="calculatePrice",
     *     tags={"Category Prices"},
     *     summary="Calculate price with promo code",
     *     description="Calculate the discounted price for a category using a promo code (all prices rounded to nearest integer)",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"category_id", "promo_code"},
     *             @OA\Property(property="category_id", type="integer", example=1),
     *             @OA\Property(property="promo_code", type="string", example="SUMMER20")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Price calculation successful",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="original_price", type="integer", example=100),
     *                 @OA\Property(property="discounted_price", type="integer", example=80),
     *                 @OA\Property(property="discount_amount", type="integer", example=20),
     *                 @OA\Property(property="discount_percentage", type="integer", example=20),
     *                 @OA\Property(property="is_valid", type="boolean", example=true),
     *                 @OA\Property(property="message", type="string", example=" applied successfully")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="promo_code",
     *                     type="array",
     *                     @OA\Items(type="string", example="The promo code is invalid or expired.")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function calculatePrice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|exists:categories,id',
            'promo_code' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Get category price
            $categoryPrice = CategoryPrice::where('category_id', $request->category_id)->firstOrFail();
            $originalPrice = round($categoryPrice->price); // Rounded to nearest integer

            // Check promo code validity
            $promoCode = PromoCode::where('code', $request->promo_code)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->first();

            if (!$promoCode) {
                return response()->json([
                    'data' => [
                        'original_price' => $originalPrice,
                        'discounted_price' => $originalPrice,
                        'discount_amount' => 0,
                        'discount_percentage' => 0,
                        'is_valid' => false,
                        'message' => 'Invalid or expired promo code'
                    ]
                ]);
            }

            // Calculate discounted price (all rounded to integers)
            $discountAmount = round(($originalPrice * $promoCode->discount) / 100);
            $discountedPrice = round($originalPrice - $discountAmount);

            return response()->json([
                'data' => [
                    'original_price' => $originalPrice,
                    'discounted_price' => $discountedPrice,
                    'discount_amount' => $discountAmount,
                    'discount_percentage' => $promoCode->discount,
                    'is_valid' => true,
                    'message' => 'Promo code applied successfully'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error calculating price: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to calculate price'
            ], 500);
        }
    }
}