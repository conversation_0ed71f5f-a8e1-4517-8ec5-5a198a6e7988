import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/color_utils.dart';
import 'package:boutigak/utils/deepLinkHandler.dart';
import 'package:boutigak/views/boutigat_store/orderdeatails_page.dart';
import 'package:boutigak/views/boutigat_store/store_widgets.dart' as store_widgets;
import 'package:boutigak/views/widgets/snapchat_item_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class StoreDetailsPage extends StatefulWidget {
  final Store store;
  final int? selectedItemId;

  const StoreDetailsPage({
    Key? key,
    required this.store,
    this.selectedItemId,
  }) : super(key: key);

  @override
  _StoreDetailsPageState createState() => _StoreDetailsPageState();
}

class _StoreDetailsPageState extends State<StoreDetailsPage> {
  final StoreController storeController = Get.put(StoreController());
  final double expandedHeight = 300.0;
  Color? dominantColor = AppColors.surface;
  Color? surfaceColor = AppColors.surface;
  String? currentImageUrl;

  late final ItemController itemController;
  late final OrderController orderController;
  late bool isFollowing;
  bool isFollowLoading = false;

  @override
  void initState() {
    super.initState();

    // Instanciez vos contrôleurs d'items et d'ordres
    itemController = Get.put(ItemController());
    orderController = Get.put(OrderController(initialOrderId: 1));

    // Définir d'abord le currentStoreId pour éviter un clear() tardif
    storeController.currentStoreId.value = widget.store.id!.toString();

    // 1) CHARGER en parallèle Favoris (catégories) et Items
    Future.wait([
      storeController.fetchStoreFavoriteCategories(widget.store.id!.toString()),
      storeController.fetchStoreItems(widget.store.id!.toString()),
    ]).then((_) {
      // 2) Ensuite, on sélectionne "All"
      storeController.selectAllCategories();
    });

    // Logique existante
    _initializeStore();
    isFollowing = widget.store.isFollowed;
  }

  Future<void> _initializeStore() async {
    if (widget.store.images.isNotEmpty) {
      currentImageUrl = '${hostURLs[PossiblesHosts.STORAGE]}${widget.store.images.first}';
      await _updateDominantColor(currentImageUrl!);
    }

    if (widget.selectedItemId != null) {
      print('Selected item ID: ${widget.selectedItemId}');
      await _handleSelectedItem();
    }
  }

  Future<void> _handleSelectedItem() async {
    try {
      final Item? selectedItem = await ItemService.getItemById(widget.selectedItemId!);
      print('selected item ${selectedItem?.toJson()}');
      if (selectedItem != null && mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => ItemBottomSheetWidget(
            item: selectedItem,
            store: widget.store,
            isFromDeepLink: true,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error handling selected item: $e');
    }
  }
bool isDarkColor(Color color) {
  // Luminance de 0.0 (noir) à 1.0 (blanc)
  final luminance = color.computeLuminance();
  return luminance < 0.5; // < 0.5 = sombre, ≥ 0.5 = claire
}

  Future<void> _updateDominantColor(String imageUrl) async {
  final color = await getMostFrequentColor(imageUrl);
  setState(() {
    dominantColor = color;
    surfaceColor = isDarkColor(color) ? Colors.white : Colors.black;
  });
}


  Future<void> _toggleFollow() async {
    setState(() {
      isFollowLoading = true;
    });

    try {
      Map<String, dynamic>? result = await StoreService.followUnfollowStore(
        widget.store.id!,
        isFollowing,
      );


        print('result ${result}');

      if (result != null && result['success'] == true) {


        setState(() {
          isFollowing = result['is_followed'] ?? !isFollowing;
          widget.store.isFollowed = isFollowing;

          // Update followers count from API response
          widget.store.followersCount = result['followers_count'] ?? widget.store.followersCount;
        });
      }
    } catch (e) {
      debugPrint('Error toggling follow: $e');
    } finally {
      setState(() {
        isFollowLoading = false;
      });
    }
  }

 @override
  Widget build(BuildContext context) {
    final store = widget.store;

    return Scaffold(
      body: Stack(
        children: [
        
          if (store.images.isNotEmpty) ...[
            NestedScrollView(
              headerSliverBuilder:
                  (BuildContext context, bool innerBoxIsScrolled) {
                return <Widget>[
                  SliverAppBar(
                    expandedHeight: expandedHeight,
                    pinned: true,
                    floating: true,
                    elevation: 0,
                    backgroundColor: dominantColor ?? AppColors.primary,
                    flexibleSpace: FlexibleSpaceBar(
                      collapseMode: CollapseMode.pin,
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          Image.network(
                            '${hostURLs[PossiblesHosts.STORAGE]}${store.images.first}',
                            fit: BoxFit.cover,
                          ),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.1),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: TextButton(
                          onPressed: isFollowLoading ? null : _toggleFollow,
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            foregroundColor: AppColors.onSurface,
                          ),
                          child: isFollowLoading
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.onSurface,
                                    ),
                                  ),
                                )
                              : Text(isFollowing ? "Following" : "Follow"),
                        ),
                      ),
                      TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          foregroundColor: AppColors.surface,
                        ),
                        child: Text("${store.followersCount} Followers"),
                      ),
                      // Share button directly beside followers
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        margin: EdgeInsets.only(right: 8, top: 8, bottom: 8, left: 8),
                        child: IconButton(
                          icon: Icon(Icons.share, color: AppColors.background),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return FractionallySizedBox(
                                  heightFactor: 0.6,
                                  child: CapturableStoreWidget(
                                    storeId: store.id.toString(),
                                    imageUrl: '${hostURLs[PossiblesHosts.STORAGE]}${store.images.first}',
                                    storeType: store.typeName.toString(),
                                    storeName: store.name,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                      // Localisation button beside share
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        margin: EdgeInsets.only(right: 16, top: 8, bottom: 8, left: 0),
                        child: IconButton(
                          icon: Icon(Icons.location_on, color: Colors.red),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return FractionallySizedBox(
                                  heightFactor: 0.6,
                                  child: StoreMapDialog(
                                    latitude: store.latitude ?? 18.0735,
                                    longitude: store.longitude ?? -15.9582,
                                    storeName: store.name,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  SliverPersistentHeader(
                    delegate: _SliverAppBarDelegate(
                      child: FavoriteCategoryListWidget(
                        storeId: store.id!,
                      ),
                      height: 62,
                    ),
                    pinned: true,
                  ),
                ];
              },
              body: Container(
                color: Color(0xFFf2f3f5),
                child: Column(
                  children: [
                    Expanded(
                      // On lit réellement des variables réactives ici
                      child: Obx(() {
                        final selectedCat = storeController.selectedCategory.value;
                        if (selectedCat == null) {
                          // "All" => Afficher tous les items
                          return store_widgets.StoreitemsListViewWidget(
                            category: null,
                            items: storeController.myItems,
                            storeImage:
                                '${hostURLs[PossiblesHosts.STORAGE]}${store.images.first}',storeId: store.id.toString(),
                          );
                        } else {
                          // Filtrer par la catégorie
                          final filteredItems = storeController.myItems
                              .where(
                                (item) =>
                                    item.categoryId == selectedCat.id,
                              )
                              .toList();

                          return store_widgets.StoreitemsListViewWidget(
                            category: selectedCat,
                            items: filteredItems,
                            storeId: store.id.toString(),
                            storeImage:
                                '${hostURLs[PossiblesHosts.STORAGE]}${store.images.first}',
                          );
                        }
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            
            Center(child: CircularProgressIndicator()),
          ],

        
          Obx(() {
        
            if (orderController.items.isNotEmpty) {
              return Positioned(
                bottom: 20,
                right: 20,
                child: FloatingActionButton(
                  onPressed: () {

                    orderController.calculateTotal();

                    Get.to(() => CartPage(
                          orderController: orderController,
                          store: widget.store,
                          dominentColor: dominantColor ?? Colors.black,
                          surfaceColor: surfaceColor ?? AppColors.surface
                        ));
                  },
                  backgroundColor: dominantColor,
                  child: Icon(Icons.shopping_cart, color: surfaceColor),
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          }),
        ],
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverAppBarDelegate({
    required this.child,
    required this.height,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;
  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}

// ---------------------------------------------------------------------
// ------------------   FavoriteCategoryListWidget   --------------------
// ---------------------------------------------------------------------
class FavoriteCategoryListWidget extends StatefulWidget {
  final int storeId;

  const FavoriteCategoryListWidget({
    Key? key,
    required this.storeId,
  }) : super(key: key);

  @override
  _FavoriteCategoryListWidgetState createState() => _FavoriteCategoryListWidgetState();
}

class _FavoriteCategoryListWidgetState extends State<FavoriteCategoryListWidget> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Obx(() {
        // 1) On lit les catégories favorites
        final favorites = storeController.FavoriteCategories;

        // 2) On lit la catégorie sélectionnée
        final selectedCat = storeController.selectedCategory.value;

        // 3) Nombre total : +1 pour inclure "All"
        final itemCount = favorites.length + 1;

        return ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: itemCount,
          itemBuilder: (context, index) {
            if (index == 0) {
              // "All"
              final isSelected = (selectedCat == null);
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: TextButton(
                  onPressed: () {
                    storeController.selectAllCategories();
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onSurface,
                  ),
                  child: Text(
                    "All",
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: 
                           Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              );
            } else {
              // Une catégorie favorite réelle
              final category = favorites[index - 1];
              final isSelected = (selectedCat == category);

              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: TextButton(
                  onPressed: () {
                    storeController.selectCategory(category, widget.storeId.toString());
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onSurface,
                  ),
                  child: Text(
                    category.getTitle(),
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color:
                          Theme.of(context).colorScheme.onSurface

                    ),
                  ),
                ),
              );
            }
          },
        );
      }),
    );
  }
}

// Add this widget at the end of the file:

class StoreMapDialog extends StatelessWidget {
  final double latitude;
  final double longitude;
  final String storeName;

  const StoreMapDialog({
    Key? key,
    required this.latitude,
    required this.longitude,
    required this.storeName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        height: 350,
        width: 350,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                storeName,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ),
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: LatLng(latitude, longitude),
                    zoom: 15,
                  ),
                  markers: {
                    Marker(
                      markerId: MarkerId('store_location'),
                      position: LatLng(latitude, longitude),
                    ),
                  },
                  myLocationEnabled: false,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: true,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
