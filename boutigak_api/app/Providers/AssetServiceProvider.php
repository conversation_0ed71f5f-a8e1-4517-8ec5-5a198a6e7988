<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class AssetServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set the SFTP base URL for image storage operations only
        $sftpBaseUrl = 'https://storage.boutigak.com ';

        // Override Storage URL generation for images only
        Storage::macro('imageUrl', function ($path) use ($sftpBaseUrl) {
            $path = ltrim($path, '/');
            // Ensure storage prefix
            if (!str_starts_with($path, 'storage/')) {
                $path = 'storage/' . $path;
            }
            return $sftpBaseUrl . '/' . $path;
        });
    }
}
