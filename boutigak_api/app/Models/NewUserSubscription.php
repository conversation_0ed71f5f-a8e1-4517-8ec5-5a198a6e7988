<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NewUserSubscription extends Model
{
    protected $fillable = ['user_id', 'price', 'start_date', 'end_date'];

    protected $table = 'new_user_subscriptions';

    protected $dates = ['start_date', 'end_date'];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
