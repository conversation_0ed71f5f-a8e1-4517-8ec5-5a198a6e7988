<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StorePromotion extends Model
{
    protected $table = 'store_promotion';

    protected $fillable = [
        'percentage',
        'store_id',
        'start_date',
        'end_date',
        
    ];

    protected $dates = [
        'start_date',
        'end_date',
        
        'created_at',
        'updated_at',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }
}
