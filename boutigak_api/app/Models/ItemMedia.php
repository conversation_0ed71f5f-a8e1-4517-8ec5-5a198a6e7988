<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemMedia extends Model
{
    protected $table = 'item_media';

    protected $fillable = [
        'media_id',
        'item_id',
        'order'
        
    ];

    protected $dates = [
        
        'created_at',
        'updated_at',
    ];

    public function media()
    {
        return $this->belongsTo(Media::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
