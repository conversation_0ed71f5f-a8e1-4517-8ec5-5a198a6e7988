import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/snapchat_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:palette_generator/palette_generator.dart';
import 'package:share_plus/share_plus.dart';

class CapturableItemWidget extends StatefulWidget {
  final String imageUrl;
  final String? ownerName;
  final String title;
  final String? brand;
  final double price;
  final int itemId;

  CapturableItemWidget({
    required this.imageUrl,
    required this.itemId,
    required this.title,
    required this.brand,
    required this.ownerName,
    required this.price,
  });

  @override
  _CapturableItemWidgetState createState() => _CapturableItemWidgetState();
}

class _CapturableItemWidgetState extends State<CapturableItemWidget> {
  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RepaintBoundary(
          key: _globalKey,
          child: Container(
            width: 300,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            // Use the first image of the boutique if it exists, otherwise display a default image
                            image: AssetImage('assets/images/icon.png')
                                as ImageProvider,
                            fit: BoxFit.cover,
                          ),
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.title,
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            Row(
                              children: [
                                Text(
                                  "by ",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                Text(
                                  "${widget.ownerName}",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).disabledColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Image.network(widget.imageUrl,
                    width: 300,
                    fit: BoxFit.cover), // Affiche l'image du produit

                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Swipe",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                "Up ",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                              Icon(
                                FontAwesomeIcons.arrowTrendUp,
                              )
                            ],
                          ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            "Price (mru)",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).disabledColor,
                            ),
                          ),
                          Text(
                            widget.price.toStringAsFixed(0),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 150,
              child: ElevatedButton(
                onPressed: () async {
                  // Capture l'image du widget et l'envoie via Snap Kit
                  Uint8List? capturedImage = await _captureWidgetAsImage();
                  if (capturedImage != null) {
                    // Envoie l'image capturée via Snap Kit
                    SnapchatShare snapchatShare = SnapchatShare();
                    try {
                      print('Attempting to share to Snapchat...');

                      // Partager l'image capturée avec Snapchat
                      await snapchatShare.shareCapturedImage(
                          capturedImage, "Check out this product!");
                      print('Share to Snapchat completed successfully.');

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Share to Snapchat initiated!')),
                      );
                    } catch (e) {
                      print('Error sharing to Snapchat: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text('Error sharing to Snapchat: $e')),
                      );
                    }
                    print("Image capturée avec succès, prête à être envoyée.");
                  } else {
                    print("Erreur lors de la capture de l'image.");
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset(
                      'assets/images/snapchat.png',
                      width: 25,
                      height: 25,
                    ),
                    Text(
                      'Snapchat',
                      style: TextStyle(color: AppColors.onSurface),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 10,
            ),
            Container(
              width: 150,
              child: ElevatedButton(
                onPressed: () async {
                  String deepLink = _generateDeepLink(widget.itemId.toString());
                  await Share.share('Découvrez ce produit : $deepLink');
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Icon(FontAwesomeIcons.share,
                        color: AppColors.primary, size: 25),
                    Text('Share Link',
                        style: TextStyle(color: AppColors.onSurface)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _generateDeepLink(String? itemId) {
    return 'https://app.boutigak.com/items/$itemId';
  }

  // Fonction pour capturer l'image du widget
  Future<Uint8List?> _captureWidgetAsImage() async {
    try {
      RenderRepaintBoundary boundary = _globalKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      print("Erreur lors de la capture du widget en image : $e");
      return null;
    }
  }
}

class CapturableStoreWidget extends StatefulWidget {
  final String imageUrl;
  final String? storeType;
  final String storeName;
  final String storeId;

  const CapturableStoreWidget({
    super.key,
    required this.imageUrl,
    required this.storeType,
    required this.storeId,
    required this.storeName,
  });

  @override
  _CapturableStoreWidgetState createState() => _CapturableStoreWidgetState();
}

class _CapturableStoreWidgetState extends State<CapturableStoreWidget> {
  final GlobalKey _globalKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        RepaintBoundary(
          key: _globalKey,
          child: Container(
            width: 300,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: AppColors.disabled,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Image.network(
                        widget.imageUrl,
                        width: 300,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 5,
                      left: 5,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        margin: EdgeInsets.only(
                            right: 8, top: 8, bottom: 8, left: 8),
                        padding: EdgeInsets.all(4),
                        child: Image(
                          image: AssetImage('assets/images/logo.png'),
                          width: 30,
                          height: 30,
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16.0, vertical: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          Text(
                            widget.storeName,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).disabledColor,
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: Text(
                              widget.storeType.toString(),
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Swipe",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                "Up ",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                              Icon(
                                FontAwesomeIcons.arrowTrendUp,
                              )
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 150,
              child: ElevatedButton(
                onPressed: () async {
                  // Capture l'image du widget et l'envoie via Snap Kit
                  Uint8List? capturedImage = await _captureWidgetAsImage();
                  if (capturedImage != null) {
                    // Envoie l'image capturée via Snap Kit
                    SnapchatShare snapchatShare = SnapchatShare();
                    try {
                      print('Attempting to share to Snapchat...');

                      // Partager l'image capturée avec Snapchat
                      await snapchatShare.shareCapturedImage(
                          capturedImage, "Check out this product!");
                      print('Share to Snapchat completed successfully.');

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Share to Snapchat initiated!')),
                      );
                    } catch (e) {
                      print('Error sharing to Snapchat: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                            content: Text('Error sharing to Snapchat: $e')),
                      );
                    }
                    print("Image capturée avec succès, prête à être envoyée.");
                  } else {
                    print("Erreur lors de la capture de l'image.");
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset(
                      'assets/images/snapchat.png',
                      width: 25,
                      height: 25,
                    ),
                    Text(
                      'Snapchat',
                      style: TextStyle(color: AppColors.onSurface),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 10,
            ),
            Container(
              width: 150,
              child: ElevatedButton(
                onPressed: () async {
                  String deepLink =
                      _generateDeepLink(widget.storeId.toString());
                  await Share.share('Découvrez ce produit : $deepLink');
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Icon(FontAwesomeIcons.share,
                        color: AppColors.primary, size: 25),
                    Text('Share Link',
                        style: TextStyle(color: AppColors.onSurface)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _generateDeepLink(String? storeId) {
    return 'https://app.boutigak.com/stores/$storeId';
  }

  // Fonction pour capturer l'image du widget
  Future<Uint8List?> _captureWidgetAsImage() async {
    try {
      RenderRepaintBoundary boundary = _globalKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      print("Erreur lors de la capture du widget en image : $e");
      return null;
    }
  }
}
