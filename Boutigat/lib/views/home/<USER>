import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/widgets/cached_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:lottie/lottie.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shimmer/shimmer.dart';





class FollowedStoresPage extends StatefulWidget {
  @override
  _FollowedStoresPageState createState() => _FollowedStoresPageState();
}

class _FollowedStoresPageState extends State<FollowedStoresPage> {
  final StoreController storeController = Get.put(StoreController());
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFollowedStores();
    });
  }

  Future<void> _loadFollowedStores() async {
    setState(() => _isLoading = true);
    await storeController.fetchFollowedStores();
    setState(() => _isLoading = false);
  }

  Future<void> _onRefresh() async {
    try {
      await storeController.fetchFollowedStores();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'followed_stores'.tr,
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.primary),
      ),
      body: SmartRefresher(
        controller: _refreshController,
        enablePullDown: true,
        onRefresh: _onRefresh,
        header: CustomHeader(
          height: 100,
          builder: (context, mode) {
            return Lottie.asset(
              'assets/lottie/loader.json',
              width: 75,
              height: 75,
              repeat: true,
              animate: true,
            );
          },
        ),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: _isLoading
                  ? _buildShimmerList()
                  : Obx(() {
                      final stores = storeController.FollowedStores;
                      if (stores.isEmpty) {
                        return SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: Center(child: Text("No followed stores")),
                        );
                      }
                      return ListView.builder(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: stores.length,
                        itemBuilder: (context, index) {
                          final store = stores[index];
                          return FollowedStoreWidget(
                            store: store,
                            isFollowing: store.isFollowed,
                            toggleFollow: () {
                              setState(() {
                                store.isFollowed = !store.isFollowed;
                              });
                            },
                          );
                        },
                      );
                    }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(vertical: 8),
      itemCount: 10,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
          padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 4,
                offset: Offset(0.4, 0.4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Image
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 120,
                        height: 14,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                    SizedBox(height: 6),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 80,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 8),
              Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 70,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}



class FollowedStoreWidget extends StatefulWidget {
  final Store store;
  final bool isFollowing;
  final void Function()? toggleFollow;

  const FollowedStoreWidget({
    Key? key,
    required this.store,
    required this.isFollowing,
    required this.toggleFollow,
  }) : super(key: key);

  @override
  _FollowedStoreWidgetState createState() => _FollowedStoreWidgetState();
}

class _FollowedStoreWidgetState extends State<FollowedStoreWidget> {
  late bool isFollowing = false;
  bool isFollowLoading = false;

  @override
  void initState() {
    super.initState();
    isFollowing = widget.isFollowing;
  }

  Future<void> _toggleFollow() async {
    final storeId = widget.store.id; // Might be null
    if (storeId == null) {
      // Handle the case of a null storeId (log, return, show message, etc.)
      return;
    }

    setState(() {
      isFollowLoading = true;
    });

    try {
      Map<String, dynamic>? result = await StoreService.followUnfollowStore(
        storeId,
        isFollowing,
      );

      if (result != null && result['success'] == true) {
        setState(() {
          isFollowing = result['is_followed'] ?? !isFollowing;
          widget.store.isFollowed = isFollowing;

          // Update followers count from API response
          widget.store.followersCount = result['followers_count'] ?? widget.store.followersCount;
        });
      }
    } catch (e) {
      debugPrint('Error toggling follow: $e');
    } finally {
      setState(() {
        isFollowLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final store = widget.store;
    final storeName = store.name ?? '';              // Fallback if name is null
    final storeType = store.typeName ?? 'Unknown';   // Fallback if typeName is null
    final images = store.images ?? [];               // Fallback if images is null

    return GestureDetector(
      onTap: () {
        // If you want to guard, you can also check for store.id != null or something else
        Get.to(() => StoreDetailsPage(store: store));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0.4, 0.4),
            ),
          ],
        ),
        child: Row(
          children: [
          
           
            CachedImageWidget(
      imageUrl: '${hostURLs[PossiblesHosts.STORAGE]}${images.first}',
      width: 70,
      height: 70,
      fit: BoxFit.cover,
  borderRadius: BorderRadius.circular(8.0),
  ),
    const SizedBox(width: 8.0), 
            Expanded(
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        storeName,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Text(
                          storeType,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                ],
              ),
            ),
            TextButton(
              onPressed: isFollowLoading ? null : _toggleFollow,
              style: TextButton.styleFrom(
                backgroundColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                foregroundColor: Colors.white, // Text color
              ),
              child: isFollowLoading
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(isFollowing ? "following".tr : "follow".tr, style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
      ),
    );
  }
}
