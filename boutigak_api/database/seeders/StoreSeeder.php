<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Store;
use App\Models\Media;
use App\Models\StoreMedia;
use Illuminate\Support\Facades\Storage;

class StoreSeeder extends Seeder
{
    public function run()
    {
        // Create a store
        $store = Store::query()->create([
            'name' => 'Test Store',
            'description' => 'This is a test store.',
            'user_id' => 1,
            'type_id' => 1,
            'location_id' => 1,
            'is_promoted' => true,
            'opening_time' => '09:00',
            'closing_time' => '18:00',
        ]);

        // Add images to the store
        $images = [
            'public/backoffice/assets/images/product/img1.png',
            'public/backoffice/assets/images/product/img2.png',
            'public/backoffice/assets/images/product/img3.png',
        ];

        foreach ($images as $imagePath) {
            $media = Media::query()->create([
                'type' => 'image/jpeg',
                'url' => Storage::url($imagePath),
            ]);

            StoreMedia::query()->create([
                'store_id' => $store->id,
                'media_id' => $media->id,
                //'dimension' => '800x600',
            ]);
        }
    }
}
