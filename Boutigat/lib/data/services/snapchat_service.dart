import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';


class SnapchatShare {
  static const platform = MethodChannel('com.example.snapchat_share');

  // Share captured image to Snapchat with Uint8List (binary data)
  Future<void> shareCapturedImage(Uint8List imageData, String caption) async {
    try {
      // Envoyer les données binaires directement via MethodChannel
      await platform.invokeMethod('shareToCamera', {
        'imageData': imageData,  // Utilisation directe des données binaires
        'caption': caption,
         'attachmentUrl': 'https://example.com/redirect' 
      });
    } on PlatformException catch (e) {
      print("Failed to share to Snapchat: ${e.message}");
    }
  }
}