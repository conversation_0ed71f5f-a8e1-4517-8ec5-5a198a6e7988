


import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/services/auth_service.dart';

class ResetPasswordController extends GetxController {
  RxBool phone = false.obs;
  RxBool password = false.obs;
  RxBool passwordConfirm = false.obs;
  var isLoading = false.obs;
  var showErrorMessage = "".obs;

  bool validatePasswords(String password, String passwordConfirm) {
    if (password != passwordConfirm) {
      showErrorMessage("Les mots de passe ne correspondent pas.");
      return false;
    }

    if (password.length < 8 || passwordConfirm.length < 8) {
      showErrorMessage("Le mot de passe doit contenir au moins 8 caractères.");
      return false;
    }
    return true;
  }

  void resetPassword(String phone, String otp, String password, String passwordConfirm) async {
    isLoading(true);
    showErrorMessage("");

    // Validation locale (on peut affiner selon besoins)
    if (!validatePasswords(password, passwordConfirm)) {
      isLoading(false);
      return;
    }

    try {
      // On appelle la méthode qui nécessite OTP
      final response = await AuthService.resetPassword(
        phone: phone,
        otp: otp,
        password: password,
        passwordConfirmation: passwordConfirm,
      );


      print("response reset password ${response}");

      // Vérification du succès
      if (response != null && response["message"] == "Password reset successfully") {
        // Par exemple, on retourne à la page de login
        Get.to(LoginPage()) ;
         Get.snackbar("Succès", "Mot de passe réinitialisé avec succès.");
      } else {
        showErrorMessage("Erreur lors de la réinitialisation du mot de passe.");
      }
    } catch (e) {
      showErrorMessage("Erreur : ${e.toString()}");
    } finally {
      isLoading(false);
    }
  }
}

