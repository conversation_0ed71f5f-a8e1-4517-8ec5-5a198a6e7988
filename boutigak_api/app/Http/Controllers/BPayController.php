<?php

namespace App\Http\Controllers;

use App\Models\ItemPayment;
use App\Models\Transaction;


use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\services\BPayService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


class BPayController extends Controller
{
    protected BPayService $bPayService;

    public function __construct(BPayService $bPayService)
    {
        $this->bPayService = $bPayService;
    }

    /**
     * Initialize payment session
     * 
     * @OA\Post(
     *     path="/api/payments/bpay/initialize",
     *     tags={"Payments"},
     *     summary="Initialize B-Pay payment session",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"amount"},
     *             @OA\Property(property="amount", type="number", example="100.00")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Payment session initialized"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function initialize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $transaction = Transaction::create([
                'amount' => $request->amount,
                'operation_id' => 'OP-' . Str::random(10),
                'status' => 'INITIALIZED',
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'operation_id' => $transaction->operation_id,
                    'amount' => $transaction->amount,
                    'merchant_code' => config('services.bpay.merchant_code')
                ]
            ]);



        } catch (\Exception $e) {
            Log::error('Payment initialization failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize payment'
            ], 500);
        }
    }

    /**
     * Process payment
     * 
     * @OA\Post(
     *     path="/api/payments/bpay/process",
     *     tags={"Payments"},
     *     summary="Process B-Pay payment",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"phone", "passcode", "amount", "item_id"},
     *             @OA\Property(property="phone", type="string"),
     *             @OA\Property(property="passcode", type="string"),
     *             @OA\Property(property="amount", type="number"),
     *             @OA\Property(property="item_id", type="string")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Payment processed successfully"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function process(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'passcode' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'item_id' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create transaction first
            $transaction = Transaction::create([
                'amount' => $request->amount,
                'operation_id' => 'OP-' . Str::random(10),
                'status' => 'INITIALIZED',
                'user_id' => auth()->id(),
                'item_id' => $request->item_id
            ]);

            // Authenticate with B-Pay
            $auth = $this->bPayService->authenticate(
                config('services.bpay.username'),
                config('services.bpay.password')
            );

            Log::info('auth response'.json_encode($auth));
            if (isset($auth['error'])) {
                throw new \Exception($auth['message']);
            }

            // Process payment
            $payment = $this->bPayService->processPayment(
                $request->phone,
                $request->passcode,
                $transaction->operation_id,
                $transaction->amount,
                $request->user()->lang ?? 'FR'
            );

            Log::info('payment response '.json_encode($payment));

            if ($payment['errorCode'] != 0 ) {
                $transaction->update([
                    'status' => 'FAILED',
                    'error_message' => $payment['errorMessage']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $payment['errorMessage']
                ], 400);
            }

            else {

                $transaction->update([
                    'status' => 'COMPLETED',
                    'payment_id' => $payment['transactionId'] ?? null
                ]);


                ItemPayment::create([
                    'item_id' => $request->item_id,
                    'amount' => $transaction->amount,
                    'type' => 'e-pay',
                ]);
            }
            

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'operation_id' => $transaction->operation_id,
                    'status' => 'PROCESSING',
                    'payment_id' => $payment['transactionId'] ?? null,
                    'amount' => $transaction->amount,
                    'merchant_code' => config('services.bpay.merchant_code')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'item_id' => $request->item_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }

    /**
     * Verify payment status
     * 
     * @OA\Get(
     *     path="/api/payments/bpay/{transactionId}/status",
     *     tags={"Payments"},
     *     summary="Check B-Pay payment status",
     *     @OA\Parameter(
     *         name="transactionId",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(response=200, description="Payment status retrieved"),
     *     @OA\Response(response=404, description="Transaction not found")
     * )
     */
    public function status(string $transactionId): JsonResponse
    {
        try {
            $transaction = Transaction::findOrFail($transactionId);

            // Verify transaction belongs to authenticated user
            if ($transaction->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }
            
            $status = $this->bPayService->checkTransaction($transaction->operation_id);

            if (isset($status['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $status['message']
                ], 400);
            }

            // Map B-Pay status to our status
            $newStatus = match($status['status']) {
                'TS' => 'COMPLETED',
                'TF' => 'FAILED',
                'TA' => 'PENDING',
                default => 'UNKNOWN'
            };

            $transaction->update([
                'status' => $newStatus,
                'error_message' => $status['errorMessage'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'status' => $newStatus,
                    'payment_id' => $status['transactionId'] ?? null,
                    'amount' => $transaction->amount,
                    'error_message' => $status['errorMessage'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment status check failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check payment status'
            ], 500);
        }
    }





    /**
     * Test B-Pay payment processing
     * 
     * @OA\Post(
     *     path="/api/test-bpay",
     *     tags={"Payments"},
     *     summary="Test B-Pay payment processing",
     *     description="Test endpoint for processing B-Pay payments",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"phone", "passcode", "operation_id", "amount"},
     *             @OA\Property(property="phone", type="string", description="Customer phone number"),
     *             @OA\Property(property="passcode", type="string", description="Payment passcode"),
     *             @OA\Property(property="operation_id", type="string", description="Operation ID"),
     *             @OA\Property(property="amount", type="string", description="Payment amount")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Payment processed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="transaction_id", type="string"),
     *                 @OA\Property(property="status", type="string", example="PROCESSING"),
     *                 @OA\Property(property="payment_id", type="string", nullable=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Payment processing failed",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Payment processing failed")
     *         )
     *     )
     * )
     */
    public function testBpay(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'passcode' => 'required|string',
            'operation_id' => 'required|string',
            'amount' => 'required|string',
        ]);


        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {

            // Authenticate with B-Pay
            $auth = $this->bPayService->authenticate(
                config('services.bpay.username'),
                config('services.bpay.password')
            );


            Log::info('auth response'.json_encode($auth));
            if (isset($auth['error'])) {
                throw new \Exception($auth['message']);
            }

            // Process payment
            $payment = $this->bPayService->processPayment(
                $request->phone,
                $request->passcode,
                $request->operation_id,
                $request->amount,
                'FR'
            );

            Log::info('payment response '.json_encode($payment));

            if (isset($payment['errorCode']) && $payment['errorCode'] != 0 ) {
                return response()->json([
                    'success' => false,
                    'message' => $payment
                ], 400);
            }

            return response()->json([
                'success' => true,
                'msg' => $payment
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $request->transaction_id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }
}
