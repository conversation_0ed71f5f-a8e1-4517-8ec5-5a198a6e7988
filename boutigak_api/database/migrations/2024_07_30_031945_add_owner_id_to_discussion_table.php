<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('discution', function (Blueprint $table) {
                        
        //     $table->unsignedBigInteger('owner_id')->nullable();
        //     $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');

        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('discussion', function (Blueprint $table) {
            //
        });
    }
};
