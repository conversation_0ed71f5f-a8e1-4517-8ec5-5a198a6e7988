@extends('backoffice.layouts.layout')

@section('title', 'Edit Category | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Edit Category</h4>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('categories.update', $category->id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-3">
                                    <label for="title_en" class="form-label">Title (EN)</label>
                                    <input type="text" class="form-control" id="title_en" name="title_en" value="{{ $category->title_en }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="title_ar" class="form-label">Title (AR)</label>
                                    <input type="text" class="form-control" id="title_ar" name="title_ar" value="{{ $category->title_ar }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="title_fr" class="form-label">Title (FR)</label>
                                    <input type="text" class="form-control" id="title_fr" name="title_fr" value="{{ $category->title_fr }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="parent_id" class="form-label">Parent Category</label>
                                    <select class="form-control" id="parent_id" name="parent_id">
                                        <option value="">None</option>
                                        @foreach($categories as $parent)
                                            <option value="{{ $parent->id }}" {{ $category->parent_id == $parent->id ? 'selected' : '' }}>{{ $parent->title_en }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="details" class="form-label">Details</label>
                                    <div id="details">
                                        @foreach($category->details as $index => $detail)
                                            <div class="detail-item">
                                                <input type="text" class="form-control mb-2" name="details[{{ $index }}][label_en]" value="{{ $detail->label_en }}" placeholder="Label (EN)" required>
                                                <input type="text" class="form-control mb-2" name="details[{{ $index }}][label_fr]" value="{{ $detail->label_fr }}" placeholder="Label (FR)" required>
                                                <input type="text" class="form-control mb-2" name="details[{{ $index }}][label_ar]" value="{{ $detail->label_ar }}" placeholder="Label (AR)" required>
                                            </div>
                                        @endforeach
                                    </div>
                                    <button type="button" class="btn btn-secondary" id="addDetail">Add Detail</button>
                                </div>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            $('#addDetail').on('click', function() {
                var index = $('#details .detail-item').length;
                var detailHtml = `
                    <div class="detail-item">
                        <input type="text" class="form-control mb-2" name="details[${index}][label_en]" placeholder="Label (EN)" required>
                        <input type="text" class="form-control mb-2" name="details[${index}][label_fr]" placeholder="Label (FR)" required>
                        <input type="text" class="form-control mb-2" name="details[${index}][label_ar]" placeholder="Label (AR)" required>
                    </div>
                `;
                $('#details').append(detailHtml);
            });
        });
    </script>
@endpush
