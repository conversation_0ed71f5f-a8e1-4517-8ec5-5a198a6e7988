so i  am trying to  creative-kit  snoachat with my flutter app  throe platform.invoke<PERSON>ethod i am able now to open snao but i am getting 'something went worng in snapchat' i will give you my code and the code that i found in docs so you can modifiy my code if nedded to help me and add log inside app delgate so i can see the log the ptint i snot workiing flutter  import 'package:boutigak/data/services/snapchat_service.dart';
import 'package:boutigak/views/profil/my_order_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'dart:typed_data';
import 'package:http/http.dart' as http;

// class ProductWidget extends StatefulWidget {
//   final String imageUrl;  // L'URL de l'image du produit
//   final String title;     // Le titre du produit
//   final String description; // La description du produit
//   final String productLink; // Le lien vers la page produit

//   ProductWidget({required this.imageUrl, required this.title, required this.description, required this.productLink});

//   @override
//   _ProductWidgetState createState() => _ProductWidgetState();
// }

// class _ProductWidgetState extends State<ProductWidget> {
//   GlobalKey _globalKey = GlobalKey(); // Clé pour capturer l'image du widget

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         RepaintBoundary( // Capture l'image du widget
//           key: _globalKey,
//           child: Card(
//             child: Column(
//               children: [
//                 Image.network(widget.imageUrl, height: 200), // Affiche l'image du produit
//                 Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: Text(widget.title, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: Text(widget.description),
//                 ),
//                 Padding(
//                   padding: const EdgeInsets.all(8.0),
//                   child: Text(
//                     'Cliquez pour voir le produit',
//                     style: TextStyle(color: Colors.blue, decoration: TextDecoration.underline),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//         ElevatedButton(
//           onPressed: () async {
//             await _captureAndShare(); // Partage le sticker capturé
//           },
//           child: Text('Partager sur Snapchat'),
//         ),
//       ],
//     );
//   }

//   // Capture le widget sous forme d'image et le partage sans lien
//   Future<void> _captureAndShare() async {
//     try {
//       // Capture l'image du widget
//       RenderRepaintBoundary boundary = _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
//       ui.Image image = await boundary.toImage(pixelRatio: 3.0);
//       ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
//       Uint8List pngBytes = byteData!.buffer.asUint8List();

//       // Sauvegarde l'image dans un fichier temporaire
//       final directory = await getTemporaryDirectory();
//       final imagePath = '${directory.path}/product_image.png';
//       final imageFile = File(imagePath);
//       await imageFile.writeAsBytes(pngBytes);

//       // Appelle la méthode native pour partager l'image sans lien vers Snapchat
//       await SnapchatShare().partagerSticker(imagePath);
//     } catch (e) {
//       print('Erreur lors du partage : $e');
//     }
//   }
// }





// class ProductWidget extends StatelessWidget {
//   final String imageUrl;  // L'URL de l'image du produit
//   final String title;     // Le titre du produit
//   final String description; // La description du produit

//   ProductWidget({required this.imageUrl, required this.title, required this.description});

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Card(
//           child: Column(
//             children: [
//               Image.network(imageUrl, height: 200), // Affiche l'image du produit
//               Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Text(title, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
//               ),
//               Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Text(description),
//               ),
//               Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Text(
//                   'Cliquez pour voir le produit',
//                   style: TextStyle(color: Colors.blue, decoration: TextDecoration.underline),
//                 ),
//               ),
//             ],
//           ),
//         ),
//         ElevatedButton(
//           onPressed: () async {
//             // Appelle la méthode pour partager directement l'image
//             await _shareOnSnapchat();
//           },
//           child: Text('Partager sur Snapchat'),
//         ),
//       ],
//     );
//   }

//   // Méthode pour télécharger l'image, l'enregistrer localement et la partager sur Snapchat
//   Future<void> _shareOnSnapchat() async {
//   try {
//     // Télécharger l'image à partir de l'URL
//     final response = await http.get(Uri.parse(imageUrl));
//     if (response.statusCode == 200) {
//       // Sauvegarder l'image téléchargée dans un fichier temporaire
//       final directory = await getTemporaryDirectory();
//       final imagePath = '${directory.path}/product_image.png';
//       final imageFile = File(imagePath);
//       await imageFile.writeAsBytes(response.bodyBytes);

//       // Appeler la méthode native pour partager l'image téléchargée
//       await SnapchatShare().partagerSticker(imageFile.path);
//     } else {
//       print("Erreur lors du téléchargement de l'image : Code ${response.statusCode}");
//     }
//   } catch (e) {
//     print('Erreur lors du partage : $e');
//   }
// }

// }
// class SnapchatShare {
//   static const platform = MethodChannel('com.example.snapchat_share');

//   // Partager un sticker sans lien vers Snapchat
//   Future<void> partagerSticker(String cheminSticker) async {
//     try {
//       await platform.invokeMethod('partagerStickerSansLien', {
//         'cheminSticker': cheminSticker,
//       });
//     } on PlatformException catch (e) {
//       print("Échec du partage sur Snapchat : ${e.message}");
//     }
//   }
// }



class SnapchatShareTestWidget extends StatelessWidget {
  final String clientId = "c50ae703-92aa-4e28-a468-efdc53acdf7d";  
  final String stickerPath = "assets/images/icon.png"; 
  final String caption = "Check out this sticker!";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Snapchat Share Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Test Snapchat Sharing',
              style: TextStyle(fontSize: 24),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                SnapchatShare snapchatShare = SnapchatShare();
                try {
                  print('Attempting to share to Snapchat...');
                  print('Client ID: $clientId');
                  print('Sticker Path: $stickerPath');
                  print('Caption: $caption');

                  // Trigger sharing to Snapchat with sticker and caption
                  await snapchatShare.shareToCamera(clientId, stickerPath, caption);
                  print('Share to Snapchat completed successfully.');

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Share to Snapchat initiated!')),
                  );
                } catch (e) {
                  print('Error sharing to Snapchat: $e');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error sharing to Snapchat: $e')),
                  );
                }
              },
              child: Text('Share Sticker to Snapchat'),
            ),
          ],
        ),
      ),
    );
  }
} import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

class SnapchatShare {
  static const platform = MethodChannel('com.example.snapchat_share');

  // Share a sticker with a link to Snapchat Camera
  Future<void> shareToCamera(String clientId, String stickerAssetPath, String caption) async {
    try {
      // Load the asset as ByteData
      final ByteData imageData = await rootBundle.load(stickerAssetPath);
      final List<int> bytes = imageData.buffer.asUint8List();

      print('Loaded Image Data Length: ${bytes.length}'); // Debugging info
      
      // Invoke the method channel with image data
      await platform.invokeMethod('shareToCamera', {
        'clientId': clientId,
        'imageData': bytes, // Send the image as bytes
        'caption': caption,
      });
    } on PlatformException catch (e) {
      print("Échec du partage sur Snapchat : ${e.message}");
    }
  }
}. import Flutter
import UIKit
import FirebaseCore
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  private let CHANNEL = "com.example.snapchat_share"

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    
    // Google Maps API Key Setup
    GMSServices.provideAPIKey("AIzaSyBBVmQvPJIvxKJqlrgbelKtYdEJnF_GcF0")
    
    // Firebase Configuration (uncomment if Firebase is required)
    // FirebaseApp.configure()

    // Notifications delegate setup
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }

    // Flutter setup
    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    let snapchatShareChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
    
    snapchatShareChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
      if call.method == "shareToCamera" {
        guard let args = call.arguments as? [String: Any],
              let clientId = args["clientId"] as? String,
              let imageData = args["imageData"] as? FlutterStandardTypedData,
              let caption = args["caption"] as? String else {
          result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
          return
        }
        print("Received Client ID: \(clientId)")
        print("Received Image Data size: \(imageData.data.count)")
        print("Received Caption: \(caption)")

        self.shareToCamera(clientID: clientId, sticker: imageData.data, caption: caption)
        result(nil)
      } else {
        result(FlutterMethodNotImplemented)
      }
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  // Function to resize the image to a new width while maintaining the aspect ratio
func resizeImage(image: UIImage, targetWidth: CGFloat) -> UIImage? {
    let size = image.size
    let widthRatio  = targetWidth  / size.width
    let newSize = CGSize(width: targetWidth, height: size.height * widthRatio)

    UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
    image.draw(in: CGRect(origin: .zero, size: newSize))
    let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    return resizedImage
}
 private func shareToCamera(clientID: String, sticker: Data, caption: String?) {
    // Convert the sticker data to a UIImage
    guard let originalImage = UIImage(data: sticker) else {
        print("Failed to create UIImage from sticker data.")
        return
    }

    // Resize the image to a target width (e.g., 200 pixels)
    let targetWidth: CGFloat = 200
    guard let resizedImage = resizeImage(image: originalImage, targetWidth: targetWidth) else {
        print("Failed to resize image.")
        return
    }

    // Convert the resized image back to PNG data
    guard let pngData = resizedImage.pngData() else {
        print("Failed to convert resized image to PNG format.")
        return
    }

    // Create a dictionary to configure share action
    var dict: [String: Any] = [
        "com.snapchat.creativekit.clientID": clientID
    ]

    // Add the resized sticker image
    dict["com.snapchat.creativekit.stickerImage"] = pngData

    // Configure sticker metadata
    var stickerMetadata = [String: Any]()
    stickerMetadata["posX"] = 0.5 // Align horizontally to center
    stickerMetadata["posY"] = 0.5 // Align vertically to middle
    stickerMetadata["rotation"] = 0 // Add no rotation
    stickerMetadata["widthDp"] = 200 // Set width to 200dp
    stickerMetadata["heightDp"] = 200 // Set height to 200dp

    // Embed metadata into a payload dictionary
    var payloadMetadata = [String: Any]()
    payloadMetadata["stickerMetadata"] = stickerMetadata

    // Add sticker metadata to sharing dictionary
    dict["com.snapchat.creativekit.payloadMetadata"] = payloadMetadata

    // Optionally, add caption if provided
    if let caption = caption {
        dict["com.snapchat.creativekit.captionText"] = caption
    }

    // Call function to create and open the share URL to Snapchat
    createAndOpenShareUrl(clientID: clientID, shareDest: "snapchat://camera", dict: dict)
}


  // Function to create and open a share URL to Snapchat
  private func createAndOpenShareUrl(clientID: String, shareDest: String, dict: [String: Any]) {
    // Verify if Snapchat can be opened
    guard var urlComponents = URLComponents(string: shareDest),
          let url = urlComponents.url,
          UIApplication.shared.canOpenURL(url) else {
        print("Cannot open Snapchat.")
        return
    }
    
    // Dictionary contains sharing message configuration
    let items = [dict]

    // Set content in the Pasteboard to expire in 5 minutes.
    let expire = Date().addingTimeInterval(5 * 60)
    let options = [UIPasteboard.OptionsKey.expirationDate: expire]
    UIPasteboard.general.setItems(items, options: options)

    // Ensure that the pasteboard isn't tampered with
    let queryItem = URLQueryItem(name: "checkcount",
                                 value: String(format: "%ld", UIPasteboard.general.changeCount))
    
    // Pass Client ID to the share URL
    let clientIdQueryItem = URLQueryItem(name: "clientId", value: clientID)
    
    // Pass App Display name to the share URL
    let appDisplayNameQueryItem = URLQueryItem(name: "appDisplayName", value: "My App")

    // Create final Share URL
    urlComponents.queryItems = [
        queryItem,
        clientIdQueryItem,
        appDisplayNameQueryItem
    ]

    // Open final Share URL
    if let finalURL = urlComponents.url {
        UIApplication.shared.open(finalURL, options: [:], completionHandler: nil)
    }
  }
}.     this the code from the docs // Shares to the Snapchat Camera with a default sticker or caption to decorate the snap
func shareToCamera(clientID: String, sticker: Data, caption: String?)
{
    // Create a dictionary to configure share action
    var dict: [String: Any] = [ 
        "com.snapchat.creativekit.clientID": clientID
    ]

    // Add sticker image
    dict["com.snapchat.creativekit.stickerImage"] = sticker
    
    // Configure sticker
    var stickerMetadata = [String:Any]()
    stickerMetadata["posX"] = 0.5 // Align horizontally to center
    stickerMetadata["posY"] = 0.5 // Align vertically to middle
    stickerMetadata["rotation"] = 0 // Add no rotation
    stickerMetadata["widthDp"] = 200 // Set width to 200dp
    stickerMetadata["heightDp"] = 200 // Set height to 200dp
    var payloadMetadata = [String:Any]()
    payloadMetadata["stickerMetadata"] = stickerMetadata
    
    // Add sticker metadata to sharing dictionary
    dict["com.snapchat.creativekit.payloadMetadata"] = payloadMetadata
    
    // Optionally Add caption
    if let caption = caption
    {
        dict["com.snapchat.creativekit.captionText"] = caption
    }
    
    createAndOpenShareUrl(clientID:clientID, shareDest: "camera", dict:dict)
}