<?php



namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Transaction extends Model
{
    protected $fillable = [
        'user_id',
        'amount',
        'operation_id',
        'payment_id',
        'status',
        'error_message',
        'provider_id',
        'item_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }


    public function provider()
    {
        return $this->belongsTo(EPaymentProvider::class, 'provider_id');
    }
}
