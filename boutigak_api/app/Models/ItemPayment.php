<?php

namespace App\Models;

use App\Models\Item;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ItemPayment extends Model
{
    use HasFactory;

    protected $table = 'item_payment';

    protected $fillable = [
        'type',
        'photo_url',
        'amount',
        'promo_code',
        'item_id',
        'provider_id',
    ];

    public function item(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Item::class);
    }

    public function provider(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(EPaymentProvider::class);
    }
}
