import 'package:boutigak/data/services/webservices.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/oders.dart';

class StoreOrderValidationPage extends StatelessWidget {
  final Order order;
  final OrderController orderController = Get.find();

  StoreOrderValidationPage({Key? key, required this.order}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    print('ordoer ${order.userFirstName}');
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Order #${order.orderId}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        elevation: 2,
        actions: [
          TextButton.icon(
            onPressed: () => _showOrderSummary(context),
            icon: const Icon(Icons.info_outline, color: Colors.white),
            label: const Text(
              'Summary',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildOrderHeader(),
          Expanded(
            child: Obx(() {
              final nonRemovedItems = order.items.where((item) => 
                !orderController.isItemRemoved(order.orderId!, item.itemId)
              ).toList();
              
              return nonRemovedItems.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: nonRemovedItems.length,
                      itemBuilder: (context, index) {
                        final orderItem = nonRemovedItems[index];
                        return _buildOrderItemCard(orderItem);
                      },
                    );
            }),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildOrderHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
       
       Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Customer Information',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Icon(Icons.person, size: 20, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text('${order.userFirstName ?? ''} ${order.userLastName ?? ''}',
                        style: const TextStyle(fontSize: 14)),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.phone, size: 20, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(order.userPhone ?? 'No phone number',
                        style: const TextStyle(fontSize: 14)),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Text(
          //   'Delivery Address: ${order.deliveryAddress ?? "Not specified"}',
          //   style: const TextStyle(
          //     fontSize: 14,
          //     color: Colors.grey,
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.remove_shopping_cart,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'All items have been removed',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItemCard(OrderItem orderItem) {
    final ItemController itemController = Get.put(
      ItemController(),
      tag: orderItem.itemId.toString(),
    );
    
    itemController.fetchItemById(orderItem.itemId);

    return Obx(() {
      final item = itemController.selectedItem.value;
      if (item == null) return const SizedBox();

      final isModified = orderController.isItemModified(order.orderId!, orderItem.itemId);

      return Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 2,
        child: Container(
          decoration: BoxDecoration(
            border: isModified
                ? Border.all(color: AppColors.primary, width: 2)
                : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image: NetworkImage('${hostURLs[PossiblesHosts.STORAGE]}${item.images.first}'),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Price: \$${item.price}',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            'Ordered: ${orderItem.quantity}',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      onPressed: () => _showRemoveItemDialog(orderItem.itemId),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Text(
                      'Quantity: ',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: TextFormField(
                        initialValue: orderItem.quantity.toString(),
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          isDense: true,
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          suffixIcon: isModified
                              ? const Icon(
                                  Icons.edit,
                                  color: AppColors.primary,
                                  size: 20,
                                )
                              : null,
                        ),
                        onChanged: (value) {
                          int? newQuantity = int.tryParse(value);
                          if (newQuantity != null && newQuantity > 0) {
                            orderController.updateOrderItemQuantity(
                              order.orderId!,
                              orderItem.itemId,
                              newQuantity,
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _showRemoveItemDialog(int itemId) {
    Get.dialog(
      AlertDialog(
        title: const Text('Remove Item'),
        content: const Text('Are you sure you want to remove this item from the order?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              orderController.removeOrderItem(order.orderId!, itemId);
              Get.back();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _showOrderSummary(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Summary',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text('Original Items: ${order.items.length}'),
            Text('Removed Items: ${orderController.removedItems.where((key) => key.startsWith("${order.orderId}_")).length}'),
            Text('Modified Items: ${orderController.modifiedQuantities.keys.where((key) => key.startsWith("${order.orderId}_")).length}'),
            const SizedBox(height: 16),
            const Text(
              'Note: Changes will be applied after validation',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                orderController.validateOrderWithModifications(order.orderId!);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryVariant,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Validate Order',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                orderController.declineOrder(order.orderId!);
                Get.back();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Decline Order',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
