import 'dart:developer';
import 'dart:ui';

import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/views/boutigat_store/store_details_page.dart';
import 'package:boutigak/views/inbox/inbox_page.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/item_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:boutigak/controllers/search_controller.dart';
import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';

import '../../data/services/store_service.dart';
class SearchPage extends StatefulWidget {
  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final MYSearchController controller = Get.put(MYSearchController());
  final TextEditingController textController = TextEditingController();
  final AuthController authController = Get.put(AuthController());

  final FocusNode focusNode = FocusNode();


  @override
  void initState() {
    super.initState();
    controller.searchText.listen((value) {
      if (textController.text != value) {
        textController.text = value;
      }
    });



    print('here in search widget');
    

    if (authController.isAuthenticated.value ) {
       controller.fetchSearchHistory();
    }
   
    controller.getPopularCategories();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Column(
          children: [
            AppBar(
              automaticallyImplyLeading: false,
              title: CupertinoTextField(
                controller: textController,
                focusNode: focusNode,
                placeholder: "search".tr,
                prefix: IconButton(
                  icon: Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                  onPressed: () {
                    controller.submitSearch();
                  },
                ),
                suffix: IconButton(
                  icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
                  onPressed: () {
                    textController.clear();
                    controller.onSearchChanged('');
                    focusNode.unfocus();
                    controller.showHistory.value = true;
                  },
                ),
                onChanged: controller.onSearchChanged,
                onSubmitted: (value) {
                  controller.submitSearch();
                  focusNode.unfocus();
                },
              ),
              backgroundColor: Theme.of(context).colorScheme.surface,
            ),
            Expanded(
              child: Obx(() {
                return ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    if (controller.showHistory.isTrue) ...[
                      ListView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: controller.searchHistory.length,
                        itemBuilder: (context, index) {
                          var item = controller.searchHistory[index];
                          return Column(
                            children: [
                              ListTile(
                                contentPadding: EdgeInsets.symmetric(horizontal: sidePadding),
                                title: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Icon(FontAwesomeIcons.clockRotateLeft, color: Theme.of(context).primaryColor, size: 20),
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(horizontal: sidePadding),
                                        child: Text(item.query, style: TextStyle(fontSize: 16, color: Theme.of(context).colorScheme.onSurface, fontWeight: FontWeight.bold)),
                                      ),
                                    ),
                                    IconButton(
                                      icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurface),
                                      onPressed: () => controller.removeSearchHistory(item.id),
                                    ),
                                  ],
                                ),
                                onTap: () => controller.submitHistorySearch(item.query),
                              ),
                            ],
                          );
                        },
                      ),
                      SizedBox(height: sidePadding),
                      PopularWordsWidget(
                        words: controller.popularCategories.value,
                        onWordSelected: controller.submitHistorySearch,
                      ),
                    ],
                    if (!controller.showHistory.isTrue) ...[
                      Row(
                        children: [
                          SizedBox(width: 8,),
                          Text("${controller.items.length} " ,style: TextStyle( color: Theme.of(context).colorScheme.onSurface,fontSize: AppTextSizes.bodyText, fontWeight: AppFontWeights.bold),),
                          Text("results_for".tr),
                          Text("\"${controller.searchText.value}\"" ,style: TextStyle( color: Theme.of(context).colorScheme.onSurface,fontSize: AppTextSizes.bodyText, fontWeight: AppFontWeights.bold),),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0,vertical: 8,),
                        child: ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: controller.items.length,
                          itemBuilder: (context, index) {
                            var item = controller.items[index];
                            return Column(
                              children: [
                                SearchItemWidget(item: item),
                                
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    textController.dispose();
    super.dispose();
  }
}

class PopularWordsWidget extends StatelessWidget {
  final List<String> words;
  final void Function(String) onWordSelected;

  PopularWordsWidget({
    required this.words,
    required this.onWordSelected,
  }) ;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: sidePadding),
          child: Text("popular".tr, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20, color: Theme.of(context).primaryColor)),
        ),
        SizedBox(height: sidePadding),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: sidePadding),
          child: Wrap(
            spacing: sidePadding / 2,
            runSpacing: sidePadding / 2,
            children: words.map((word) {
              return GestureDetector(
                onTap: () => onWordSelected(word),
                child: FractionallySizedBox(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).dividerColor,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: sidePadding * 0.625, vertical: sidePadding / 2),
                    child: Text(word, style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}




class SearchItemWidget extends StatefulWidget {
  final Item item;
  
  SearchItemWidget({
    Key? key,
    required this.item,
   
  }) : super(key: key);

  @override
  _SearchItemWidgetState createState() => _SearchItemWidgetState();
}

class _SearchItemWidgetState extends State<SearchItemWidget> {
  late bool isFavorited;

  @override
  void initState() {
    super.initState();
    
  }

  
void _showModalBottomSheet(BuildContext context, Item item) async {
  if (item.storeId != null) {


    log('store id ${item.storeId}');
    try {
      Store? store = await StoreService.getStoreById(item.storeId!);
      
      if (store != null) {
        Get.to(() => StoreDetailsPage(
          store: store,
          selectedItemId: item.id,
        ));
        return; 
        
      }
    } catch (e) {
      debugPrint('Error fetching store: $e');
    }
  }

  // Continue with normal bottom sheet for non-store items
  PageController pageController = PageController();
  int currentPage = 0;
  bool isFavorited = false;

  // Get the controllers
  ConversationController conversationController = Get.put(ConversationController());
  ItemController itemController = Get.find<ItemController>();

  // Set the item in the controller so it can be used reactively
  itemController.selectItem(item);
  
  // Fetch the latest item data before showing the bottom sheet
  itemController.fetchItemById(item.id!).then((_) {
    showModalBottomSheet(
      backgroundColor: Theme.of(context).colorScheme.surface,
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Obx(() {
              final updatedItem = itemController.selectedItem.value;

              if (updatedItem == null) {
                return Center(child: CircularProgressIndicator());
              }

              // Build the modal with the updated item data
              return Container(
                height: MediaQuery.of(context).size.height,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Expanded(
                          child: Stack(
                            children: [
                              ImageSlider(
                                pageController: pageController,
                                photos: updatedItem.images.map((image) => '$image').toList(),
                                currentPage: currentPage,
                                onPageChanged: (int page) => setState(() => currentPage = page),
                                borderRadius: 0,
                              ),
                              Positioned(
                                top: 60,
                                right: 10,
                                child: ClipOval(
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                    child: Container(
                                      color: AppColors.onBackground.withOpacity(0.2),
                                      child: IconButton(
                                        iconSize: 20,
                                        icon: Icon(FontAwesomeIcons.upRightFromSquare, color: AppColors.background),
                                        onPressed: () => print('Share Icon Tapped!'),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 60,
                                left: 10,
                                child: ClipOval(
                                  child: BackdropFilter(
                                    filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
                                    child: Container(
                                      color: AppColors.onBackground.withOpacity(0.3),
                                      child: IconButton(
                                        iconSize: 20,
                                        icon: Icon(FontAwesomeIcons.chevronDown, color: AppColors.background),
                                        onPressed: () => Navigator.pop(context),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(),
                        ),
                      ],
                    ),
                    Positioned(
                      top: MediaQuery.of(context).size.height * 0.49,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child: Column(
                          children: [
                            InfoSection(
                              item: updatedItem, // Use the updated item data
                              isFavorited: isFavorited,
                              toggleFavorite: () => setState(() => isFavorited = !isFavorited),
                            ),
                           
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
                        ),
                        child:
                            Padding(
                              padding: const EdgeInsets.only(bottom: 30.0,left: 25,right: 25),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Price (mru)",
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context).disabledColor,
                                        ),
                                      ),
                                      Text(
                                        "${updatedItem.price.toStringAsFixed(0)} ",
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context).colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Spacer(),
                                  Container(
                                    width: 200,  // Button width
                                    height: 50,  // Button height
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.onSurface,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: TextButton(
                                      onPressed: () async {
                                        dynamic success = await conversationController.createDiscussion(item.id!);

                                        if (success != null) {
                                          Get.to(() => ConversationPage(
                                            itemId: item.id!,
                                            item: updatedItem.toJson(),
                                            discussionId: success['discussion']['id'],
                                            interlocutor: item.userName!,
                                          ));
                                        }
                                      },
                                      style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(30),
                                        ),
                                      ),
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text(
                                          "Make an offer",
                                          style: TextStyle(
                                            color: AppColors.primary,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          
                        
                      ),
                    ),
                  ],
                ),
              );
            });
          },
        );
      },
    );
  });
}

  @override
 Widget build(BuildContext context) {
  return GestureDetector(
    onTap: () {
      _showModalBottomSheet(context, widget.item);
    },
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Container(
       
       
        child: Row(
          children: [
               Container(
                  width: 50.0,
                  height: 60.0,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    image: DecorationImage(
        fit: BoxFit.cover,
        image: widget.item.images.isNotEmpty
            ? NetworkImage('${widget.item.images.first}')
            : AssetImage('assets/default_image.png') as ImageProvider,
                    ),
                     boxShadow: [
                    BoxShadow(
        color: Colors.grey.withOpacity(0.3),
        spreadRadius: 2,
        blurRadius: 4,
        offset: Offset(0.5, 0.5), // Position de l'ombre
                    ),
                  ],
                  ),
                ),
                SizedBox(width: 16,),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                
                
                                       
                Text(
                  widget.item.brandName ?? "Unknown",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.disabled,
                  ),
                ),
              ],
            ),
            Spacer(),
            Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Price (mru)",
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).disabledColor,
                                ),
                              ),
                              Text(
                                "${widget.item.price.toStringAsFixed(0)} ",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                            ],
                          ),
          ],
        ),
        
      ),
    ),
  );
}

}
