<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Monitoring - Debug Logs</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #00ff00;
        }
        .log-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }
        .log-title {
            color: #ffff00;
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #444;
            padding-bottom: 10px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px 0;
        }
        .timestamp {
            color: #888;
            font-size: 0.9em;
        }
        .status-online { color: #00ff00; }
        .status-warning { color: #ffaa00; }
        .status-offline { color: #ff0000; }
        .json-data {
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 0.9em;
        }
        .refresh-btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .refresh-btn:hover {
            background: #005a99;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            color: #00aaff;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border: 1px solid #00aaff;
            border-radius: 4px;
        }
        .nav-links a:hover {
            background: #00aaff;
            color: #1a1a1a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Server Monitoring Debug Console</h1>
            <p>Real-time debugging and logging information</p>
        </div>

        <div class="nav-links">
            <a href="/">← Back to Dashboard</a>
            <a href="/api/database.php" target="_blank">Database API</a>
            <a href="/api/overview.php" target="_blank">Overview API</a>
            <button class="refresh-btn" onclick="location.reload()">🔄 Refresh</button>
        </div>

        <?php
        // Function to fetch and display API debug info
        function fetchApiDebug($endpoint, $title) {
            echo "<div class='log-section'>";
            echo "<div class='log-title'>$title</div>";
            
            try {
                $url = "http://localhost:8000/api/$endpoint";
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 10,
                        'ignore_errors' => true
                    ]
                ]);
                
                $response = file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "<div class='timestamp'>Last updated: " . date('Y-m-d H:i:s') . "</div>";
                        
                        // Show status
                        $status = $data['status'] ?? 'unknown';
                        $statusClass = 'status-' . $status;
                        echo "<div class='log-entry'><strong>Status:</strong> <span class='$statusClass'>" . strtoupper($status) . "</span></div>";
                        
                        // Show debug logs if available
                        if (isset($data['debug_logs']) && is_array($data['debug_logs'])) {
                            echo "<div style='margin: 15px 0;'><strong>Debug Logs:</strong></div>";
                            foreach ($data['debug_logs'] as $log) {
                                echo "<div class='log-entry'>$log</div>";
                            }
                        }
                        
                        // Show key metrics
                        echo "<div style='margin: 15px 0;'><strong>Key Metrics:</strong></div>";
                        $metrics = [
                            'connections' => 'Database Connections',
                            'queries' => 'Queries/sec',
                            'slowQueries' => 'Slow Queries',
                            'size' => 'Database Size',
                            'cpu' => 'CPU Usage',
                            'ram' => 'RAM Usage',
                            'disk' => 'Disk Usage'
                        ];
                        
                        foreach ($metrics as $key => $label) {
                            if (isset($data[$key])) {
                                $value = $data[$key];
                                if (in_array($key, ['cpu', 'ram', 'disk']) && is_numeric($value)) {
                                    $value .= '%';
                                }
                                echo "<div class='log-entry'>$label: <strong>$value</strong></div>";
                            }
                        }
                        
                        // Show full JSON response
                        echo "<details style='margin-top: 15px;'>";
                        echo "<summary style='cursor: pointer; color: #ffff00;'>📄 Full JSON Response</summary>";
                        echo "<div class='json-data'>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
                        echo "</details>";
                        
                    } else {
                        echo "<div class='log-entry' style='color: #ff0000;'>❌ Invalid JSON response</div>";
                        echo "<div class='json-data'>$response</div>";
                    }
                } else {
                    echo "<div class='log-entry' style='color: #ff0000;'>❌ Could not fetch data from API</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='log-entry' style='color: #ff0000;'>💥 Error: " . $e->getMessage() . "</div>";
            }
            
            echo "</div>";
        }

        // Display debug info for each API
        fetchApiDebug('database.php', '🗄️ Database Server API Debug');
        fetchApiDebug('overview.php', '📊 Overview API Debug');
        fetchApiDebug('app-server-1.php', '🖥️ App Server 1 API Debug');
        fetchApiDebug('app-server-2.php', '🖥️ App Server 2 API Debug');
        ?>

        <div class="log-section">
            <div class="log-title">🔧 System Information</div>
            <div class="log-entry">PHP Version: <?php echo PHP_VERSION; ?></div>
            <div class="log-entry">Server Time: <?php echo date('Y-m-d H:i:s T'); ?></div>
            <div class="log-entry">Server Software: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></div>
            
            <div style="margin: 15px 0;"><strong>PHP Extensions:</strong></div>
            <?php
            $requiredExtensions = ['pdo', 'pdo_pgsql', 'ssh2', 'curl', 'json'];
            foreach ($requiredExtensions as $ext) {
                $loaded = extension_loaded($ext);
                $status = $loaded ? '✅' : '❌';
                $color = $loaded ? '#00ff00' : '#ff0000';
                echo "<div class='log-entry'>$status <span style='color: $color;'>$ext</span></div>";
            }
            ?>
        </div>

        <div class="log-section">
            <div class="log-title">💡 Recommendations</div>
            <?php if (!extension_loaded('ssh2')): ?>
            <div class="log-entry" style="color: #ffaa00;">
                ⚠️ SSH2 extension not installed. To enable remote server monitoring:
                <div style="margin: 10px 0; padding: 10px; background: #333; border-radius: 4px;">
                    <code>sudo apt-get update && sudo apt-get install php-ssh2</code>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="log-entry">
                🔐 For production, consider using SSH key authentication instead of passwords
            </div>
            <div class="log-entry">
                🔒 Set up HTTPS/SSL for secure access to the monitoring dashboard
            </div>
            <div class="log-entry">
                📊 Configure proper log rotation to prevent disk space issues
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #888;">
            <p>Auto-refresh: <button class="refresh-btn" onclick="setInterval(() => location.reload(), 30000)">Enable 30s Auto-refresh</button></p>
        </div>
    </div>
</body>
</html>
