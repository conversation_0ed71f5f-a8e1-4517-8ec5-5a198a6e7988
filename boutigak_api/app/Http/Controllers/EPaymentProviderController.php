<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\EPaymentProvider;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EPaymentProviderController extends Controller
{
    /**
     * List all payment providers
     *
     * @OA\Get(
     *     path="/api/payment-providers",
     *     tags={"Payment Providers"},
     *     summary="Get all payment providers",
     *     description="Returns list of all payment providers",
     *     @OA\Parameter(
     *         name="has_api",
     *         in="query",
     *         description="Filter by API capability",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Parameter(
     *         name="active",
     *         in="query",
     *         description="Filter by active status",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="BANKILY"),
     *                     @OA\Property(property="provider_code", type="string", example="BPAY"),
     *                     @OA\Property(property="has_api", type="boolean", example=true),
     *                     @OA\Property(property="is_active", type="boolean", example=true),
     *                     @OA\Property(property="description", type="string", example="Bankily Payment Gateway"),
     *                     @OA\Property(property="logo_url", type="string", nullable=true),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = EPaymentProvider::query();

            // Apply has_api filter
            if ($request->has('has_api')) {
                $query->where('has_api', $request->boolean('has_api'));
            }

            // Apply active filter
            if ($request->has('active')) {
                $query->where('is_active', $request->boolean('active'));
            }

            $providers = $query->orderBy('name')->get();

            // Transform the data to ensure consistent format
            $transformedProviders = $providers->map(function ($provider) {
                return [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'provider_code' => $provider->provider_code,
                    'has_api' => $provider->has_api,
                    'is_active' => $provider->is_active,
                    'description' => $provider->description,
                    'logo_url' => $provider->logo_url,
                    'logo' => $provider->logo,
                    'created_at' => $provider->created_at,
                    'updated_at' => $provider->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProviders
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment providers: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch payment providers'
            ], 500);
        }
    }

    /**
     * Get specific payment provider details
     *
     * @OA\Get(
     *     path="/api/payment-providers/{id}",
     *     tags={"Payment Providers"},
     *     summary="Get payment provider details",
     *     description="Returns details of a specific payment provider",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Payment provider ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="BANKILY"),
     *                 @OA\Property(property="provider_code", type="string", example="BPAY"),
     *                 @OA\Property(property="has_api", type="boolean", example=true),
     *                 @OA\Property(property="is_active", type="boolean", example=true),
     *                 @OA\Property(property="description", type="string", example="Bankily Payment Gateway"),
     *                 @OA\Property(property="logo_url", type="string", nullable=true),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(response=404, description="Provider not found")
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $provider = EPaymentProvider::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'provider_code' => $provider->provider_code,
                    'has_api' => $provider->has_api,
                    'is_active' => $provider->is_active,
                    'description' => $provider->description,
                    'logo_url' => $provider->logo_url,
                    'created_at' => $provider->created_at,
                    'updated_at' => $provider->updated_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment provider: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Payment provider not found'
            ], 404);
        }
    }

    /**
     * Get active payment providers with API support
     *
     * @OA\Get(
     *     path="/api/payment-providers/api-enabled",
     *     tags={"Payment Providers"},
     *     summary="Get active API-enabled payment providers",
     *     description="Returns list of active payment providers that support API integration",
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="BANKILY"),
     *                     @OA\Property(property="provider_code", type="string", example="BPAY"),
     *                     @OA\Property(property="description", type="string", example="Bankily Payment Gateway"),
     *                     @OA\Property(property="logo_url", type="string", nullable=true)
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function getApiEnabled(): JsonResponse
    {
        try {
            $providers = EPaymentProvider::where('has_api', true)
                ->where('is_active', true)
                ->get()
                ->map(function ($provider) {
                    return [
                        'id' => $provider->id,
                        'name' => $provider->name,
                        'provider_code' => $provider->provider_code,
                        'description' => $provider->description,
                        'logo_url' => $provider->logo_url
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $providers
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching API-enabled providers: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch API-enabled providers'
            ], 500);
        }
    }
}