# 🐳 Server Monitoring - Docker Development Environment

Complete Docker setup for the Server Monitoring application with all necessary PHP extensions and development tools.

## 🚀 Quick Start

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- Make (optional, for convenience commands)

### 1. Initialize Environment
```bash
# Clone/navigate to the project
cd server-monitoring

# Initialize environment (creates .env file and log directories)
make init

# Update .env with your actual credentials
nano .env
```

### 2. Start Development Environment
```bash
# Build and start containers
make dev

# Or manually:
docker-compose up -d monitoring-app redis-cache
```

### 3. Access the Application
- **Dashboard**: http://localhost:8080
- **Debug Console**: http://localhost:8080/debug.php
- **API Endpoints**: http://localhost:8080/api/

## 📦 Container Services

### Main Application (`monitoring-app`)
- **Base**: PHP 8.2 with Apache
- **Extensions**: PDO, PostgreSQL, SSH2, Redis, cURL, GD, etc.
- **Port**: 8080
- **Volume**: `.:/var/www/html` (live code reloading)

### Redis Cache (`redis-cache`)
- **Purpose**: Caching and session storage
- **Port**: 6379
- **Config**: `docker/redis.conf`

### Nginx Proxy (`nginx-proxy`) - Optional
- **Purpose**: Production-like reverse proxy
- **Ports**: 80, 443
- **Profile**: `production`

### PostgreSQL Test (`postgres-test`) - Optional
- **Purpose**: Local testing database
- **Port**: 5433
- **Profile**: `testing`

### Adminer (`adminer`) - Optional
- **Purpose**: Database management tool
- **Port**: 8081
- **Profile**: `tools`

## 🛠️ Make Commands

### Development
```bash
make dev        # Complete development setup
make up         # Start main containers
make down       # Stop all containers
make restart    # Restart containers
make logs       # Show container logs
make shell      # Access app container shell
```

### Production
```bash
make prod       # Start with Nginx proxy
make ssl        # Generate SSL certificates
```

### Tools & Debugging
```bash
make tools      # Start with Adminer
make debug      # Start in debug mode
make test       # Run test scripts
make status     # Show container status
make stats      # Show resource usage
```

### Maintenance
```bash
make clean      # Clean up containers and volumes
make update     # Update container images
make backup     # Backup Redis data
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Database
DB_HOST=************
DB_PORT=5432
DB_NAME=boutigak_db
DB_USERNAME=postgress
DB_PASSWORD=adminboutigak

# SSH for remote monitoring
SSH_HOST=************
SSH_PORT=22
SSH_USERNAME=your_ssh_username
SSH_PASSWORD=your_ssh_password

# Application
ENVIRONMENT=development
APP_DEBUG=true
```

### Volume Mounts
- **Application Code**: `.:/var/www/html` (live reload)
- **Logs**: `./docker/logs:/var/log/monitoring`
- **Apache Logs**: `./docker/logs/apache:/var/log/apache2`
- **Redis Data**: `redis-data:/data` (persistent)

## 📊 Monitoring Features

### ✅ Included PHP Extensions
- **pdo, pdo_pgsql**: PostgreSQL database connectivity
- **ssh2**: Remote server monitoring via SSH
- **redis**: Caching and session storage
- **curl**: HTTP requests and API calls
- **gd**: Image processing
- **json, xml, mbstring**: Data processing
- **zip**: Archive handling
- **opcache**: PHP performance optimization

### 🔍 Development Tools
- **Debug Console**: Real-time API debugging
- **Error Logging**: Comprehensive error tracking
- **Health Checks**: Container health monitoring
- **Hot Reload**: Live code changes without restart

## 🌐 Access URLs

### Development Mode
- **Main Dashboard**: http://localhost:8080
- **Debug Console**: http://localhost:8080/debug.php
- **Database API**: http://localhost:8080/api/database.php
- **Overview API**: http://localhost:8080/api/overview.php

### With Tools Profile
- **Adminer**: http://localhost:8081
  - Server: `************:5432`
  - Database: `boutigak_db`
  - Username: `postgress`

### Production Mode (with Nginx)
- **HTTP**: http://localhost
- **HTTPS**: https://localhost (after SSL setup)

## 🔐 Security Features

### Development
- CORS headers for API endpoints
- Error logging and debugging
- Environment variable isolation

### Production Ready
- SSL/TLS configuration
- Security headers (HSTS, XSS protection, etc.)
- Rate limiting capabilities
- Access control options

## 🐛 Troubleshooting

### Container Won't Start
```bash
# Check logs
make logs

# Check container status
make status

# Rebuild containers
make clean && make build && make up
```

### Database Connection Issues
```bash
# Test PostgreSQL connection
make shell
php test-postgres.php

# Check environment variables
docker-compose exec monitoring-app env | grep DB_
```

### SSH2 Extension Issues
```bash
# Verify SSH2 is installed
make shell
php -m | grep ssh2

# Test SSH connection
php test-api.php
```

### Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod -R 755 docker/logs
```

## 📈 Performance Optimization

### Development
- **Opcache**: Enabled for PHP performance
- **Redis**: Caching for database queries
- **Volume Optimization**: Efficient file mounting

### Production
- **Nginx**: Reverse proxy with caching
- **Gzip**: Compression for static assets
- **SSL**: HTTP/2 support
- **Health Checks**: Automatic container recovery

## 🔄 Development Workflow

### 1. Code Changes
- Edit files directly in the project directory
- Changes are reflected immediately (volume mount)
- No container restart needed for PHP/HTML/CSS/JS

### 2. Configuration Changes
- Update `.env` file
- Restart containers: `make restart`

### 3. Docker Changes
- Modify Dockerfile or docker-compose.yml
- Rebuild: `make clean && make build && make up`

### 4. Testing
- Run tests: `make test`
- Check debug console: http://localhost:8080/debug.php
- Monitor logs: `make logs`

## 📚 Additional Resources

### Docker Commands
```bash
# Manual container management
docker-compose up -d                    # Start detached
docker-compose down -v                  # Stop and remove volumes
docker-compose logs -f monitoring-app   # Follow app logs
docker-compose exec monitoring-app bash # Shell access
```

### Profiles
```bash
# Start specific profiles
docker-compose --profile production up -d
docker-compose --profile tools up -d
docker-compose --profile testing up -d
```

### Health Checks
```bash
# Check container health
docker-compose ps
curl http://localhost:8080/api/database.php
```

This Docker setup provides a complete development environment with all necessary tools and extensions for the Server Monitoring application! 🎉
