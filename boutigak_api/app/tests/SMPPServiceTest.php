<?php

namespace App\tests;

use App\services\SMPPService;
use PHPUnit\Framework\TestCase;
use SmppClient;
use SocketTransport;

class SMPPServiceTest extends TestCase
{
    protected $smppService;
    protected $smppClientMock;
    protected $socketTransportMock;

    protected function setUp(): void
    {
        // Mock the SocketTransport and SmppClient
        $this->socketTransportMock = $this->createMock(SocketTransport::class);
        $this->smppClientMock = $this->createMock(SmppClient::class);

        // Inject the mocks into the SMPPService
        $this->smppService = $this->getMockBuilder(SMPPService::class)
            ->setConstructorArgs([$this->socketTransportMock, $this->smppClientMock])
            ->onlyMethods(['getEnv'])
            ->getMock();

        // Mock the getEnv method to return test values
        $this->smppService->method('getEnv')->willReturnMap([
            ['SMPP_HOST', 'test_host'],
            ['SMPP_PORT', '1234'],
            ['SMPP_ID', 'test_id'],
            ['SMPP_PASSWORD', 'test_password'],
        ]);
    }

    public function testSendSMS()
    {
        // Arrange
        $to = '47100763';
        $message = 'Test message';

        // Expect the sendSMS method to be called once with the correct parameters
        $this->smppClientMock->expects($this->once())
            ->method('sendSMS')
            ->with('Sender', $to, $message);

        // Act
        $this->smppService->sendSMS($to, $message);

        // Assert
        // No assertions needed as the expectation on the mock will fail the test if not met
    }

    protected function tearDown(): void
    {
        // Clean up
        unset($this->smppService);
        unset($this->smppClientMock);
        unset($this->socketTransportMock);
    }
}
