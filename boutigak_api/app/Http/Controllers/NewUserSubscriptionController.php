<?php

namespace App\Http\Controllers;

use App\Models\NewUserSubscription;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class NewUserSubscriptionController extends Controller
{
    /**
     * @throws \Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $query = NewUserSubscription::query()->with('user');
            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    $editUrl = route('users-subscription.edit', $row->id);
                    $csrfToken = csrf_token();

                    $btn = '<a href="' . $editUrl . '" class="edit btn btn-primary btn-sm me-1">
                            <i class="fas fa-edit"></i> Edit
                        </a>';
                    $btn .= '<button type="button" class="delete btn btn-danger btn-sm" onclick="confirmDelete(' . $row->id . ')">
                            <i class="fas fa-trash-alt"></i> Delete
                         </button>';
                    $btn .= '<form id="delete-form-' . $row->id . '" action="' . route('users-subscription.destroy', $row->id) . '" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="' . $csrfToken . '">
                            <input type="hidden" name="_method" value="DELETE">
                         </form>';

                    return $btn;
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('backoffice.user_subscriptions.index');
    }

    public function create()
    {
        $users = User::all();
        return view('backoffice.user_subscriptions.create', compact('users'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'price' => 'required|numeric',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        NewUserSubscription::query()->create($request->all());
        return redirect()->route('users-subscription.index')->with('success', 'Subscription created successfully.');
    }

    public function edit($id)
    {
        $subscription = NewUserSubscription::query()->findOrFail($id);
        $users = User::all();
        return view('backoffice.user_subscriptions.edit', compact('subscription', 'users'));
    }

    public function update(Request $request, $id)
    {
        $subscription = NewUserSubscription::query()->findOrFail($id);

        $request->validate([
            'user_id' => 'required|exists:users,id',
            'price' => 'required|numeric',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        $subscription->update($request->all());
        return redirect()->route('users-subscription.index')->with('success', 'Subscription updated successfully.');
    }

    public function destroy($id)
    {
        $subscription = NewUserSubscription::query()->findOrFail($id);
        $subscription->delete();
        return redirect()->route('users-subscription.index')->with('success', 'Subscription deleted successfully.');
    }
}
