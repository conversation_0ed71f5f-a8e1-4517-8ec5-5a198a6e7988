<?php

namespace App\services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class OtpService
{
    private string $javaBackendUrl = 'http://localhost:8080/api/otp';

    /**
     * @throws Exception
     */
    public function generateOtp($phoneNumber)
    {
        Log::info('Generating OTP for ' . $phoneNumber);

        if (!str_starts_with($phoneNumber, '00222')) {
            $phoneNumber = '00222' . $phoneNumber;
        }

        $response = Http::post("{$this->javaBackendUrl}/generate?phoneNumber={$phoneNumber}");

        if ($response->successful()) {
            $otp = $response->json()['otp'];
            Cache::put($phoneNumber, $otp, now()->addMinutes(5));


    // Log::info('Generating OTP  ' . $otp);

            return $otp;
        }

        // Log the response for debugging
        Log::error('Failed to generate OTP', ['response' => $response->body()]);

        throw new Exception('Failed to generate OTP', $response->status());
    }

    public function verifyOtp($phoneNumber, $otp): bool
    {
        if (!str_starts_with($phoneNumber, '00222')) {
            $phoneNumber = '00222' . $phoneNumber;
        }

        Log::info('Verifying OTP for ' . $phoneNumber);
        Log::info('OTP: ' . $otp);

        $cachedOtp = Cache::get($phoneNumber);

        Log::info('Cached OTP: ' . $cachedOtp);

        if ($cachedOtp && $cachedOtp == $otp) {
            Cache::forget($phoneNumber);
            return true;
        }

        return false;
    }
}
