<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       
        Schema::table('discution', function (Blueprint $table) {
            $table->unsignedBigInteger('store_id')->nullable();
            $table->foreign('store_id')->references('id')->on('store');
        });

        Schema::table('notifications', function (Blueprint $table) {
            $table->text('title_en')->nullable()->default('');
            $table->text('title_fr')->nullable()->default('');
            $table->text('title_ar')->nullable()->default('');
            $table->text('message_en')->nullable()->default('');
            $table->text('message_fr')->nullable()->default('');
            $table->text('message_ar')->nullable()->default('');
            $table->json('params')->nullable();
            // Drop the old columns if you want
            $table->dropColumn(['title', 'message']);

            // Add discussion_id for linking to discussions
            $table->unsignedBigInteger('discussion_id')->nullable();
            $table->foreign('discussion_id')->references('id')->on('discution')->onDelete('cascade');


                // Add payment-related fields
                $table->unsignedBigInteger('payment_id')->nullable();
                $table->unsignedBigInteger('order_id')->nullable();
                
                // Add foreign key constraints
                $table->foreign('payment_id')
                      ->references('id')
                      ->on('payment_proofs')
                      ->onDelete('cascade');
                $table->foreign('order_id')
                      ->references('id')
                      ->on('order')
                      ->onDelete('cascade');

                      $table->unsignedBigInteger('item_id')->nullable();
                      $table->foreign('item_id')
                      ->references('id')
                      ->on('item')
                      ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
