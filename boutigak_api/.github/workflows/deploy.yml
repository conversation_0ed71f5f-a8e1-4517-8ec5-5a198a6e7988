name: Deploy to Production Servers

on:
  push:
    branches: [ production ]
  workflow_dispatch:
    inputs:
      skip_build:
        description: 'Skip asset build'
        required: false
        default: false
        type: boolean

jobs:
  deploy-server1:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production'

    steps:
    - uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, pgsql, zip, gd, redis
        coverage: none

    - name: Install Dependencies
      run: |
        composer install --no-dev --optimize-autoloader --ignore-platform-reqs || \
        composer update --no-dev --optimize-autoloader

    - name: Build Assets
      if: ${{ !inputs.skip_build }}
      run: |
        npm ci
        npm run build

    - name: Create deployment archive
      run: |
        tar --warning=no-file-changed -czf deployment.tar.gz \
          --exclude='.git' \
          --exclude='.github' \
          --exclude='node_modules' \
          --exclude='tests' \
          --exclude='*.md' \
          --exclude='storage/logs/*' \
          --exclude='storage/framework/cache/*' \
          --exclude='storage/framework/sessions/*' \
          --exclude='storage/framework/views/*' \
          . || [ $? -eq 1 ]

    - name: Deploy to App Server 1 (Direct PHP)
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🚀 Starting deployment on App Server 1 (Direct PHP)"

          # Create backup
          if [ -d "/var/www/html/boutigak_api" ]; then
            cp -r /var/www/html/boutigak_api /var/www/html/boutigak_api_backup_$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
          fi

          # Create deployment directory
          mkdir -p /var/www/html/boutigak_api

    - name: Upload files to App Server 1
      uses: appleboy/scp-action@v0.1.7
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Extract and configure App Server 1
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "**************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          set -e

          echo "📦 Extracting files on App Server 1..."
          cd /var/www/html/boutigak_api
          tar -xzf /tmp/deployment.tar.gz
          rm /tmp/deployment.tar.gz

          # Create necessary directories
          mkdir -p storage/framework/{cache,sessions,views}
          mkdir -p storage/logs
          mkdir -p storage/app/public
          mkdir -p bootstrap/cache

          # Set permissions
          echo "🔒 Setting file permissions..."
          chown -R www-data:www-data /var/www/html/boutigak_api
          chmod -R 755 /var/www/html/boutigak_api
          chmod -R 775 storage
          chmod -R 775 bootstrap/cache

          # Laravel optimization commands
          # Check for duplicate route names before caching
          php artisan route:list --json > /tmp/routes.json 2>/dev/null || echo "Route listing skipped"
          echo "🔧 Running Laravel optimization..."
          php artisan config:cache
          php artisan route:cache || echo "Route cache failed - continuing with uncached routes"
          php artisan view:cache
          php artisan storage:link || echo "Storage link already exists"

          # Clear old caches
          echo "🧹 Clearing old caches..."
          php artisan cache:clear
          php artisan config:clear
          php artisan route:clear
          php artisan view:clear

          # Restart services
          echo "🔄 Restarting web services..."
          systemctl reload apache2 || systemctl reload nginx || echo "Web server reload completed"
          systemctl restart php8.1-fpm || systemctl restart php-fpm || echo "PHP-FPM restart completed"

          # Health check
          echo "🏥 Performing health check..."
          if curl -f http://localhost/health || curl -f http://localhost; then
            echo "✅ App Server 1 is responding"
          else
            echo "⚠️ App Server 1 health check failed, but deployment completed"
          fi

          echo "✅ App Server 1 deployment completed successfully!"

  deploy-server2:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production'

    steps:
    - uses: actions/checkout@v4

    - name: Create deployment archive for Docker
      run: |
        tar --warning=no-file-changed -czf deployment.tar.gz \
          --exclude='.git' \
          --exclude='.github' \
          --exclude='node_modules' \
          --exclude='tests' \
          --exclude='*.md' \
          --exclude='storage/logs/*' \
          --exclude='storage/framework/cache/*' \
          --exclude='storage/framework/sessions/*' \
          --exclude='storage/framework/views/*' \
          . || [ $? -eq 1 ]

    - name: Deploy to App Server 2 (Docker)
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          echo "🚀 Starting deployment on App Server 2 (Docker)"

          # Create backup
          if [ -d "/var/www/boutigak_api" ]; then
            cp -r /var/www/boutigak_api /var/www/boutigak_api_backup_$(date +%Y%m%d_%H%M%S)
            echo "📦 Created backup"
          fi

          # Create deployment directory
          mkdir -p /var/www/boutigak_api

    - name: Upload files to App Server 2
      uses: appleboy/scp-action@v0.1.7
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Extract and configure App Server 2 (Docker)
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: "************"
        username: root
        password: ${{ secrets.SERVER_PASSWORD }}
        port: 22
        script: |
          set -e

          echo "📦 Extracting files on App Server 2..."
          cd /var/www/boutigak_api
          tar -xzf /tmp/deployment.tar.gz
          rm /tmp/deployment.tar.gz

          # Set permissions for Docker
          echo "🔒 Setting file permissions..."
          chmod -R 755 /var/www/boutigak_api

          # Stop existing containers
          echo "🛑 Stopping existing containers..."
          docker compose down || echo "No containers to stop"

          # Remove old images to force rebuild
          echo "🗑️ Cleaning up old images..."
          docker image prune -f || echo "Image cleanup completed"

          # Build containers without cache
          echo "🐳 Building Docker containers..."
          docker compose build --pull

          # Start containers
          echo "🚀 Starting Docker containers..."
          docker compose up -d

          # Wait for containers to be ready
          echo "⏳ Waiting for containers to be ready..."
          sleep 30

          # Check container status
          echo "📊 Checking container status..."
          docker compose ps

          # Execute Laravel commands inside container
          echo "🔧 Running Laravel optimization in container..."
          docker compose exec -T boutigak_server php artisan config:cache || echo "Config cache completed"
          docker compose exec -T boutigak_server php artisan route:cache || echo "Route cache completed"
          docker compose exec -T boutigak_server php artisan view:cache || echo "View cache completed"
          docker compose exec -T boutigak_server php artisan storage:link || echo "Storage link already exists"

          # Verify GD extension and JPEG support
          echo "🖼️ Verifying GD extension and JPEG support..."
          docker compose exec -T boutigak_server php -m | grep -i gd || echo "GD extension check"
          docker compose exec -T boutigak_server php -r "echo 'JPEG support: ' . (function_exists('imagecreatefromjpeg') ? 'YES' : 'NO') . PHP_EOL;"
          docker compose exec -T boutigak_server php -r "echo 'PNG support: ' . (function_exists('imagecreatefrompng') ? 'YES' : 'NO') . PHP_EOL;"
          docker compose exec -T boutigak_server php -r "echo 'WebP support: ' . (function_exists('imagecreatefromwebp') ? 'YES' : 'NO') . PHP_EOL;"

          # Clear old caches in container
          echo "🧹 Clearing old caches in container..."
          docker compose exec -T boutigak_server php artisan cache:clear || echo "Cache clear completed"
          docker compose exec -T boutigak_server php artisan config:clear || echo "Config clear completed"
          docker compose exec -T boutigak_server php artisan route:clear || echo "Route clear completed"
          docker compose exec -T boutigak_server php artisan view:clear || echo "View clear completed"

          chmod -R 777 storage/

          # Verify deployment and health check
          echo "✅ Verifying Docker deployment..."
          if docker compose exec -T boutigak_server php artisan --version; then
            echo "✅ Laravel is running properly in container"
            # Additional health check
            echo "🏥 Performing container health check..."
            if docker compose exec -T boutigak_server curl -f http://localhost || echo "Health endpoint check completed"; then
              echo "✅ App Server 2 (Docker) deployment completed successfully!"
            else
              echo "⚠️ App Server 2 health check warning, but deployment completed"
            fi
          else
            echo "❌ Docker deployment verification failed"
            echo "📋 Container logs:"
            docker compose logs boutigak_server
            exit 1
          fi

  notify:
    needs: [deploy-server1, deploy-server2]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify deployment status
      run: |
        echo "📋 Deployment Summary"
        echo "===================="
        echo "Server 1 (Direct PHP): ${{ needs.deploy-server1.result }}"
        echo "Server 2 (Docker): ${{ needs.deploy-server2.result }}"

        if [ "${{ needs.deploy-server1.result }}" == "success" ] && [ "${{ needs.deploy-server2.result }}" == "success" ]; then
          echo "✅ Deployment successful to both servers"
        else
          echo "❌ Deployment failed on one or more servers"
          exit 1
        fi