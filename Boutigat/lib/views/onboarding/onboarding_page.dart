
// Contrôleur pour gérer l'index des pages d'onboarding

import 'package:boutigak/constants/app_colors.dart';
import 'package:lottie/lottie.dart';
import 'package:boutigak/views/widgets/navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
// SplashScreen pour vérifier si l'onboarding a été vu
// class SplashScreen extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     // Vérification de l'état de l'onboarding
//     checkOnboardingStatus();
//     return Scaffold(
//       body: Center(
//         child: CircularProgressIndicator(), // Indicateur de chargement pendant la vérification
//       ),
//     );
//   }

//   // Méthode pour vérifier si l'onboarding a déjà été vu
//   void checkOnboardingStatus() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     bool? onboardingSeen = prefs.getBool('onboardingSeen') ?? false;

//     // Si l'onboarding a été vu, rediriger vers la page principale, sinon afficher l'onboarding
//     if (onboardingSeen) {
//       Get.off(() => const NavigationBarPage());
//     } else {
//       Get.off(() => const OnboardingScreen());
//     }
//   }
// }
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int currentPage = 0;
  final PageController _pageController = PageController();

  // Méthode pour avancer à la page suivante avec boucle
  void nextPage() {
    setState(() {
      currentPage = (currentPage + 1) % 3; // Boucler entre 0 et 2
      _pageController.animateToPage(
        currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  // Méthode pour revenir à la page précédente avec boucle
  void previousPage() {
    setState(() {
      currentPage = (currentPage - 1 + 3) % 3; // Boucler entre 0 et 2
      _pageController.animateToPage(
        currentPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  // Marquer l'onboarding comme complété
  void completeOnboarding() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboardingSeen', true);
    Get.off(() => const NavigationBarPage());
  }

  // Ajout du bouton "Skip"
  Widget buildSkipButton() {
    return Positioned(
      top: 40,
      right: 20,
      child: TextButton(
        onPressed: completeOnboarding,
        child: const Text(
          'Skip',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  // Ajout de l'indicateur de page sous forme de ligne
  Widget buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          height: 8,
          width: currentPage == index ? 60 : 20, // Taille en fonction de la page active
          decoration: BoxDecoration(
            color: currentPage == index ? AppColors.surface : Colors.grey, // Couleur de la page active
            borderRadius: BorderRadius.circular(8),
          ),
        );
      }),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Fond vert dans la moitié inférieure de l'écran avec vague
          Positioned.fill(
  child: Stack(
    children: [
      // Partie inférieure avec le fond de couleur
      Column(
        children: [
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xFFFBE4D8), // Fond de couleur
              ),
            ),
          ),
        ],
      ),
      // CustomPaint pour le dessin de la vague au-dessus du Container
      Positioned.fill(
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: WavePainter(),
        ),
      ),
    ],
  ),
),
          // Contenu principal par-dessus le fond
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      currentPage = index;
                    });
                  },
                  children: const [
                    OnboardingPage(
                      frenchText: 'Achetez et vendez des articles uniques en toute simplicité !',
                      englishText: 'Buy and sell unique items with ease!',
                      arabicText: '! اشترِ وبِع منتجات فريدة بكل سهولة',
                      lottieAnimationPath: 'assets/lottie/handshake.json',
                    ),
                    OnboardingPage(
                      frenchText: 'Créez votre boutique en ligne en quelques clics !',
                      englishText: 'Create your online store in just a few clicks!',
                      arabicText: '! أنشئ متجرك الإلكتروني في بضع نقرات',
                      lottieAnimationPath: 'assets/lottie/oldshop.json',
                    ),
                    OnboardingPage(
                      frenchText: 'Faites votre shopping sans quitter votre maison.',
                      englishText: 'Shop without leaving your home.',
                      arabicText: '. تسوق من دون مغادرة منزلك',
                      lottieAnimationPath: 'assets/lottie/basketfill.json',
                    ),
                  ],
                ),
              ),
              // Indicateur de page
              
              // Boutons de navigation
              Padding(
                padding: const EdgeInsets.all(25.0),
                child: Row(
                  
                  children: [
                    buildPageIndicator(),
                    Spacer(),
                      Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.8), // Fond blanc légèrement transparent
      ),
      child: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: previousPage,
      ),
    ),
    const SizedBox(width: 20),
    // Bouton suivant avec fond blanc circulaire et légèrement transparent
    Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.8), // Fond blanc légèrement transparent
      ),
      child: IconButton(
        icon: const Icon(Icons.arrow_forward),
        onPressed: nextPage,
      ),
    ),
                  ],
                ),
              ),
              const SizedBox(height: 19),
            ],
          ),
          // Bouton "Skip"
          buildSkipButton(),
        ],
      ),
    );
  }
}
// Peintre personnalisé pour dessiner des vagues différentes en fonction de la page actuelle
class WavePainter extends CustomPainter {
  // Page actuelle



  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.fill;

    Path path = Path();
    
    // Crée une forme de vague plus complexe et plus large en fonction de la page
    
        path.lineTo(0, size.height * 0.4); // La vague commence plus bas
        path.quadraticBezierTo(size.width * 0.3, size.height * 0.5, size.width * 0.5, size.height * 0.4);
        path.quadraticBezierTo(size.width * 0.75, size.height * 0.30, size.width, size.height * 0.47);
       

    // Fermer le chemin et dessiner le bas de l'écran
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    // Dessiner le chemin sur le canvas
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true; // Repeindre chaque fois que la page change
  }
}


// Widget pour chaque page d'onboarding avec trois langues
class OnboardingPage extends StatelessWidget {
  final String frenchText;
  final String englishText;
  final String arabicText;
  final String lottieAnimationPath;

  const OnboardingPage({
    Key? key,
    required this.frenchText,
    required this.englishText,
    required this.arabicText,
    required this.lottieAnimationPath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 80.0,right:  16 , left: 16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // Animation Lottie
            
              
              
            
             Row(
               children: [
                Spacer(),
                 Lottie.asset(
                  lottieAnimationPath,
                  width: 200,
                  height: 200,
                  fit: BoxFit.cover,
                             ),
               ],
             ),

            const SizedBox(height: 30),
            Text(
              frenchText,
              
             style: const TextStyle(fontSize: 18 , color: AppColors.onSurface, fontWeight: AppFontWeights.bold),
            ),
            const SizedBox(height: 50),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SizedBox(width: 10),
                Text(
                  arabicText,
                 style: const TextStyle(fontSize: 18 , color: AppColors.surface, fontWeight: AppFontWeights.bold),
                ),
                
              ],
            ),
             const SizedBox(height: 40),
            Text(
              englishText,
              
              style: const TextStyle(fontSize: 18 , color: AppColors.surface, fontWeight: AppFontWeights.bold),
            ),
           
           
            const SizedBox(height: 40),
            Row(
                
                 children: [
                  
                   Image.asset(
                    'assets/images/logo.png',
                    width: 150,
                    height: 150,
                    color: AppColors.surface,
                                 ),
                 ],
               ),
          ],
        ),
      ),
    );
  }
}