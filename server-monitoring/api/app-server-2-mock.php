<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Mock data for App Server 2
$mockData = [
    'status' => 'online',
    'cpu' => rand(15, 35),
    'ram' => rand(35, 65),
    'disk' => rand(25, 55),
    'uptime' => 'up 12 hours, 45 minutes',
    'loadAverage' => '0.42, 0.38, 0.31',
    'networkIO' => [
        'rx_bytes' => 189234567,
        'tx_bytes' => 35678901
    ],
    'diskIO' => [
        'read_kb_s' => 0.02,
        'write_kb_s' => 0.01
    ],
    'phpProcesses' => 4,
    'nginxStatus' => 'running',
    'laravelLogs' => 0,
    'queueJobs' => [
        'pending' => rand(0, 5),
        'processing' => rand(0, 2),
        'failed' => 0
    ],
    'cacheStats' => [
        'hit_rate' => '92%',
        'memory_usage' => '38%',
        'keys' => 980
    ],
    'sessionCount' => rand(8, 30),
    'errorRate' => '0.2%',
    'responseTime' => rand(60, 180) . 'ms',
    'timestamp' => time()
];

echo json_encode($mockData);
?>
