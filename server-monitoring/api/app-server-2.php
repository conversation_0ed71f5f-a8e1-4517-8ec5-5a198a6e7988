<?php
// Set maximum execution time to 10 seconds
set_time_limit(10);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// App Server 2 configuration
$appServer2 = [
    'host' => '************',
    'port' => 22,
    'username' => 'root',
    'password' => 'Boutigak@36666688@38407840',
    'laravel_path' => '/var/www/boutigak_api',
    'server_name' => 'App Server 2'
];

class AppServer2Monitor {
    private $serverConfig;

    public function __construct($config) {
        $this->serverConfig = $config;
    }

    public function getAppServerMetrics() {
        $data = [
            'status' => $this->getServerStatus(),
            'cpu' => $this->getCpuUsage(),
            'ram' => $this->getRamUsage(),
            'disk' => $this->getDiskUsage(),
            'uptime' => $this->getUptime(),
            'loadAverage' => $this->getLoadAverage(),
            'networkIO' => $this->getNetworkIO(),
            'diskIO' => $this->getDiskIO(),
            'phpProcesses' => $this->getPhpProcesses(),
            'nginxStatus' => $this->getNginxStatus(),
            'laravelLogs' => $this->getLaravelLogs(),
            'queueJobs' => $this->getQueueJobs(),
            'cacheStats' => $this->getCacheStats(),
            'sessionCount' => $this->getSessionCount(),
            'errorRate' => $this->getErrorRate(),
            'responseTime' => $this->getAverageResponseTime(),
            'timestamp' => time()
        ];

        return $data;
    }

    private function getServerStatus() {
        try {
            // Try to ping the server
            $pingResult = $this->executeRemoteCommand('echo "alive"');

            if ($pingResult !== false && trim($pingResult) === 'alive') {
                return 'online';
            }

            return 'warning';

        } catch (Exception $e) {
            error_log("App server status check failed: " . $e->getMessage());
            return 'offline';
        }
    }

    private function getCpuUsage() {
        try {
            $command = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | awk -F'%' '{print $1}'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (float)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting CPU usage: " . $e->getMessage());
            return 0;
        }
    }

    private function getRamUsage() {
        try {
            $command = "free | grep Mem | awk '{printf \"%.1f\", $3/$2 * 100.0}'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (float)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting RAM usage: " . $e->getMessage());
            return 0;
        }
    }

    private function getDiskUsage() {
        try {
            $command = "df -h / | awk 'NR==2{print $5}' | sed 's/%//'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (int)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting disk usage: " . $e->getMessage());
            return 0;
        }
    }

    private function getUptime() {
        try {
            $command = "uptime -p";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return trim($result);
            }

            return 'unknown';

        } catch (Exception $e) {
            error_log("Error getting uptime: " . $e->getMessage());
            return 'unknown';
        }
    }

    private function getLoadAverage() {
        try {
            $command = "uptime | awk -F'load average:' '{print $2}'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return trim($result);
            }

            return 'unknown';

        } catch (Exception $e) {
            error_log("Error getting load average: " . $e->getMessage());
            return 'unknown';
        }
    }

    private function getNetworkIO() {
        try {
            $command = "cat /proc/net/dev | grep eth0 | awk '{print $2,$10}'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                $parts = explode(' ', trim($result));
                return [
                    'rx_bytes' => isset($parts[0]) ? (int)$parts[0] : 0,
                    'tx_bytes' => isset($parts[1]) ? (int)$parts[1] : 0
                ];
            }

            return ['rx_bytes' => 0, 'tx_bytes' => 0];

        } catch (Exception $e) {
            error_log("Error getting network I/O: " . $e->getMessage());
            return ['rx_bytes' => 0, 'tx_bytes' => 0];
        }
    }

    private function getDiskIO() {
        try {
            $command = "iostat -d 1 1 | tail -n +4 | head -n 1 | awk '{print $3,$4}'";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                $parts = explode(' ', trim($result));
                return [
                    'read_kb_s' => isset($parts[0]) ? (float)$parts[0] : 0,
                    'write_kb_s' => isset($parts[1]) ? (float)$parts[1] : 0
                ];
            }

            return ['read_kb_s' => 0, 'write_kb_s' => 0];

        } catch (Exception $e) {
            error_log("Error getting disk I/O: " . $e->getMessage());
            return ['read_kb_s' => 0, 'write_kb_s' => 0];
        }
    }

    private function getPhpProcesses() {
        try {
            $command = "ps aux | grep php | grep -v grep | wc -l";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (int)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting PHP processes: " . $e->getMessage());
            return 0;
        }
    }

    private function getNginxStatus() {
        try {
            $command = "systemctl is-active nginx";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false && trim($result) === 'active') {
                return 'running';
            }

            return 'stopped';

        } catch (Exception $e) {
            error_log("Error getting Nginx status: " . $e->getMessage());
            return 'unknown';
        }
    }

    private function getLaravelLogs() {
        try {
            $logPath = $this->serverConfig['laravel_path'] . '/storage/logs/laravel.log';
            $command = "tail -n 100 $logPath | grep ERROR | wc -l";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (int)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting Laravel logs: " . $e->getMessage());
            return 0;
        }
    }

    private function getQueueJobs() {
        try {
            // This would require connecting to Redis or database to check queue
            // For now, return mock data
            return [
                'pending' => 0,
                'processing' => 0,
                'failed' => 0
            ];

        } catch (Exception $e) {
            error_log("Error getting queue jobs: " . $e->getMessage());
            return ['pending' => 0, 'processing' => 0, 'failed' => 0];
        }
    }

    private function getCacheStats() {
        try {
            // This would require connecting to Redis to get cache stats
            // For now, return mock data
            return [
                'hit_rate' => '0%',
                'memory_usage' => '0%',
                'keys' => 0
            ];

        } catch (Exception $e) {
            error_log("Error getting cache stats: " . $e->getMessage());
            return ['hit_rate' => '0%', 'memory_usage' => '0%', 'keys' => 0];
        }
    }

    private function getSessionCount() {
        try {
            // Count session files or Redis keys
            $sessionPath = $this->serverConfig['laravel_path'] . '/storage/framework/sessions';
            $command = "find $sessionPath -name 'sess_*' | wc -l";
            $result = $this->executeRemoteCommand($command);

            if ($result !== false) {
                return (int)trim($result);
            }

            return 0;

        } catch (Exception $e) {
            error_log("Error getting session count: " . $e->getMessage());
            return 0;
        }
    }

    private function getErrorRate() {
        try {
            // Parse access logs for error rates
            $command = "tail -n 1000 /var/log/nginx/access.log | grep ' 5[0-9][0-9] ' | wc -l";
            $errors = $this->executeRemoteCommand($command);

            if ($errors !== false) {
                $errorCount = (int)trim($errors);
                $errorRate = ($errorCount / 1000) * 100;
                return round($errorRate, 2) . '%';
            }

            return '0%';

        } catch (Exception $e) {
            error_log("Error getting error rate: " . $e->getMessage());
            return '0%';
        }
    }

    private function getAverageResponseTime() {
        try {
            // This would require parsing access logs with response times
            // For now, return mock data
            return '0ms';

        } catch (Exception $e) {
            error_log("Error getting response time: " . $e->getMessage());
            return '0ms';
        }
    }

    private function executeRemoteCommand($command) {
        if (!function_exists('ssh2_connect')) {
            return false;
        }

        try {
            // Set timeout for SSH connection
            $connection = ssh2_connect($this->serverConfig['host'], $this->serverConfig['port'], null, [
                'timeout' => 5 // 5 second timeout
            ]);

            if (!$connection) {
                return false;
            }

            if (!ssh2_auth_password($connection, $this->serverConfig['username'], $this->serverConfig['password'])) {
                return false;
            }

            $stream = ssh2_exec($connection, $command);
            if (!$stream) {
                return false;
            }

            stream_set_blocking($stream, true);

            // Set timeout for reading
            stream_set_timeout($stream, 5);

            $result = stream_get_contents($stream);
            fclose($stream);

            return $result;

        } catch (Exception $e) {
            error_log("SSH connection error: " . $e->getMessage());
            return false;
        }
    }
}

try {
    $monitor = new AppServer2Monitor($appServer2);
    $data = $monitor->getAppServerMetrics();

    echo json_encode($data);

} catch (Exception $e) {
    error_log("App Server 2 API error: " . $e->getMessage());

    // Return error data if there's an error
    echo json_encode([
        'status' => 'offline',
        'cpu' => 0,
        'ram' => 0,
        'disk' => 0,
        'uptime' => 'unknown',
        'loadAverage' => 'unknown',
        'networkIO' => ['rx_bytes' => 0, 'tx_bytes' => 0],
        'diskIO' => ['read_kb_s' => 0, 'write_kb_s' => 0],
        'phpProcesses' => 0,
        'nginxStatus' => 'unknown',
        'laravelLogs' => 0,
        'queueJobs' => ['pending' => 0, 'processing' => 0, 'failed' => 0],
        'cacheStats' => ['hit_rate' => '0%', 'memory_usage' => '0%', 'keys' => 0],
        'sessionCount' => 0,
        'errorRate' => '0%',
        'responseTime' => '0ms',
        'timestamp' => time(),
        'error' => 'Connection failed'
    ]);
}
?>
