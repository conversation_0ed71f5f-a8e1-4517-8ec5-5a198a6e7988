<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemPromotion extends Model
{
    protected $table = 'item_promotion';

    protected $fillable = [
        'percentage',
        'item_id',
        'start_date',
        'end_date',
        
    ];

    protected $dates = [
        'start_date',
        'end_date',
        
        'created_at',
        'updated_at',
    ];

    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
