<?php

namespace Database\Seeders;

use App\Models\EPaymentProvider;
use Illuminate\Database\Seeder;

class EPaymentProviderSeeder extends Seeder
{
    public function run()
    {


        EPaymentProvider::truncate();
        $providers = [
            [
                'name' => 'BANKILY',
                'provider_code' => '01993',
                'has_api' => true,
                'is_active' => true,
                'logo' => 'payment_providers/bankily.png',
                'config' => [
                    'base_url' => env('BPAY_BASE_URL', 'https://ebankily-tst.appspot.com'),
                    'client_id' => env('BPAY_CLIENT_ID', 'ebankily'),
                    'username' => env('BPAY_USERNAME'),
                    'password' => env('BPAY_PASSWORD'),
                ]
            ],
            [
                'name' => 'BIMBANK',
                'provider_code' => '01994',
                'has_api' => false,
                'is_active' => true,
                'logo' => 'payment_providers/bim_bank.png',
            ],
            [
                'name' => 'MASRVI',
                'provider_code' => '01995',
                'has_api' => false,
                'is_active' => true,
                'logo' => 'payment_providers/Masrvi.png',
            ],
            [
                'name' => 'SEDAD',
                'provider_code' => '01996',
                'has_api' => false,
                'is_active' => true,
                'logo' => 'payment_providers/sedad.jpg',
            ],
            [
                'name' => 'BCI PAY',
                'provider_code' => '01997',
                'has_api' => false,
                'is_active' => true,
                'logo' => 'payment_providers/bci.jpeg',
            ],
            [
                'name' => 'CLICK',
                'provider_code' => '01998',
                'has_api' => false,
                'is_active' => true,
                'logo' => 'payment_providers/click.png',
            ],
            // [
            //     'name' => 'Amanty BEA',
            //     'provider_code' => '019323',
            //     'has_api' => false,
            //     'is_active' => true,
            //     'logo' => 'payment_providers/amanty-bea.png',
            // ],
            // [
            //     'name' => 'Barid Cash',
            //     'provider_code' => '0199323',
            //     'has_api' => false,
            //     'is_active' => true,
            //     'logo' => 'payment_providers/barid-cash.png',
            // ],
            // [
            //     'name' => 'BAMIS',
            //     'provider_code' => '0193293',
            //     'has_api' => false,
            //     'is_active' => true,
            //     'logo' => 'payment_providers/bamis.png',
            // ]
        ];

        foreach ($providers as $provider) {
            EPaymentProvider::updateOrCreate(
                ['name' => $provider['name']],
                $provider
            );
        }
    }
}