<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CategoryDetail;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/categories",
     *     tags={"Categories"},
     *     summary="Create a new category",
     *     description="Create a new category with optional parent and details",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title_en", "title_fr", "title_ar"},
     *             @OA\Property(property="title_en", type="string", example="Electronics"),
     *             @OA\Property(property="title_fr", type="string", example="Électronique"),
     *             @OA\Property(property="title_ar", type="string", example="إلكترونيات"),
     *             @OA\Property(property="parent_id", type="integer", example=1, nullable=true),
     *             @OA\Property(
     *                 property="details",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     required={"label_en", "label_fr", "label_ar"},
     *                     @OA\Property(property="label_en", type="string", example="Brand"),
     *                     @OA\Property(property="label_fr", type="string", example="Marque"),
     *                     @OA\Property(property="label_ar", type="string", example="ماركة")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Category created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Category created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="category",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title_en", type="string", example="Electronics"),
     *                     @OA\Property(property="title_fr", type="string", example="Électronique"),
     *                     @OA\Property(property="title_ar", type="string", example="إلكترونيات"),
     *                     @OA\Property(property="parent_id", type="integer", example=1, nullable=true),
     *                     @OA\Property(
     *                         property="details",
     *                         type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=1),
     *                             @OA\Property(property="category_id", type="integer", example=1),
     *                             @OA\Property(property="label_en", type="string", example="Brand"),
     *                             @OA\Property(property="label_fr", type="string", example="Marque"),
     *                             @OA\Property(property="label_ar", type="string", example="ماركة")
     *                         )
     *                     ),
     *                     @OA\Property(
     *                         property="parent",
     *                         type="object",
     *                         nullable=true,
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="title_en", type="string", example="Parent Category"),
     *                         @OA\Property(property="title_fr", type="string", example="Catégorie Parent"),
     *                         @OA\Property(property="title_ar", type="string", example="الفئة الأم")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="title_en",
     *                     type="array",
     *                     @OA\Items(type="string", example="The title en field is required.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Failed to create category"),
     *             @OA\Property(property="error", type="string", example="Error message details")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'title_en' => 'required|string|max:255',
            'title_fr' => 'required|string|max:255',
            'title_ar' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:category,id',
            'details' => 'nullable|array',
            'details.*.label_en' => 'required|string|max:255',
            'details.*.label_fr' => 'required|string|max:255',
            'details.*.label_ar' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Create the category
            $category = Category::create([
                'title_en' => $validated['title_en'],
                'title_fr' => $validated['title_fr'],
                'title_ar' => $validated['title_ar'],
                'parent_id' => $validated['parent_id'] ?? null,
            ]);

            // Create category details if provided
            if (!empty($validated['details'])) {
                foreach ($validated['details'] as $detail) {
                    CategoryDetail::create([
                        'category_id' => $category->id,
                        'label_en' => $detail['label_en'],
                        'label_fr' => $detail['label_fr'],
                        'label_ar' => $detail['label_ar'],
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Category created successfully',
                'data' => [
                    'category' => $category->load('details', 'parent')
                ]
            ], 201);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create category: ' . $e->getMessage());

            return response()->json([
                'message' => 'Failed to create category',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function getAll()
    {
        $categories = Category::with(['children.details', 'details'])->get();

        return response()->json($categories);
    }

    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $data = Category::with('parent')->get();
            return DataTables::of($data)
                ->addColumn('parent', function ($row) {
                    return $row->parent ? $row->parent->title_en : 'N/A';
                })
                ->addColumn('actions', function ($row) {
                    return '<a href="' . route('categories.show', $row->id) . '" class="btn btn-primary btn-sm me-1">Show</a>
            <a href="' . route('categories.edit', $row->id) . '" class="btn btn-warning btn-sm me-1">Edit</a>
            <button type="button" class="delete btn btn-danger btn-sm" data-id="' . $row->id . '">
                <i class="fa fa-trash"></i> Delete
            </button>';
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('backoffice.category.index');
    }

    public function create(): \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|\Laravel\Lumen\Application
    {
        $categories = Category::all();
        return view('backoffice.category.create', compact('categories'));
    }

    public function edit($id): \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|\Laravel\Lumen\Application
    {
        $category = Category::with('details')->find($id);
        $categories = Category::all();
        return view('backoffice.category.edit', compact('category', 'categories'));
    }

    public function show($id): \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|\Laravel\Lumen\Application
    {
        $category = Category::with('details')->find($id);
        return view('backoffice.category.show', compact('category'));
    }

    public function storeBo(Request $request): \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|\Laravel\Lumen\Application
    {
        $validated = $request->validate([
            'title_en' => 'required|string|max:255',
            'title_fr' => 'required|string|max:255',
            'title_ar' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:category,id',
            'details' => 'nullable|array',
            'details.*.label_en' => 'required|string|max:255',
            'details.*.label_fr' => 'required|string|max:255',
            'details.*.label_ar' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $category = Category::query()->updateOrCreate(['id' => $request->id], [
                'title_en' => $validated['title_en'],
                'title_fr' => $validated['title_fr'],
                'title_ar' => $validated['title_ar'],
                'parent_id' => $validated['parent_id'] ?? null,
            ]);

            if (!empty($validated['details'])) {
                foreach ($validated['details'] as $detail) {
                    CategoryDetail::query()->updateOrCreate(
                        ['id' => $detail['id'] ?? null],
                        [
                            'category_id' => $category->id,
                            'label_en' => $detail['label_en'],
                            'label_fr' => $detail['label_fr'],
                            'label_ar' => $detail['label_ar'],
                        ]
                    );
                }
            }

            DB::commit();

            return view('backoffice.category.index');
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to save category: ' . $e->getMessage());
            return view('backoffice.category.create');
        }
    }

    public function destroy($id): \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Foundation\Application|\Illuminate\View\View|\Laravel\Lumen\Application
    {
        try {
            Category::query()->find($id)->delete();
            return view('backoffice.category.index');
        } catch (Exception $e) {
            Log::error('Failed to delete category: ' . $e->getMessage());
            return view('backoffice.category.index');
        }
    }
}
