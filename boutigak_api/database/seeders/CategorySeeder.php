<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\CategoryDetail;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CategorySeeder extends Seeder
{
    public function run()
    {
        DB::table('category_detail')->truncate();
        DB::table('category')->truncate();

        $this->seedCategories();
    }

   
    private function seedCategories()
{
    $mainCategories = [
        ['en' => 'Electronics', 'fr' => 'Électronique', 'ar' => 'إلكترونيات'],
        ['en' => 'Home Appliances', 'fr' => 'Appareils ménagers', 'ar' => 'أجهزة منزلية'],
        ['en' => 'Books', 'fr' => 'Livres', 'ar' => 'كتب'],
        ['en' => 'Toys', 'fr' => 'Jouets', 'ar' => 'ألعاب'],
        ['en' => 'Sports & Outdoors', 'fr' => 'Sports et plein air', 'ar' => 'رياضة وهواء الطلق'],
        ['en' => 'Health & Wellness', 'fr' => 'Santé et bien-être', 'ar' => 'الصحة والعافية'],
        ['en' => 'Furniture', 'fr' => 'Meubles', 'ar' => 'أثاث'],
        ['en' => 'Jewelry', 'fr' => 'Bijoux', 'ar' => 'مجوهرات'],
        ['en' => 'Real Estate', 'fr' => 'Immobilier', 'ar' => 'العقارات'],
        ['en' => 'Automotive', 'fr' => 'Automobile', 'ar' => 'سيارات'],
        ['en' => 'Fashion', 'fr' => 'Mode', 'ar' => 'الموضة'],
        ['en' => 'Pets', 'fr' => 'Animaux', 'ar' => 'الحيوانات الأليفة'],
        ['en' => 'Food & Beverages', 'fr' => 'Nourriture et boissons', 'ar' => 'الطعام والمشروبات'],
        ['en' => 'Video Games', 'fr' => 'Jeux vidéo', 'ar' => 'ألعاب الفيديو'],
        ['en' => 'Musical Instruments', 'fr' => 'Instruments de musique', 'ar' => 'آلات موسيقية'],
        ['en' => 'Baby Products', 'fr' => 'Produits pour bébés', 'ar' => 'منتجات الأطفال'],
        ['en' => 'Tools & DIY', 'fr' => 'Bricolage et outils', 'ar' => 'أدوات وأعمال يدوية'],
        ['en' => 'Office Supplies', 'fr' => 'Fournitures de bureau', 'ar' => 'مستلزمات المكتب'],
        ['en' => 'Computers', 'fr' => 'Informatique', 'ar' => 'أجهزة كمبيوتر'],
        ['en' => 'Home Decor', 'fr' => 'Décoration intérieure', 'ar' => 'ديكور المنزل'],
        ['en' => 'Art & Collectibles', 'fr' => 'Art et objets de collection', 'ar' => 'فن ومقتنيات']
    ];

    $genderRoots = [
        ['en' => 'Men', 'fr' => 'Hommes', 'ar' => 'رجال'],
        ['en' => 'Women', 'fr' => 'Femmes', 'ar' => 'نساء'],
    ];

    $subCategories = [
        'Electronics' => [
            ['en' => 'Smartphones', 'fr' => 'Smartphones', 'ar' => 'هواتف ذكية'],
            ['en' => 'TVs', 'fr' => 'Télévisions', 'ar' => 'تلفزيونات'],
            ['en' => 'Cameras', 'fr' => 'Appareils photo', 'ar' => 'كاميرات'],
            ['en' => 'Computers', 'fr' => 'Ordinateurs', 'ar' => 'أجهزة الكمبيوتر'],
            ['en' => 'Audio', 'fr' => 'Audio', 'ar' => 'أجهزة صوتية'],
            ['en' => 'Wearables', 'fr' => 'Objets connectés', 'ar' => 'أجهزة قابلة للارتداء'],
            ['en' => 'Drones', 'fr' => 'Drones', 'ar' => 'طائرات بدون طيار'],
            ['en' => 'Accessories', 'fr' => 'Accessoires', 'ar' => 'اكسسوارات']
        ],
        'Clothing' => [
            ['en' => 'Men', 'fr' => 'Hommes', 'ar' => 'رجال'],
            ['en' => 'Women', 'fr' => 'Femmes', 'ar' => 'نساء'],
            ['en' => 'Kids', 'fr' => 'Enfants', 'ar' => 'أطفال'],
            ['en' => 'Sportswear', 'fr' => 'Vêtements de sport', 'ar' => 'ملابس رياضية'],
            ['en' => 'Formal Wear', 'fr' => 'Tenue formelle', 'ar' => 'ملابس رسمية'],
            ['en' => 'Casual Wear', 'fr' => 'Vêtements décontractés', 'ar' => 'ملابس كاجوال']
        ],
        'Home Appliances' => [
            ['en' => 'Kitchen', 'fr' => 'Cuisine', 'ar' => 'مطبخ'],
            ['en' => 'Cleaning', 'fr' => 'Nettoyage', 'ar' => 'تنظيف'],
            ['en' => 'Heating & Cooling', 'fr' => 'Chauffage et climatisation', 'ar' => 'تدفئة وتبريد'],
            ['en' => 'Laundry', 'fr' => 'Buanderie', 'ar' => 'غسيل']
        ],
        'Books' => [
            ['en' => 'Fiction', 'fr' => 'Fiction', 'ar' => 'روايات'],
            ['en' => 'Non-fiction', 'fr' => 'Non-fiction', 'ar' => 'غير روائية'],
            ['en' => 'Educational', 'fr' => 'Éducatif', 'ar' => 'تعليمي'],
            ['en' => 'Comics', 'fr' => 'Bandes dessinées', 'ar' => 'قصص مصورة'],
            ['en' => 'Children', 'fr' => 'Enfants', 'ar' => 'أطفال']
        ],
        'Toys' => [
            ['en' => 'Educational', 'fr' => 'Éducatifs', 'ar' => 'ألعاب تعليمية'],
            ['en' => 'Outdoor', 'fr' => 'En plein air', 'ar' => 'خارجية'],
            ['en' => 'Puzzles', 'fr' => 'Puzzles', 'ar' => 'ألعاب ألغاز'],
            ['en' => 'Action Figures', 'fr' => 'Figurines', 'ar' => 'شخصيات ألعاب']
        ],
        'Real Estate' => [
            ['en' => 'Apartment', 'fr' => 'Appartement', 'ar' => 'شقة'],
            ['en' => 'House', 'fr' => 'Maison', 'ar' => 'منزل'],
            ['en' => 'Commercial', 'fr' => 'Commercial', 'ar' => 'تجاري'],
            ['en' => 'Land', 'fr' => 'Terrain', 'ar' => 'أرض'],
        ],
        'Automotive' => [
            ['en' => 'Sedan', 'fr' => 'Berline', 'ar' => 'سيدان'],
            ['en' => 'SUV', 'fr' => 'SUV', 'ar' => 'دفع رباعي'],
            ['en' => 'Truck', 'fr' => 'Camion', 'ar' => 'شاحنة'],
            ['en' => 'Motorcycle', 'fr' => 'Moto', 'ar' => 'دراجة نارية'],
            ['en' => 'Parts & Accessories', 'fr' => 'Pièces et accessoires', 'ar' => 'قطع واكسسوارات'],
            ['en' => 'Electric Vehicles', 'fr' => 'Véhicules électriques', 'ar' => 'سيارات كهربائية'],
        ],
    
        'Sports & Outdoors' => [
            ['en' => 'Fitness', 'fr' => 'Fitness', 'ar' => 'لياقة بدنية'],
    
        ],
        'Beauty & Personal Care' => [
            ['en' => 'Skin Care', 'fr' => 'Soins de la peau', 'ar' => 'العناية بالبشرة'],
            ['en' => 'Perfumes', 'fr' => 'Parfums', 'ar' => 'عطور'],
            ['en' => 'Makeup', 'fr' => 'Maquillage', 'ar' => 'مكياج']
        ],
        'Furniture' => [
            ['en' => 'Living Room', 'fr' => 'Salon', 'ar' => 'غرفة المعيشة'],
            ['en' => 'Bedroom', 'fr' => 'Chambre à coucher', 'ar' => 'غرفة نوم']
        ],
        'Jewelry' => [
            ['en' => 'Necklaces', 'fr' => 'Colliers', 'ar' => 'قلائد'],
            ['en' => 'Rings', 'fr' => 'Bagues', 'ar' => 'خواتم']
        ],
    
    ];
    

    $genderCategories = [
        'Men' => [
            'Clothing' => [
                ['en' => 'Sportswear', 'fr' => 'Vêtements de sport', 'ar' => 'ملابس رياضية'],
                ['en' => 'Casual Wear', 'fr' => 'Vêtements décontractés', 'ar' => 'ملابس كاجوال'],
                ['en' => 'Formal Wear', 'fr' => 'Tenue formelle', 'ar' => 'ملابس رسمية'],
            ],
            'Shoes' => [
                ['en' => 'Sneakers', 'fr' => 'Baskets', 'ar' => 'أحذية رياضية'],
                ['en' => 'Formal Shoes', 'fr' => 'Chaussures habillées', 'ar' => 'أحذية رسمية']
            ],
            'Accessories' => [
                ['en' => 'Belts', 'fr' => 'Ceintures', 'ar' => 'أحزمة'],
                ['en' => 'Sunglasses', 'fr' => 'Lunettes de soleil', 'ar' => 'نظارات شمسية']
            ],
            'Watches' => [],
            'Perfumes' => []
        ],
        'Women' => [
            'Clothing' => [
                ['en' => 'Dresses', 'fr' => 'Robes', 'ar' => 'فساتين'],
                ['en' => 'Tops', 'fr' => 'Hauts', 'ar' => 'قمصان'],
                ['en' => 'Casual Wear', 'fr' => 'Vêtements décontractés', 'ar' => 'ملابس كاجوال'],
                ['en' => 'Formal Wear', 'fr' => 'Tenue formelle', 'ar' => 'ملابس رسمية'],
            ],
            'Shoes' => [
                ['en' => 'Heels', 'fr' => 'Talons', 'ar' => 'كعوب'],
                ['en' => 'Flats', 'fr' => 'Chaussures plates', 'ar' => 'أحذية مسطحة']
            ],
            'Bags' => [],
            'Accessories' => [
                ['en' => 'Belts', 'fr' => 'Ceintures', 'ar' => 'أحزمة'],
                ['en' => 'Jewelry', 'fr' => 'Bijoux', 'ar' => 'مجوهرات'],
                ['en' => 'Sunglasses', 'fr' => 'Lunettes de soleil', 'ar' => 'نظارات شمسية']
            ],
            'Perfumes' => [],
            'Beauty & Personal Care' => []
        ]
    ];

    $categoryDetails = [
        'Clothing' => [
            ['label_en' => 'Size', 'label_fr' => 'Taille', 'label_ar' => 'المقاس'],
            ['label_en' => 'Color', 'label_fr' => 'Couleur', 'label_ar' => 'اللون'],
            ['label_en' => 'Gender', 'label_fr' => 'Genre', 'label_ar' => 'الجنس'],
        ],
        'Books' => [
            ['label_en' => 'Author', 'label_fr' => 'Auteur', 'label_ar' => 'المؤلف'],
            ['label_en' => 'Language', 'label_fr' => 'Langue', 'label_ar' => 'اللغة'],
            ['label_en' => 'Edition', 'label_fr' => 'Édition', 'label_ar' => 'الطبعة'],
        ],
        'Automotive' => [
                ['label_en' => 'Model', 'label_fr' => 'Modèle', 'label_ar' => 'نموذج'],
                ['label_en' => 'Year', 'label_fr' => 'Année', 'label_ar' => 'سنة'],
                ['label_en' => 'Mileage', 'label_fr' => 'Kilométrage', 'label_ar' => 'المسافة المقطوعة'],
                ['label_en' => 'Transmission', 'label_fr' => 'Transmission', 'label_ar' => 'نقل'],
                ['label_en' => 'Engine', 'label_fr' => 'Moteur', 'label_ar' => 'محرك'],
                ['label_en' => 'Color', 'label_fr' => 'Couleur', 'label_ar' => 'لون'],
            ],
        'Real Estate' => [
            ['label_en' => 'Surface', 'label_fr' => 'Surface', 'label_ar' => 'المساحة'],
            ['label_en' => 'Rooms', 'label_fr' => 'Pièces', 'label_ar' => 'عدد الغرف'],
            ['label_en' => 'Furnished', 'label_fr' => 'Meublé', 'label_ar' => 'مفروش'],
        ],
        'Electronics' => [
            ['label_en' => 'Brand', 'label_fr' => 'Marque', 'label_ar' => 'العلامة التجارية'],
            ['label_en' => 'Condition', 'label_fr' => 'État', 'label_ar' => 'الحالة'],
        ],
        'Furniture' => [
            ['label_en' => 'Material', 'label_fr' => 'Matériau', 'label_ar' => 'الخامة'],
            ['label_en' => 'Color', 'label_fr' => 'Couleur', 'label_ar' => 'اللون'],
        ],
        'Toys' => [
            ['label_en' => 'Age Group', 'label_fr' => "Tranche d'âge", 'label_ar' => 'الفئة العمرية'],
            ['label_en' => 'Brand', 'label_fr' => 'Marque', 'label_ar' => 'العلامة التجارية'],
        ]

    ];

    DB::transaction(function () use ($mainCategories, $genderRoots, $subCategories, $genderCategories, $categoryDetails) {
        // Création des catégories principales
        foreach ($mainCategories as $cat) {
            $main = Category::create([
                'title_en' => $cat['en'],
                'title_fr' => $cat['fr'],
                'title_ar' => $cat['ar'],
                'parent_id' => null
            ]);

            // Sous-catégories
            if (isset($subCategories[$cat['en']])) {
                foreach ($subCategories[$cat['en']] as $sub) {
                    Category::create([
                        'title_en' => $sub['en'],
                        'title_fr' => $sub['fr'],
                        'title_ar' => $sub['ar'],
                        'parent_id' => $main->id
                    ]);
                }
            }

            // Détails
            if (isset($categoryDetails[$cat['en']])) {
                foreach ($categoryDetails[$cat['en']] as $detail) {
                    CategoryDetail::create([
                        'category_id' => $main->id,
                        'label_en' => $detail['label_en'],
                        'label_fr' => $detail['label_fr'],
                        'label_ar' => $detail['label_ar']
                    ]);
                }
            }
        }

        // Catégories Men/Women
        foreach ($genderRoots as $gender) {
            $genderRoot = Category::create([
                'title_en' => $gender['en'],
                'title_fr' => $gender['fr'],
                'title_ar' => $gender['ar'],
                'parent_id' => null
            ]);

            $genderKey = $gender['en']; // 'Men' or 'Women'

            if (isset($genderCategories[$genderKey])) {
                foreach ($genderCategories[$genderKey] as $subCatName => $subSubCats) {
                    // Créer la sous-catégorie principale (ex: Clothing, Shoes...)
                    $sub = Category::create([
                        'title_en' => $subCatName,
                        'title_fr' => $subCatName,
                        'title_ar' => $subCatName,
                        'parent_id' => $genderRoot->id
                    ]);

                    // Créer les sous-sous-catégories (ex: Dresses, Tops, etc.)
                    foreach ($subSubCats as $child) {
                        Category::create([
                            'title_en' => $child['en'],
                            'title_fr' => $child['fr'],
                            'title_ar' => $child['ar'],
                            'parent_id' => $sub->id
                        ]);
                    }
                }
            }
        }
    });

    Log::info('✅ All categories with Men/Women structure seeded successfully.');
}
}
