
import 'dart:typed_data';
import 'package:boutigak/constants/app_colors.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;

Future<Color> getMostFrequentColor(String imageUrl) async {
  final response = await http.get(Uri.parse(imageUrl));
  if (response.statusCode != 200) {
    debugPrint('Erreur chargement image: ${response.statusCode}');
    return AppColors.surface; // Default fallback color
  }
  return compute(_mostFrequentColorInBytes, response.bodyBytes);
}

Color _mostFrequentColorInBytes(Uint8List bytes) {
  final img.Image? original = img.decodeImage(bytes);
  if (original == null) {
    return AppColors.surface;
  }

  // Resize the image to optimize performance
  const int maxSize = 100;
  final img.Image resized = img.copyResize(
    original,
    width: (original.width > original.height ? maxSize : null),
    height: (original.height >= original.width ? maxSize : null),
  );

  // Count pixel occurrences
  final Map<int, int> colorCount = {};
  for (int y = 0; y < resized.height; y++) {
    for (int x = 0; x < resized.width; x++) {
      final img.Pixel pixel = resized.getPixel(x, y); // ✅ Correct way
      final int a = pixel.a.toInt(); // ✅ Alpha
      final int r = pixel.r.toInt(); // ✅ Red
      final int g = pixel.g.toInt(); // ✅ Green
      final int b = pixel.b.toInt(); // ✅ Blue

      final int argb = (a << 24) | (r << 16) | (g << 8) | b; // Convert to ARGB int
      colorCount[argb] = (colorCount[argb] ?? 0) + 1;
    }
  }

  // Find the most frequent color
  int? mostFrequent;
  int maxCount = 0;
  colorCount.forEach((pixel, count) {
    if (count > maxCount) {
      mostFrequent = pixel;
      maxCount = count;
    }
  });

  if (mostFrequent == null) {
    return AppColors.surface;
  }

  // Extract ARGB from most frequent pixel
  int realMostFrequent = mostFrequent!;
  final int a = (realMostFrequent >> 24) & 0xFF;
  final int r = (realMostFrequent >> 16) & 0xFF;
  final int g = (realMostFrequent >> 8) & 0xFF;
  final int b = realMostFrequent & 0xFF;

  return Color.fromARGB(a, r, g, b);
}
