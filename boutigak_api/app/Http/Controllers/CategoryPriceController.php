<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CategoryPrice;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class CategoryPriceController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = CategoryPrice::query()->with('category');

            return Datatables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function($row){
                    $editUrl = route('category_prices.edit', $row->id);
                    $csrfToken = csrf_token();

                    $btn = '<a href="' . $editUrl . '" class="edit btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>';
                    $btn .= '<button type="button" class="delete btn btn-danger btn-sm ms-1" onclick="confirmDelete(' . $row->id . ')">
                            <i class="fas fa-trash-alt"></i> Delete
                         </button>';
                    $btn .= '<form id="delete-form-' . $row->id . '" action="' . route('category_prices.destroy', $row->id) . '" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="' . $csrfToken . '">
                            <input type="hidden" name="_method" value="DELETE">
                         </form>';

                    return $btn;
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('backoffice.category-pricing.index');
    }

    public function create()
    {
        $categories = Category::all();
        return view('backoffice.category-pricing.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:category,id',
            'price' => 'required|numeric',
        ]);

        CategoryPrice::query()->create($request->all());
        return redirect()->route('category_prices.index')->with('success', 'Category price created successfully.');
    }

    public function edit($id)
    {
        $categoryPrice = CategoryPrice::query()->findOrFail($id);
        $categories = Category::all();
        return view('backoffice.category-pricing.edit', compact('categoryPrice', 'categories'));
    }

    public function update(Request $request, $id)
    {
        $categoryPrice = CategoryPrice::query()->findOrFail($id);

        $request->validate([
            'category_id' => 'required|exists:category,id',
            'price' => 'required|numeric',
        ]);

        $categoryPrice->update($request->all());
        return redirect()->route('category_prices.index')->with('success', 'Category price updated successfully.');
    }

    public function destroy($id)
    {
        $categoryPrice = CategoryPrice::query()->findOrFail($id);
        $categoryPrice->delete();
        return redirect()->route('category_prices.index')->with('success', 'Category price deleted successfully.');
    }
}
