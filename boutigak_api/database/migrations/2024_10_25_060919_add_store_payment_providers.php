<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::create('store_payment_providers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')->nullable()->constrained('store')->cascadeOnDelete();
            $table->foreignId('provider_id')->nullable()->constrained('e_payment_providers')->cascadeOnDelete();
            $table->string('payment_code')->nullable();
            $table->string('phone_number')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Add unique constraint to prevent duplicate provider for store
            $table->unique(['store_id', 'provider_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
