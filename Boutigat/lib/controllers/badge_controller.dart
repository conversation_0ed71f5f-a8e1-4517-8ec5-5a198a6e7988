import 'package:boutigak/controllers/inbox_controller.dart';
import 'package:boutigak/controllers/notifications_controller.dart';
import 'package:boutigak/data/models/badge.dart';
import 'package:boutigak/data/services/badge_service.dart';
import 'package:get/get.dart';

class BadgeController extends GetxController {
  RxMap<String, int> badgeCounts = <String, int>{}.obs;
  RxInt totalBadgeCount = 0.obs;
  RxBool isLoading = false.obs;

  InboxController get inboxController => Get.find<InboxController>();
  NotificationController get notificationController => Get.find<NotificationController>();
  @override
  void onInit() {
    super.onInit();
    // fetchBadgeCounts();
  }

  Future<void> fetchBadgeCounts() async {
    try {
      isLoading.value = true;
      BadgeCounts counts = await BadgeService.getBadgeCounts();
      badgeCounts.value = counts.counts;
      totalBadgeCount.value = counts.total;

      inboxController.fetchDiscussions();
      notificationController.fetchNotifications();

    } catch (e) {
      print('Error fetching badge counts: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> resetBadge(String module) async {
    try {
      BadgeCounts counts = await BadgeService.resetBadge(module);
      badgeCounts.value = counts.counts;
      totalBadgeCount.value = counts.total;
    } catch (e) {
      print('Error resetting badge: $e');
    }
  }

  int getModuleCount(String module) {
    return badgeCounts[module] ?? 0;
  }
}