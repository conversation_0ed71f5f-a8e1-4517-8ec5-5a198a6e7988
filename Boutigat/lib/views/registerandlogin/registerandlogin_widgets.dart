import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '/constants/app_colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// ✅ Responsive CustomTextFormField, CustomGenderRadioField, and TextLink without fixed height

class CustomTextFormField extends StatefulWidget {
  final String hintText;
  final RxString value;
  final double width;
  final RxnString? errorText;
  final bool obscureText;
  final IconData? prefixIcon;
  final List<TextInputFormatter>? inputFormatters;

  const CustomTextFormField({
    super.key,
    required this.hintText,
    required this.value,
    required this.width,
    required this.errorText,
    this.obscureText = false,
    this.prefixIcon,
    this.inputFormatters,
  });

  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  late bool _obscure;

  @override
  void initState() {
    super.initState();
    _obscure = widget.obscureText;
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(375, 812));

    return Container(
      width: widget.width.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 55.h,
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            decoration: BoxDecoration(
              border: Border.all(
                color: (widget.errorText?.value?.isNotEmpty ?? false)
                    ? AppColors.error
                    : Colors.grey.shade400,
              ),
              borderRadius: BorderRadius.circular(12.0.r),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: Row(
              children: [
                if (widget.prefixIcon != null) ...[
                  Icon(widget.prefixIcon, color: AppColors.disabled, size: 20.sp),
                  SizedBox(width: 8.w),
                ],
                Expanded(
                  child: TextField(
                    onChanged: (newValue) => widget.value.value = newValue,
                    obscureText: _obscure,
                    inputFormatters: widget.inputFormatters,
                    textAlignVertical: TextAlignVertical.center,
                    style: TextStyle(color: AppColors.disabled),
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      hintStyle: TextStyle(color: AppColors.disabled),
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 15.h),
                    ),
                  ),
                ),
                if (widget.obscureText)
                  GestureDetector(
                    onTap: () => setState(() => _obscure = !_obscure),
                    child: Icon(
                      _obscure ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.disabled,
                      size: 20.sp,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TextLink extends StatelessWidget {
  final String text;
  final String routeName;

  const TextLink({super.key, required this.text, required this.routeName});

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);

    return GestureDetector(
      onTap: () => Get.offAndToNamed(routeName),
      child: Text(
        text,
        style: TextStyle(
          color: AppColors.primary,
          
          
          fontSize: 14.sp,
        ),
      ),
    );
  }
}
class CustomGenderRadioField extends StatelessWidget {
  final String hintText;
  final Rxn<String> value;
  final double width;
  final RxnString? errorText;

  const CustomGenderRadioField({
    super.key,
    required this.hintText,
    required this.value,
    required this.width,
    required this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(375, 812));

    return Container(
      width: width.w,
     
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         
          Obx(() => Container(
                height: 55.h, // ✅ Hauteur alignée avec CustomTextFormField
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                 border: Border.all(
  color: (errorText?.value?.isNotEmpty ?? false)
      ? AppColors.error
      : Colors.grey.shade400,
),

                  borderRadius: BorderRadius.circular(12.0.r),
                  color: Theme.of(context).colorScheme.surface,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Icon(Icons.wc, color: AppColors.disabled, size: 20),
    SizedBox(width: 8.w),

                    Text(
                      hintText,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.disabled,
                        fontWeight: AppFontWeights.medium,
                      ),
                    ),
                    SizedBox(width: 60.w),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => value.value = 'Male',
                        child: Row(
                          children: [
                            _customRadioButton(value.value == 'Male'),
                            SizedBox(width: 5.w),
                            Text('male'.tr),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => value.value = 'Female',
                        child: Row(
                          children: [
                            _customRadioButton(value.value == 'Female'),
                            SizedBox(width: 5.w),
                            Text('female'.tr),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        
        ],
      ),
    );
  }

  Widget _customRadioButton(bool isSelected) {
    return Container(
      width: 18.w,
      height: 18.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: isSelected ? AppColors.primary : Colors.grey, width: 2.w),
        color: isSelected ? AppColors.primary : Colors.transparent,
      ),
      child: isSelected
          ? Center(
              child: Icon(
                Icons.check,
                color: Colors.white,
                size: 12.sp,
              ),
            )
          : null,
    );
  }
}
