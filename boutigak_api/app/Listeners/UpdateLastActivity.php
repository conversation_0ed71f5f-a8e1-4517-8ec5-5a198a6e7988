<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Auth;

class UpdateLastActivity
{
    /**
     * Handle the login event.
     *
     * @param Login $event
     * @return void
     */
    public function handleLogin(Login $event): void
    {
        $user = $event->user;
        if ($user) {
            $user->last_activity = now();
            $user->save();
        }
    }

    /**
     * Handle the logout event.
     *
     * @param Logout $event
     * @return void
     */
    public function handle(Logout $event): void
    {
        $user = Auth::user();
        if ($user) {
            $user->last_activity = now();
            $user->save();
        }
    }
}
