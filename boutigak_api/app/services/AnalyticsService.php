<?php

namespace App\services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AnalyticsService
{
    /**
     * Get daily active users for the last N days
     *
     * @param int $days
     * @return array
     */
    public function getDailyActiveUsers(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        $dailyActiveUsers = User::select(
            DB::raw('DATE(last_activity) as date'),
            DB::raw('COUNT(DISTINCT id) as active_users')
        )
        ->where('last_activity', '>=', $startDate)
        ->where('last_activity', '<=', $endDate)
        ->whereNotNull('last_activity')
        ->groupBy(DB::raw('DATE(last_activity)'))
        ->orderBy('date')
        ->get();

        // Fill missing dates with 0 active users
        $result = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $found = $dailyActiveUsers->firstWhere('date', $dateString);
            
            $result[] = [
                'date' => $dateString,
                'active_users' => $found ? $found->active_users : 0,
                'formatted_date' => $currentDate->format('M d')
            ];
            
            $currentDate->addDay();
        }

        return $result;
    }

    /**
     * Get current active users (active in last 24 hours)
     *
     * @return int
     */
    public function getCurrentActiveUsers(): int
    {
        return User::where('last_activity', '>=', Carbon::now()->subDay())
            ->whereNotNull('last_activity')
            ->count();
    }

    /**
     * Get active users for different time periods
     *
     * @return array
     */
    public function getActiveUsersStats(): array
    {
        $now = Carbon::now();

        return [
            'last_hour' => User::where('last_activity', '>=', $now->copy()->subHour())->count(),
            'last_24_hours' => User::where('last_activity', '>=', $now->copy()->subDay())->count(),
            'last_7_days' => User::where('last_activity', '>=', $now->copy()->subWeek())->count(),
            'last_30_days' => User::where('last_activity', '>=', $now->copy()->subMonth())->count(),
        ];
    }

    /**
     * Get user activity summary
     *
     * @return array
     */
    public function getUserActivitySummary(): array
    {
        $totalUsers = User::count();
        $activeToday = User::where('last_activity', '>=', Carbon::today())->count();
        $activeThisWeek = User::where('last_activity', '>=', Carbon::now()->startOfWeek())->count();
        $activeThisMonth = User::where('last_activity', '>=', Carbon::now()->startOfMonth())->count();

        return [
            'total_users' => $totalUsers,
            'active_today' => $activeToday,
            'active_this_week' => $activeThisWeek,
            'active_this_month' => $activeThisMonth,
            'activity_rate_today' => $totalUsers > 0 ? round(($activeToday / $totalUsers) * 100, 2) : 0,
            'activity_rate_week' => $totalUsers > 0 ? round(($activeThisWeek / $totalUsers) * 100, 2) : 0,
            'activity_rate_month' => $totalUsers > 0 ? round(($activeThisMonth / $totalUsers) * 100, 2) : 0,
        ];
    }

    /**
     * Get hourly active users for today
     *
     * @return array
     */
    public function getHourlyActiveUsers(): array
    {
        $today = Carbon::today();
        $result = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $startHour = $today->copy()->addHours($hour);
            $endHour = $startHour->copy()->addHour();

            $activeUsers = User::where('last_activity', '>=', $startHour)
                ->where('last_activity', '<', $endHour)
                ->count();

            $result[] = [
                'hour' => $hour,
                'formatted_hour' => $startHour->format('H:i'),
                'active_users' => $activeUsers
            ];
        }

        return $result;
    }
}
