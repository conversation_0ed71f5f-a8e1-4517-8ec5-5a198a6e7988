<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class TrackUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track activity for authenticated users
        if (Auth::check()) {
            $user = Auth::user();
            $userId = $user->id;
            
            // Use cache to prevent too frequent database updates (throttle to once per minute)
            $cacheKey = "user_activity_updated_{$userId}";
            
            if (!Cache::has($cacheKey)) {
                // Update last_activity timestamp
                $user->update(['last_activity' => now()]);
                
                // Cache for 1 minute to prevent excessive database writes
                Cache::put($cacheKey, true, 60);
            }
        }

        return $response;
    }
}
