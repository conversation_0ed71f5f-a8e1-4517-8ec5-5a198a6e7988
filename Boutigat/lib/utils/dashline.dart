import 'package:flutter/material.dart';

Widget dashedLine() {
  return LayoutBuilder(
    builder: (context, constraints) {
      int dashCount = (constraints.maxWidth / 10).floor();
      return Wrap(
        children: List.generate(dashCount, (_) {
          return Container(
            width: 5,
            height: 1,
            margin: EdgeInsets.symmetric(horizontal: 2),
            color: Colors.grey,
          );
        }),
      );
    },
  );
}
