<?php

namespace App\Models;

use App\Models\Item;
use App\Models\Order;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderPayment extends Model
{
    use HasFactory;

    protected $table = 'order_payment';

    protected $fillable = [
        'photo_url',
        'amount',
        'order_id',
        'provider_id',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
