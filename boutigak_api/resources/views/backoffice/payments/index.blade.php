@extends('backoffice.layouts.layout')

@section('title', 'Payments | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Payments</h4>
                        </div>
                        <div class="card-body">
                            <table id="payments-datatable" class="table dt-responsive nowrap w-100">
                                <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Image</th>
                                    <th>Item Title</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Provider</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Data will be populated by DataTables -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('script')
    <script type="text/javascript">
        $(document).ready(function () {
            $('#payments-datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('payments.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {
                        data: 'first_image', name: 'first_image', render: function (data, type, row) {
                            return data ? '<img src="' + data + '" class="img-fluid img-thumbnail" alt="item-image" style="max-width: 100px;">' : 'No Image';
                        }
                    },
                    {data: 'item_title', name: 'item_title'},
                    {data: 'type', name: 'type'},
                    {data: 'amount', name: 'amount'},
                    {data: 'provider_name', name: 'provider_name'},
                    {data: 'action', name: 'action', orderable: false, searchable: false},
                ],
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                    '<"row"<"col-sm-12"tr>>' +
                    '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                lengthChange: true,
                searchPlaceholder: "Search payments..."
            });
        });
    </script>
@endpush
