import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:boutigak/data/models/ad.dart';
import 'package:boutigak/data/services/webservices.dart';

class AdController extends GetxController {
  final RxList<Ad> ads = <Ad>[].obs;
  final RxBool isLoading = false.obs;
  final RxInt currentAdIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    fetchAds();
  }

  Future<void> fetchAds() async {
    try {
      isLoading.value = true;
      final response = await WebService.getAds();
      

      log('response ads ${response.body}');
      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body)['data'];

        log('data .. ${data}');
        ads.value = data.map((json) => Ad.fromJson(json)).toList();

        isLoading.value = false;

        log('ads value ${ads.value}');
      }
    } catch (e) {
      print('Error fetching ads: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  void setCurrentIndex(int index) {
    currentAdIndex.value = index;
  }
}