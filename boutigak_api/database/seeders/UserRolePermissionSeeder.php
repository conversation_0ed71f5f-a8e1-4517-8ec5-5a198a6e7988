<?php

namespace Database\Seeders;
namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserRolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            'view role', 'create role', 'update role', 'delete role',
            'view permission', 'create permission', 'update permission', 'delete permission',
            'view user', 'create user', 'update user', 'delete user',
            'view item', 'create item', 'update item', 'delete item', 'validate item'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        $adminPermissions = Permission::pluck('name')->toArray();
        $adminRole->syncPermissions($adminPermissions);

        $admin = User::firstOrCreate([
            'password' => Hash::make('password'),
            'firstname' => 'Admin',
            'lastname' => 'User',
            'phone' => '12345678',
            'invitationcode' => '123',
            'gender' => 'male',
            'has_store' => false,
            'lang' => 'en',
            'is_verified' => true,
            'deleted_at' => null,
        ]);

        DB::query()
            ->from('model_has_roles')
            ->insert([
                'role_id' => $adminRole->id,
                'model_type' => User::class,
                'model_id' => $admin->id,
            ]);
    }
}
