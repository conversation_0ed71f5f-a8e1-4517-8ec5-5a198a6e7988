<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\PaymentProof;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class PaymentVerificationController extends Controller
{
    /**
     * Get all app payment proofs
     * 
     * @OA\Get(
     *     path="/api/admin/payment-verifications",
     *     tags={"Admin"},
     *     summary="Get all app payment proofs",
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         @OA\Schema(type="string", enum={"PENDING", "APPROVED", "REJECTED"})
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         @OA\Schema(type="integer", default=20)
     *     ),
     *     @OA\Response(response=200, description="List of payment proofs"),
     *     @OA\Response(response=401, description="Unauthenticated"),
     *     @OA\Response(response=403, description="Unauthorized")
     * )
     */
    public function index(Request $request)
    {
        try {
            $query = PaymentProof::where('to_store', false)
                ->with(['user', 'item', 'provider']);

            // Apply status filter if provided
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            $paymentProofs = $query->latest();

            return response()->json([
                'success' => true,
                'data' => $paymentProofs
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment proofs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch payment proofs'
            ], 500);
        }
    }

    /**
     * Get specific payment proof details
     * 
     * @OA\Get(
     *     path="/api/admin/payment-verifications/{id}",
     *     tags={"Admin"},
     *     summary="Get specific payment proof details",
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response=200, description="Payment proof details"),
     *     @OA\Response(response=404, description="Payment proof not found")
     * )
     */
    public function show($id)
    {
        try {
            $paymentProof = PaymentProof::where('to_store', false)
                ->with(['user', 'item', 'provider', 'verifier'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $paymentProof
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment proof not found'
            ], 404);
        }
    }

    
    private function getLocalizedNotificationMessages(string $type, array $params = []): array
{
    // Set default values for parameters
    $defaultParams = [
        'reason' => '',
        'payment_id' => null,
        'item_id' => null,
        'amount' => ''
    ];

    // Merge provided params with defaults
    $params = array_merge($defaultParams, array_filter($params));

    $formatArabic = function(string $text, array $values = []): string {
        return vsprintf(str_replace('%s', '%s', $text), array_map(fn($v) => $v ?: '', $values));
    };

    $messages = [
        'payment_approved' => [
            'en' => [
                'title' => 'Payment Approved',
                'body' => 'Your payment has been verified and approved.'
            ],
            'fr' => [
                'title' => 'Paiement Approuvé',
                'body' => 'Votre paiement a été vérifié et approuvé.'
            ],
            'ar' => [
                'title' => 'تمت الموافقة على الدفع',
                'body' => 'تم التحقق من دفعتك والموافقة عليها.'
            ]
        ],
        'payment_rejected' => [
            'en' => [
                'title' => 'Payment Rejected',
                'body' => sprintf('Your payment was rejected. Reason: %s', $params['reason'])
            ],
            'fr' => [
                'title' => 'Paiement Refusé',
                'body' => sprintf('Votre paiement a été refusé. Raison: %s', $params['reason'])
            ],
            'ar' => [
                'title' => 'تم رفض الدفع',
                'body' => $formatArabic("%s :تم رفض دفعتك. السبب", [$params['reason']])
            ]
        ]
    ];

    Log::debug('Payment verification notification parameters', [
        'type' => $type,
        'params' => $params
    ]);

    return $messages[$type] ?? [];
}

private function sendLocalizedNotification(User $user, string $type, array $params = []): void
{
    // Set default values for parameters
    $params = array_merge([
        'reason' => '',
        'payment_id' => null,
        'item_id' => null,
        'amount' => '',
        'verified_by' => auth()->id()
    ], array_filter($params));

    $token = $user->deviceToken->token ?? null;
    $userLang = $user->lang ?? 'en';

    try {
        // Get all language versions
        $messages = $this->getLocalizedNotificationMessages($type, $params);
        
        // Get user's preferred language for push notification
        $userMessage = $messages[$userLang] ?? $messages['en'];

        // Create notification with all language versions
        $notification = Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'payment_id' => $params['payment_id'],
            'item_id' => $params['item_id'],
            'is_read' => false,
            // Store titles in all languages
            'title_en' => $messages['en']['title'],
            'title_fr' => $messages['fr']['title'],
            'title_ar' => $messages['ar']['title'],
            // Store messages in all languages
            'message_en' => $messages['en']['body'],
            'message_fr' => $messages['fr']['body'],
            'message_ar' => $messages['ar']['body'],
            // Store current language version for backwards compatibility
            // 'title' => $userMessage['title'],
            // 'message' => $userMessage['body'],
            // Store additional parameters
            'params' => json_encode(array_merge($params, [
                'notification_type' => $type,
                'language' => $userLang,
                'verification_date' => now()->toISOString()
            ]))
        ]);

        // Send push notification if device token exists
        if ($token) {
            $this->firebaseService->sendNotification(
                $token,
                $userMessage['title'],
                $userMessage['body']
            );

            Log::info('Payment verification notification sent successfully', [
                'user_id' => $user->id,
                'language' => $userLang,
                'type' => $type,
                'notification_id' => $notification->id,
                'payment_id' => $params['payment_id']
            ]);
        } else {
            Log::warning('No device token found for user, notification stored in database only', [
                'user_id' => $user->id
            ]);
        }
    } catch (\Exception $e) {
        Log::error('Failed to process payment verification notification', [
            'user_id' => $user->id,
            'error' => $e->getMessage(),
            'type' => $type,
            'params' => $params
        ]);
    }
}

    // Your existing methods remain the same, but update the notification sending in the update method:
    
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:APPROVED,REJECTED',
            'rejection_reason' => 'required_if:status,REJECTED|string|nullable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $paymentProof = PaymentProof::where('to_store', false)->findOrFail($id);
            
            if ($paymentProof->status !== 'PENDING') {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment proof already verified'
                ], 422);
            }

            DB::beginTransaction();

            $paymentProof->update([
                'status' => $request->status,
                'rejection_reason' => $request->rejection_reason,
                'verified_by' => auth()->id(),
                'verified_at' => now()
            ]);

            // If approved, update item status and send notification
            if ($request->status === 'APPROVED' && $paymentProof->item) {
                $paymentProof->item->update([
                    'status' => 'ACTIVE',
                    'is_paid' => true
                ]);

                $this->sendLocalizedNotification(
                    $paymentProof->user,
                    'payment_approved',
                    []
                );
            }

            // If rejected, send notification with reason
            if ($request->status === 'REJECTED') {
                $this->sendLocalizedNotification(
                    $paymentProof->user,
                    'payment_rejected',
                    ['reason' => $request->rejection_reason]
                );
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Payment verification completed',
                'data' => $paymentProof
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment verification failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to verify payment'
            ], 500);
        }
    }


    /**
     * Get verification statistics
     * 
     * @OA\Get(
     *     path="/api/admin/payment-verifications/stats",
     *     tags={"Admin"},
     *     summary="Get payment verification statistics",
     *     @OA\Response(response=200, description="Statistics retrieved successfully")
     * )
     */
    public function stats()
    {
        try {
            $stats = [
                'total' => PaymentProof::where('to_store', false)->count(),
                'pending' => PaymentProof::where('to_store', false)
                    ->where('status', 'PENDING')
                    ->count(),
                'approved' => PaymentProof::where('to_store', false)
                    ->where('status', 'APPROVED')
                    ->count(),
                'rejected' => PaymentProof::where('to_store', false)
                    ->where('status', 'REJECTED')
                    ->count(),
                'today' => PaymentProof::where('to_store', false)
                    ->whereDate('created_at', today())
                    ->count(),
                'amount_approved' => PaymentProof::where('to_store', false)
                    ->where('status', 'APPROVED')
                    ->sum('amount')
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching payment stats: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics'
            ], 500);
        }
    }
}