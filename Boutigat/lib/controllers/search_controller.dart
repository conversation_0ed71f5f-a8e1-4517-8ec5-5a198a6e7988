import 'package:get/get.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/search.dart';
import 'package:boutigak/data/services/search_service.dart';



class MYSearchController extends GetxController {
  var searchText = ''.obs;
  var items = <Item>[].obs;
  var searchHistory = <SearchHistory>[].obs;
  var showHistory = true.obs;
  var popularCategories = <String>[].obs;
  
 
 
  void onSearchChanged(String value) {
    searchText.value = value;
  }

  Future<void> submitSearch() async {
    showHistory.value = false;
    if (searchText.value.isNotEmpty) {
      var result = await SearchService.searchItems(searchText.value);
      if (result != null) {
        items.value = result;
      }

      fetchSearchHistory();
      update();
    }
  }

  void submitHistorySearch(String query) {
    searchText.value = query;
    submitSearch();
  }

  Future<void> fetchSearchHistory() async {
    var result = await SearchService.fetchSearchHistory();
    if (result != null) {
      searchHistory.value = result;
    }
  }

  Future<void> removeSearchHistory(int id) async {
    try {
      // Call the API to delete the search history
      bool success = await SearchService.deleteSearchHistory(id);
      
      if (success) {
        // Only remove from local list if API call was successful
        searchHistory.removeWhere((item) => item.id == id);
        update(); // Update the UI
      }
    } catch (e) {
      print('Error removing search history: $e');
    }
  }



Future<void> getPopularCategories() async {
 // print("here ....");
  var result = await SearchService.fetchPopuarCategories();

 // print("popular categories $result");
if (result != null) {
  // Détermine le champ de titre en fonction de la langue choisie
  String titleField;
  String languageCode = Get.locale?.languageCode ?? 'en';

  switch (languageCode) {
    case 'ar':
      titleField = 'title_ar';
      break;
    case 'fr':
      titleField = 'title_fr';
      break;
    default:
      titleField = 'title_en';
      break;
  }

  // Extrait le titre correspondant à la langue choisie
  popularCategories.value = result.map((e) => e[titleField] as String).toList();

 // print("popular categories length ${popularCategories.length}");
}
}
@override
void onInit() {
  super.onInit();
  refreshSearchData();
}

void refreshSearchData() {
  fetchSearchHistory();
  getPopularCategories();
  update();  // Forcer la mise à jour de la page
}
@override
void onReady() {
  super.onReady();
  refreshSearchData(); // Rafraîchir à chaque fois que la page devient active
}
}
