<?php

namespace Database\Seeders;

use App\Models\Category;
use Faker\Factory as Faker;
use App\Models\CategoryDetail;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;



class newCategory extends Seeder
{

    public function run()
    {
        $faker = Faker::create();

        $categoriesEn = [
            "Electronics",
            "Clothing",
            "Home Appliances",
            "Books",
            "Toys",
            "Sports & Outdoors",
            "Beauty & Personal Care",
            "Health & Wellness",
            "Furniture",
            "Jewelry",
        ];

        $categoriesFr = [
            "Électronique",
            "Vêtements",
            "Appareils ménagers",
            "Livres",
            "Jouets",
            "Sports et plein air",
            "Beauté et soins personnels",
            "Santé et bien-être",
            "Meubles",
            "Bijoux",
        ];

        $categoriesAr = [
            "إلكترونيات",
            "ملابس",
            "أجهزة منزلية",
            "كتب",
            "ألعاب",
            "رياضات وأنشطة خارجية",
            "الجمال والعناية الشخصية",
            "الصحة والعافية",
            "أثاث",
            "مجوهرات",
        ];

        // Define number of categories and details per category
        $numCategories = 10;
        $numDetailsPerCategory = 5;

        DB::transaction(function () use ($categoriesEn, $categoriesFr, $categoriesAr, $faker, $numCategories, $numDetailsPerCategory) {
            for ($i = 0; $i < $numCategories; $i++) {
                $categoryEn = $categoriesEn[$i % count($categoriesEn)];
                $categoryFr = $categoriesFr[$i % count($categoriesFr)];
                $categoryAr = $categoriesAr[$i % count($categoriesAr)];

                // Create category
                $category = Category::create([
                    'title_en' => $categoryEn,
                    'title_ar' => $categoryAr,
                    'title_fr' => $categoryFr,
                    'parent_id' => null,
                ]);

                // Create category details
                for ($j = 0; $j < $numDetailsPerCategory; $j++) {
                    CategoryDetail::create([
                        'label_en' => $faker->word(),
                        'label_ar' => $faker->word(),
                        'label_fr' => $faker->word(),
                        'category_id' => $category->id,
                        'created_date' => now(),
                    ]);
                }
            }
        });

        Log::info('Categories and category details have been seeded.');

    }
}


