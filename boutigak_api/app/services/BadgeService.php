<?php

namespace App\Services;

use App\Models\Badge;
use App\Models\User;
use Carbon\Carbon;

class BadgeService
{
    /**
     * Increment badge count for a specific module
     */
    public function increment(User $user, string $module): int
    {
        $badge = Badge::firstOrCreate(
            ['user_id' => $user->id, 'module' => $module],
            ['count' => 0]
        );
        
        $badge->increment('count');
        
        return $badge->count;
    }
    
    /**
     * Reset badge count for a specific module
     */
    public function reset(User $user, string $module): void
    {
        Badge::updateOrCreate(
            ['user_id' => $user->id, 'module' => $module],
            ['count' => 0, 'last_read_at' => Carbon::now()]
        );
    }
    
    /**
     * Get all badge counts for a user
     */
    public function getCounts(User $user): array
    {
        $badges = Badge::where('user_id', $user->id)->get();
        
        $counts = [];
        foreach ($badges as $badge) {
            $counts[$badge->module] = $badge->count;
        }
        
        return $counts;
    }
    
    /**
     * Get total badge count for a user across all modules
     */
    public function getTotalCount(User $user): int
    {
        return Badge::where('user_id', $user->id)->sum('count');
    }
}