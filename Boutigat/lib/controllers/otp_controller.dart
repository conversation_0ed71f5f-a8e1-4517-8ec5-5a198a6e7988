


import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/auth_service.dart';
import 'package:boutigak/data/services/session_service.dart';
import 'package:get/get.dart';

class OtpController extends GetxController {
  var isLoading = false.obs; 



  Future<void> sendOtp(String phoneNumber) async {
    try {
      isLoading.value = true;
      var response = await AuthService.sendOtp(phoneNumber: phoneNumber);
      
      if (response != null && response['success'] == true) {
     //   // Get.snakbar("Success", "OTP sent successfully to $phoneNumber");
      } else {
      //  // Get.snakbar("Error", response?['message'] ?? "Failed to send OTP to $phoneNumber", 
         // backgroundColor: AppColors.error);
      }
    } catch (e) {
     // // Get.snakbar("Error", "An error occurred while sending OTP: $e", 
      //  backgroundColor: AppColors.error);
   //   print('Error details: $e');
    } finally {
      isLoading.value = false;
    }
  }

Future<void> requestPasswordReset(String phoneNumber) async {
    try {
      isLoading.value = true;
      var response = await AuthService.requestPasswordReset(phoneNumber: phoneNumber);
      
      if (response != null && response['success'] == true) {
     //   // Get.snakbar("Success", "OTP sent successfully to $phoneNumber");
      } else {
      //  // Get.snakbar("Error", response?['message'] ?? "Failed to send OTP to $phoneNumber", 
         // backgroundColor: AppColors.error);
      }
    } catch (e) {
     // // Get.snakbar("Error", "An error occurred while sending OTP: $e", 
      //  backgroundColor: AppColors.error);
   //   print('Error details: $e');
    } finally {
      isLoading.value = false;
    }
  }













  Future<bool> verifyOtp(String phoneNumber, String otp) async {
    try {
      isLoading.value = true;
      var response = await AuthService.verifyOtp(phoneNumber: phoneNumber, otp: otp);
      

      print("response verify otp ${response}");
      if (response != null) {
        await SessionService.saveToken(response['access_token']);
        await SessionService.saveUser(response['user']);

        var authController = Get.find<AuthController>();
        authController.setToken(response['access_token']);
        authController.setUser(User.fromJson(response['user']));

        // Get.snakbar("Success", "OTP verified successfully");
        return true;
      } else {
        // Get.snakbar("Error", response?['error'] ?? "Invalid or expired OTP",
          //  backgroundColor: AppColors.error);
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while verifying OTP: $e",
       //   backgroundColor: AppColors.error);
      print('Error details: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}