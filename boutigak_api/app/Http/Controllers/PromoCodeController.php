<?php

namespace App\Http\Controllers;

use App\Models\PromoCode;
use App\Models\ItemPayment;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PromoCodeController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = PromoCode::query();

            // Set default values if filters are not provided
            $isActive = $request->has('is_active') && $request->is_active !== '' ? $request->is_active : null;
            $discount = $request->has('discount') && $request->discount !== '' ? $request->discount : null;
            $expiresAt = $request->has('expires_at') && $request->expires_at !== '' ? $request->expires_at : null;

            // Apply filters only if they are not null
            if (!is_null($isActive)) {
                $query->where('is_active', $isActive);
            }

            if (!is_null($discount)) {
                $query->where('discount', $discount);
            }

            if (!is_null($expiresAt)) {
                $query->whereDate('expires_at', $expiresAt);
            }

            return Datatables::of($query)
                ->addIndexColumn()
                ->editColumn('is_active', function($row) {
                    $status = $row->is_active ? 'success' : 'danger';
                    return '<span class="badge bg-' . $status . '">' . ($row->is_active ? 'Active' : 'Inactive') . '</span>';
                })
                ->editColumn('expires_at', function($row) {
                    return $row->expires_at ? with(new \Carbon\Carbon($row->expires_at))->format('d/m/Y') : '';
                })
                ->addColumn('action', function($row) {
                    $editUrl = route('promo-codes.edit', $row->id);
                    $csrfToken = csrf_token();
                    $isActive = $row->is_active ? 'Stop' : 'Unstop';
                    $isActiveIcon = $row->is_active ? 'fa fa-ban' : 'fa fa-play';

                    $btn = '<a href="' . $editUrl . '" class="edit btn btn-primary btn-sm">
                            <i class="fa fa-edit"></i> Edit
                        </a>';
                    $btn .= '<button type="button" class="stop btn btn-warning btn-sm mx-1 text-white" onclick="confirmStop(' . $row->id . ', ' . $row->is_active . ')">
                            <i class="' . $isActiveIcon . '"></i> ' . $isActive . '
                         </button>';
                    $btn .= '<button type="button" class="delete btn btn-danger btn-sm" onclick="confirmDelete(' . $row->id . ')">
                            <i class="fa fa-trash"></i> Delete
                         </button>';
                    $btn .= '<form id="delete-form-' . $row->id . '" action="' . route('promo-codes.destroy', $row->id) . '" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="' . $csrfToken . '">
                            <input type="hidden" name="_method" value="DELETE">
                         </form>';
                    $btn .= '<form id="stop-form-' . $row->id . '" action="' . route('promo-codes.stop', $row->id) . '" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="' . $csrfToken . '">
                         </form>';

                    return $btn;
                })
                ->rawColumns(['action', 'is_active'])
                ->make(true);
        }

        return view('backoffice.promocodes.index');
    }

    public function stop($id)
    {
        $promoCode = PromoCode::query()->findOrFail($id);
        $promoCode->is_active = !$promoCode->is_active;
        $promoCode->save();

        return response()->json(['success' => 'Promo code status updated successfully.']);
    }

    public function create()
    {
        return view('backoffice.promocodes.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|unique:promo_codes',
            'discount' => 'required|numeric',
            'expires_at' => 'nullable|date',
            'limit_usages' => 'nullable|integer|min:1',
        ]);

        PromoCode::create($request->all());
        return redirect()->route('promo-codes.index')->with('success', 'Promo code created successfully.');
    }

    public function edit($id)
    {
        $promoCode = PromoCode::query()->findOrFail($id);
        return view('backoffice.promocodes.edit', compact('promoCode'));
    }

    public function update(Request $request, $id)
    {
        $promoCode = PromoCode::query()->findOrFail($id);

        $request->validate([
            'code' => 'required|string|unique:promo_codes,code,' . $promoCode->id,
            'discount' => 'required|numeric',
            'expires_at' => 'nullable|date',
            'limit_usages' => 'nullable|integer|min:1',
        ]);

        $promoCode->update($request->all());
        return redirect()->route('promo-codes.index')->with('success', 'Promo code updated successfully.');
    }

    public function destroy($id)
    {
        $promoCode = PromoCode::query()->findOrFail($id);
        $promoCode->delete();
        return redirect()->route('promo-codes.index')->with('success', 'Promo code deleted successfully.');
    }


    public function calculateItemPrice(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:item,id',
            'promo_code' => 'required|string',
            'original_amount' => 'required|numeric'
        ]);

        try {
            $promoCode = PromoCode::where('code', $request->promo_code)
                ->where('is_active', true)
                ->where(function($query) {
                    $query->where('expires_at', '>', now())
                          ->orWhereNull('expires_at');
                })
                ->first();

            if (!$promoCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired promo code',
                    'amount' => $request->original_amount
                ]);
            }

            // Check usage limit if set
            if ($promoCode->limit_usages && $promoCode->times_used >= $promoCode->limit_usages) {
                return response()->json([
                    'success' => false,
                    'message' => 'Promo code usage limit reached',
                    'amount' => $request->original_amount
                ]);
            }

            $discountAmount = ($request->original_amount * $promoCode->discount) / 100;
            $finalAmount = $request->original_amount - $discountAmount;
            
            // If discount is 100%, create payment record
            if ($finalAmount == 0) {
                ItemPayment::create([
                    'item_id' => $request->item_id,
                    'amount' => 0,
                    'type' => 'promo',
                    'promo_code' => $request->promo_code,
                ]);
            }

            // increment usage counter
            $promoCode->incrementUsage();

            return response()->json([
                'success' => true,
                'message' => 'Promo code applied successfully',
                'original_amount' => $request->original_amount,
                'discount_percentage' => $promoCode->discount,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalAmount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating price',
                'amount' => $request->original_amount
            ], 500);
        }
    }
}
