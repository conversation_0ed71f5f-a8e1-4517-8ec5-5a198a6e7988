<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Http\Controllers\OrderController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;



class AutoCancelOrders extends Command
{
    protected $signature = 'orders:auto-cancel';
    protected $description = 'Automatically cancel orders that have been pending for more than 30 minutes';

    public function handle()
    {
        Log::info('AutoCancelOrders command started');
        $this->info('Starting auto-cancellation of pending orders...');
        
        $cutoffTime = Carbon::now()->subMinutes(30);
        Log::info('Looking for orders pending since before', ['cutoff_time' => $cutoffTime->toDateTimeString()]);
        
        $pendingOrders = Order::where('status', 'PENDING')
            ->where('created_at', '<=', $cutoffTime)
            ->get();
            
        Log::info('Found pending orders to cancel', ['count' => $pendingOrders->count()]);
        $this->info('Found ' . $pendingOrders->count() . ' orders to auto-cancel');
            
        $count = 0;
        
        foreach ($pendingOrders as $order) {
            Log::info('Processing order for auto-cancellation', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'store_id' => $order->store_id,
                'created_at' => $order->created_at,
                'pending_for_minutes' => Carbon::parse($order->created_at)->diffInMinutes(Carbon::now())
            ]);
            
            try {
                Log::info('Changing order status to CANCELLED', ['order_id' => $order->id]);
                $order->status = 'CANCELLED';
                $order->save();
                
                // Send notification to user
                Log::info('Preparing to send notification to user', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id
                ]);
                
                $orderController = app(OrderController::class);
                $storeName = $order->store->name ?? 'Store';
                
                Log::info('Sending auto-cancel notification', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'store_name' => $storeName
                ]);
                
                $orderController->sendLocalizedNotification(
                    $order->user,
                    'order_auto_cancelled',
                    [
                        'order_id' => $order->id,
                        'store_name' => $storeName
                    ]
                );
                
                $count++;
                
                Log::info('Order auto-cancelled successfully', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'store_id' => $order->store_id,
                    'created_at' => $order->created_at,
                    'cancelled_at' => Carbon::now()->toDateTimeString()
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to auto-cancel order', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                $this->error("Failed to cancel order #{$order->id}: " . $e->getMessage());
            }
        }
        
        Log::info('AutoCancelOrders command completed', [
            'total_orders_processed' => $pendingOrders->count(),
            'successfully_cancelled' => $count,
            'failed_cancellations' => $pendingOrders->count() - $count
        ]);
        
        $this->info("Auto-cancelled {$count} orders.");
        return 0;
    }
}
