<?php
/**
 * PostgreSQL Connection Test Script
 *
 * This script tests the connection to your PostgreSQL database
 * and verifies that the monitoring queries work correctly.
 */

// Database configuration
$config = [
    'host' => '************',
    'port' => 5432,
    'dbname' => 'boutigak_db',
    'username' => 'postgress',
    'password' => 'adminboutigak'
];

echo "🔍 Testing PostgreSQL Connection for Server Monitoring\n";
echo "=====================================================\n\n";

// Test 1: Basic Connection
echo "1. Testing basic PostgreSQL connection...\n";
try {
    $dsn = "pgsql:host={$config['host']};port={$config['port']};dbname={$config['dbname']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 5
    ]);

    echo "   ✅ Connection successful!\n\n";

} catch (Exception $e) {
    echo "   ❌ Connection failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Database Information
echo "2. Getting database information...\n";
try {
    $stmt = $pdo->query("SELECT version()");
    $version = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   PostgreSQL Version: " . $version['version'] . "\n";

    $stmt = $pdo->query("SELECT current_database()");
    $db = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Current Database: " . $db['current_database'] . "\n\n";

} catch (Exception $e) {
    echo "   ❌ Error getting database info: " . $e->getMessage() . "\n\n";
}

// Test 3: Check if users table exists
echo "3. Checking users table...\n";
try {
    $stmt = $pdo->query("
        SELECT COUNT(*) as user_count
        FROM information_schema.tables
        WHERE table_name = 'users' AND table_schema = 'public'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['user_count'] > 0) {
        echo "   ✅ Users table exists\n";

        // Get user count
        $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
        $userCount = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   Total users in database: " . $userCount['total_users'] . "\n";

        // Check if last_activity column exists
        $stmt = $pdo->query("
            SELECT COUNT(*) as column_exists
            FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'last_activity'
        ");
        $columnCheck = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($columnCheck['column_exists'] > 0) {
            echo "   ✅ last_activity column exists\n";
        } else {
            echo "   ⚠️  last_activity column not found\n";
        }

    } else {
        echo "   ❌ Users table not found\n";
    }
    echo "\n";

} catch (Exception $e) {
    echo "   ❌ Error checking users table: " . $e->getMessage() . "\n\n";
}

// Test 4: Test monitoring queries
echo "4. Testing monitoring queries...\n";

// Test active connections
try {
    $stmt = $pdo->query("SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Active connections: " . $result['active_connections'] . "\n";
} catch (Exception $e) {
    echo "   ❌ Error getting active connections: " . $e->getMessage() . "\n";
}

// Test database size
try {
    $stmt = $pdo->prepare("SELECT pg_size_pretty(pg_database_size(?)) as size_pretty");
    $stmt->execute([$config['dbname']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Database size: " . $result['size_pretty'] . "\n";
} catch (Exception $e) {
    echo "   ❌ Error getting database size: " . $e->getMessage() . "\n";
}

// Test slow queries
try {
    $stmt = $pdo->query("
        SELECT count(*) as slow_queries
        FROM pg_stat_activity
        WHERE state = 'active'
        AND query_start < now() - interval '1 second'
        AND query NOT LIKE '%pg_stat_activity%'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Current slow queries: " . $result['slow_queries'] . "\n";
} catch (Exception $e) {
    echo "   ❌ Error getting slow queries: " . $e->getMessage() . "\n";
}

// Test database statistics
try {
    $stmt = $pdo->query("
        SELECT
            xact_commit + xact_rollback as total_queries,
            extract(epoch from (now() - stats_reset)) as uptime_seconds
        FROM pg_stat_database
        WHERE datname = current_database()
        LIMIT 1
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result && $result['uptime_seconds'] > 0) {
        $qps = round($result['total_queries'] / $result['uptime_seconds'], 2);
        echo "   Queries per second: " . $qps . "\n";
    } else {
        echo "   Queries per second: Unable to calculate (stats may have been reset recently)\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error getting database statistics: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Test user activity queries (if users table exists)
echo "5. Testing user activity queries...\n";
try {
    // Test active users query
    $stmt = $pdo->query("
        SELECT COUNT(*) as active_users
        FROM users
        WHERE last_activity > NOW() - INTERVAL '15 minutes'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Active users (last 15 min): " . $result['active_users'] . "\n";

    // Test daily active users
    $stmt = $pdo->query("
        SELECT COUNT(*) as daily_users
        FROM users
        WHERE DATE(last_activity) = CURRENT_DATE
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Daily active users: " . $result['daily_users'] . "\n";

} catch (Exception $e) {
    echo "   ❌ Error testing user activity: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Test API endpoints (if running via web server)
echo "6. Testing API endpoints...\n";

// Check if we're running via web server or command line
if (isset($_SERVER['REQUEST_URI']) && isset($_SERVER['HTTP_HOST'])) {
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);

    $endpoints = [
        'overview.php' => 'Overview API',
        'database.php' => 'Database API'
    ];

    foreach ($endpoints as $endpoint => $name) {
        $url = $baseUrl . '/api/' . $endpoint;
        echo "   Testing $name ($endpoint)...\n";

        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);

        $response = @file_get_contents($url, false, $context);

        if ($response !== false) {
            $data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "     ✅ API response valid JSON\n";
                if (isset($data['error'])) {
                    echo "     ⚠️  API returned error: " . $data['error'] . "\n";
                } else {
                    echo "     ✅ API working correctly\n";
                }
            } else {
                echo "     ❌ Invalid JSON response\n";
            }
        } else {
            echo "     ❌ Could not reach API endpoint\n";
        }
    }
} else {
    echo "   ⚠️  Running via command line - API endpoint testing skipped\n";
    echo "   To test APIs, run this script via web browser or configure a web server\n";
}

echo "\n";
echo "🎉 PostgreSQL connection test completed!\n";
echo "\nNext steps:\n";
echo "1. Update SSH credentials in the API files for remote server monitoring\n";
echo "2. Install php-ssh2 extension for remote server access\n";
echo "3. Configure your web server to serve the monitoring dashboard\n";
echo "4. Access the dashboard at: http://your-server/server-monitoring/\n";
?>
