<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Supprimer la contrainte uniquement si elle existe
        if (Schema::hasTable('users')) {
            try {
                DB::statement('ALTER TABLE users DROP CONSTRAINT IF EXISTS users_location_id_foreign');
            } catch (\Exception $e) {
                // Silence l’erreur pour éviter de bloquer la migration
            }
    
            if (Schema::hasColumn('users', 'location_id')) {
                Schema::table('users', function (Blueprint $table) {
                    $table->dropColumn('location_id');
                });
            }
        }
    
        // Créer la table user_locations
        Schema::create('user_locations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('location_id')->constrained('location')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_locations');
        
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('location_id')->nullable()->constrained('location');
        });
    }
};
