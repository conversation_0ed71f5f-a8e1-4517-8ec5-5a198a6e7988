import 'package:boutigak/controllers/reset_password_controlleur.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordPage extends StatelessWidget {
  final String phoneNumber;  // on reçoit le tél depuis la page ForgotPasswordPage
  ResetPasswordPage({Key? key, required this.phoneNumber}) : super(key: key);

  final resetPasswordController = Get.put(ResetPasswordController());

  final TextEditingController otpController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    // Pré-remplir éventuellement le champ téléphone
    phoneController.text = phoneNumber;

    RxBool newPasswordVisible = RxBool(false);
    RxBool confirmPasswordVisible = RxBool(false);

    return Scaffold(
      appBar: AppBar(title: const Text("Réinitialisation du mot de passe")),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Obx(() => SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              // Champ pour le numéro de téléphone (si l'user veut le modifier)
              TextField(
                controller: phoneController,
                decoration: InputDecoration(
                  hintText: 'Téléphone',
                  suffixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Champ OTP
              TextField(
                controller: otpController,
                decoration: InputDecoration(
                  hintText: 'Code OTP',
                  suffixIcon: const Icon(Icons.lock),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Nouveau mot de passe
              TextField(
                controller: passwordController,
                obscureText: !newPasswordVisible.value,
                decoration: InputDecoration(
                  hintText: 'Nouveau mot de passe',
                  suffixIcon: IconButton(
                    icon: Icon(
                      newPasswordVisible.value
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      newPasswordVisible.value = !newPasswordVisible.value;
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Confirmation mot de passe
              TextField(
                controller: confirmPasswordController,
                obscureText: !confirmPasswordVisible.value,
                decoration: InputDecoration(
                  hintText: 'Confirmer le mot de passe',
                  suffixIcon: IconButton(
                    icon: Icon(
                      confirmPasswordVisible.value
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      confirmPasswordVisible.value =
                          !confirmPasswordVisible.value;
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // Bouton "Réinitialiser"
              SizedBox(
                width: 360,
                child: ElevatedButton(
                  onPressed: resetPasswordController.isLoading.value
                      ? null
                      : () {
                          resetPasswordController.resetPassword(
                            phoneController.text.trim(),
                            otpController.text.trim(),
                            passwordController.text.trim(),
                            confirmPasswordController.text.trim(),
                          );
                        },
                  child: Text(
                    resetPasswordController.isLoading.value
                        ? 'En cours...'
                        : 'Réinitialiser le mot de passe',
                  ),
                ),
              ),

              // Affichage d'erreur
              if (resetPasswordController.showErrorMessage.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    resetPasswordController.showErrorMessage.value,
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
            ],
          ),
        )),
      ),
    );
  }
}
