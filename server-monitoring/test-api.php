<?php
/**
 * API Test Script
 *
 * This script tests the monitoring API endpoints using curl
 */

echo "🔍 Testing Server Monitoring APIs\n";
echo "=================================\n\n";

// Function to test API endpoint
function testApiEndpoint($endpoint, $name) {
    echo "Testing $name...\n";

    // Try to determine the base URL
    $baseUrl = 'http://localhost:8000'; // Default for PHP built-in server

    // If running via web server, try to detect URL
    if (isset($_SERVER['HTTP_HOST'])) {
        $protocol = isset($_SERVER['HTTPS']) ? 'https' : 'http';
        $baseUrl = $protocol . '://' . $_SERVER['HTTP_HOST'];
        if (isset($_SERVER['REQUEST_URI'])) {
            $baseUrl .= dirname($_SERVER['REQUEST_URI']);
        }
    }

    $url = $baseUrl . '/api/' . $endpoint;

    // Try using curl first
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response !== false && empty($error)) {
            $data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "   ✅ $name working (HTTP $httpCode)\n";
                return $data;
            } else {
                echo "   ❌ Invalid JSON response from $name\n";
                echo "   Response: " . substr($response, 0, 200) . "...\n";
                return false;
            }
        } else {
            echo "   ❌ Could not reach $name: $error\n";
            return false;
        }
    } else {
        echo "   ⚠️  cURL not available, skipping $name\n";
        return false;
    }
}

// Test database API
echo "1. Testing Database API...\n";
$dbData = testApiEndpoint('database.php', 'Database API');
if ($dbData) {
    echo "   Status: " . ($dbData['status'] ?? 'unknown') . "\n";
    echo "   CPU: " . ($dbData['cpu'] ?? 'unknown') . "%\n";
    echo "   RAM: " . ($dbData['ram'] ?? 'unknown') . "%\n";
    echo "   Connections: " . ($dbData['connections'] ?? 'unknown') . "\n";
    echo "   Database Size: " . ($dbData['size'] ?? 'unknown') . "\n";

    if (isset($dbData['error'])) {
        echo "   ⚠️  Note: " . $dbData['error'] . "\n";
    }
}

echo "\n";

// Test overview API
echo "2. Testing Overview API...\n";
$overviewData = testApiEndpoint('overview.php', 'Overview API');
if ($overviewData) {
    echo "   Active Users: " . ($overviewData['activeUsers'] ?? 'unknown') . "\n";
    echo "   Total Sessions: " . ($overviewData['totalSessions'] ?? 'unknown') . "\n";
    echo "   Requests/min: " . ($overviewData['requestsPerMin'] ?? 'unknown') . "\n";
    echo "   Avg CPU: " . ($overviewData['avgCpu'] ?? 'unknown') . "%\n";
    echo "   Avg RAM: " . ($overviewData['avgRam'] ?? 'unknown') . "%\n";

    if (isset($overviewData['error'])) {
        echo "   ⚠️  Note: " . $overviewData['error'] . "\n";
    }
}

echo "\n";

// Test app server APIs
echo "3. Testing App Server APIs...\n";

$appServers = [
    'app-server-1.php' => 'App Server 1 API',
    'app-server-2.php' => 'App Server 2 API'
];

foreach ($appServers as $endpoint => $name) {
    $appData = testApiEndpoint($endpoint, $name);
    if ($appData) {
        echo "   Status: " . ($appData['status'] ?? 'unknown') . "\n";
        echo "   CPU: " . ($appData['cpu'] ?? 'unknown') . "%\n";
        echo "   RAM: " . ($appData['ram'] ?? 'unknown') . "%\n";
        echo "   PHP Processes: " . ($appData['phpProcesses'] ?? 'unknown') . "\n";

        if (isset($appData['error'])) {
            echo "   ⚠️  Note: " . $appData['error'] . "\n";
        }
    }
    echo "\n";
}

echo "🎉 API testing completed!\n";
echo "\nTo start a development server for testing:\n";
echo "php -S localhost:8000\n";
echo "Then access: http://localhost:8000/\n";
?>
