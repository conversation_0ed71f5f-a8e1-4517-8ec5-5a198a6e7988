<?php

namespace App\Models;

use App\Models\StorePaymentProvider;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EPaymentProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'logo',
        'has_api',
        'is_active',
        'config',
        'provider_code'
    ];

    protected $casts = [
        'has_api' => 'boolean',
        'is_active' => 'boolean',
        'config' => 'json'
    ];

    /**
     * Get the transactions associated with this provider
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'provider_id');
    }

    /**
     * Get the logo URL attribute
     */
    public function getLogoUrlAttribute(): ?string
    {
        if (!$this->logo) {
            return null;
        }

        $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');
        $storagePath = config('assets.sftp.storage_path', 'storage');
        $path = ltrim($this->logo, '/');

        // Add storage prefix if not already present
        if (!str_starts_with($path, $storagePath . '/')) {
            $path = $storagePath . '/' . $path;
        }

        return $sftpBaseUrl . '/' . $path;
    }

    /**
     * Scope a query to only include active providers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include API-enabled providers
     */
    public function scopeWithApi($query)
    {
        return $query->where('has_api', true);
    }

    /**
     * Check if provider is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if provider has API integration
     */
    public function hasApi(): bool
    {
        return $this->has_api;
    }

    /**
     * Get provider configuration
     */
    public function getConfig(string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->config;
        }

        return data_get($this->config, $key, $default);
    }

    public function stores()
    {
        return $this->hasMany(StorePaymentProvider::class, 'provider_id');
    }
}