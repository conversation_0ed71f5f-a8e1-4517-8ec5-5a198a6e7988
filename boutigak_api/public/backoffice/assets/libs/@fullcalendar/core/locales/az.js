(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = global || self, (global.FullCalendarLocales = global.FullCalendarLocales || {}, global.FullCalendarLocales.az = factory()));
}(this, function () { 'use strict';

    var az = {
        code: "az",
        week: {
            dow: 1,
            doy: 4 // The week that contains Jan 4th is the first week of the year.
        },
        buttonText: {
            prev: "Əvvəl",
            next: "Sonra",
            today: "Bu Gün",
            month: "Ay",
            week: "Həftə",
            day: "Gün",
            list: "Gündəm"
        },
        weekLabel: "Həftə",
        allDayText: "Bütün Gün",
        eventLimitText: function (n) {
            return "+ daha çox " + n;
        },
        noEventsMessage: "<PERSON><PERSON><PERSON><PERSON><PERSON>ə<PERSON> üçün hadisə yoxdur"
    };

    return az;

}));
