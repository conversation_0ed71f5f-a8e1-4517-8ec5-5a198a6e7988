import 'package:boutigak/controllers/otp_controller.dart';
import 'package:boutigak/views/registerandlogin/reset_password_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class ForgotPasswordPage extends StatelessWidget {
  ForgotPasswordPage({super.key});

  final OtpController otpController = Get.put(OtpController());
  final TextEditingController phoneController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double fieldHeight = MediaQuery.of(context).size.height * 0.06;
    double sidePadding = screenWidth * 0.0407;
    double verticalPadding = screenWidth * 0.01;

    return Scaffold(
      appBar: AppBar(
        title: const Text("Mot de passe oublié"),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: sidePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Champ similaire à la page de login
            TextField(
              controller: phoneController,
              decoration: InputDecoration(
                hintText: "Numéro de téléphone",
                suffixIcon: const Icon(Icons.phone),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            SizedBox(height: verticalPadding * 5),

            Obx(() {
              if (otpController.isLoading.value) {
                return const CircularProgressIndicator();
              } else {
                return ElevatedButton(
                  onPressed: () async {
                    await otpController.requestPasswordReset(
                      phoneController.text,
                    );
                    // Si succès, redirige vers la page de réinitialisation
                    // où l'on peut saisir OTP + nouveau mot de passe
                    // Ici on ne fait pas de check direct sur response?['success']
                    // Mais on peut conditionner la navigation si nécessaire
                    Get.to(() => ResetPasswordPage(
                      phoneNumber: phoneController.text,
                    ));
                  },
                  child: const Text("Envoyer la demande"),
                );
              }
            }),
          ],
        ),
      ),
    );
  }
}
