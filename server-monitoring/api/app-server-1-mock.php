<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Mock data for App Server 1
$mockData = [
    'status' => 'online',
    'cpu' => rand(10, 30),
    'ram' => rand(40, 70),
    'disk' => rand(20, 50),
    'uptime' => 'up 8 hours, 30 minutes',
    'loadAverage' => '0.53, 0.33, 0.28',
    'networkIO' => [
        'rx_bytes' => 222338481,
        'tx_bytes' => 41701843
    ],
    'diskIO' => [
        'read_kb_s' => 0.01,
        'write_kb_s' => 0
    ],
    'phpProcesses' => 3,
    'nginxStatus' => 'running',
    'laravelLogs' => 0,
    'queueJobs' => [
        'pending' => 0,
        'processing' => 0,
        'failed' => 0
    ],
    'cacheStats' => [
        'hit_rate' => '95%',
        'memory_usage' => '45%',
        'keys' => 1250
    ],
    'sessionCount' => rand(5, 25),
    'errorRate' => '0.1%',
    'responseTime' => rand(50, 200) . 'ms',
    'timestamp' => time()
];

echo json_encode($mockData);
?>
