# Server Monitoring Application Dockerfile
FROM php:8.2-apache

# Set working directory
WORKDIR /var/www/html

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Basic utilities
    curl \
    wget \
    git \
    unzip \
    vim \
    nano \
    htop \
    # Network tools for monitoring
    net-tools \
    iputils-ping \
    telnet \
    openssh-client \
    # Build dependencies for PHP extensions
    libpq-dev \
    libssh2-1-dev \
    libssl-dev \
    libcurl4-openssl-dev \
    libxml2-dev \
    libzip-dev \
    zlib1g-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libonig-dev \
    # System monitoring tools
    sysstat \
    procps \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions in stages to avoid conflicts
RUN docker-php-ext-configure gd --with-freetype --with-jpeg

# Install core extensions first
RUN docker-php-ext-install -j$(nproc) \
        pdo \
        pdo_pgsql \
        pgsql \
        zip \
        mbstring \
        opcache

# Install GD separately (can be problematic)
RUN docker-php-ext-install gd

# Install SSH2 extension manually (not available via docker-php-ext-install)
RUN pecl install ssh2-1.4 \
    && docker-php-ext-enable ssh2

# Install Redis extension
RUN pecl install redis \
    && docker-php-ext-enable redis

# Configure PHP
RUN echo "memory_limit = 256M" >> /usr/local/etc/php/conf.d/docker-php-memlimit.ini \
    && echo "upload_max_filesize = 64M" >> /usr/local/etc/php/conf.d/docker-php-uploads.ini \
    && echo "post_max_size = 64M" >> /usr/local/etc/php/conf.d/docker-php-uploads.ini \
    && echo "max_execution_time = 300" >> /usr/local/etc/php/conf.d/docker-php-execution.ini \
    && echo "date.timezone = UTC" >> /usr/local/etc/php/conf.d/docker-php-timezone.ini

# Enable Apache modules
RUN a2enmod rewrite \
    && a2enmod headers \
    && a2enmod ssl

# Configure Apache ServerName to suppress warning
RUN echo "ServerName localhost" >> /etc/apache2/apache2.conf

# Configure Apache
COPY docker/apache-config.conf /etc/apache2/sites-available/000-default.conf

# Create Apache configuration if not exists
RUN mkdir -p /var/www/html/docker

# Copy application files (will be overridden by volume in development)
COPY . /var/www/html/

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Create logs directory
RUN mkdir -p /var/log/monitoring \
    && chown www-data:www-data /var/log/monitoring

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/api/database.php || exit 1

# Expose port
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
