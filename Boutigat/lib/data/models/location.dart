class Location {
  final int? id;
  final String? name;
  final double? latitude;
  final double? longitude;
  final String? address;

  Location({
    this.id,
    this.name,
    this.latitude,
    this.longitude,
    this.address,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'],
      name: json['name'],
      latitude: double.tryParse(json['latitude'].toString()),
      longitude: double.tryParse(json['longitude'].toString()),
      address: json['address'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Location &&
      other.id == id &&
      other.name == name &&
      other.latitude == latitude &&
      other.longitude == longitude &&
      other.address == address;
  }

  @override
  int get hashCode => Object.hash(id, name, latitude, longitude, address);
}