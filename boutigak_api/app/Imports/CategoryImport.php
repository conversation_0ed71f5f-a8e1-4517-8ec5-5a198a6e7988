<?php

namespace App\Imports;

use App\Models\Category;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;

class CategoryImport implements ToModel
{
    /**
     * @param array $row
     *
     * @return Model|Category|null
     */
    public function model(array $row): Model|Category|null
    {
        if (strtolower($row[0]) === 'title_en' || strtolower($row[1]) === 'title_ar' || strtolower($row[2]) === 'title_fr' || strtolower($row[3]) === 'parent_id') {
            return null;
        }

        if (Category::query()->where('title_en', $row[0])
            ->where('title_ar', $row[1])
            ->where('title_fr', $row[2])
            ->exists()) {
            return null;
        }

        return new Category([
            'title_en' => $row[0],
            'title_ar' => $row[1],
            'title_fr' => $row[2],
            'parent_id' => $row[3],
        ]);
    }
}
