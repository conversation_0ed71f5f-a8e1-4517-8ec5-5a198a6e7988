<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StorePaymentCode extends Model
{
    protected $table = 'store_payment_code';

    protected $fillable = [
        'paymentCode',
        'store_id',
        'payment_type_id',
        
    ];

    protected $dates = [
        
        'created_at',
        'updated_at',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }
}
