{"version": 3, "sources": ["app-dark.scss", "custom/fonts/_fonts.scss", "app-dark.css", "custom/structure/_topbar.scss", "_variables-dark.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_color-picker.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_round-slider.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_toastr.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_datatable.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_x-editable.scss", "custom/pages/_email.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss"], "names": [], "mappings": "AAAA;;;;;;CAAA;ACKQ,4FAAA;AAER;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,qHAAA;ACEJ;ADEA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,yHAAA;ACAJ;ADIA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,uHAAA;ACFJ;ADMA;EACI,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,kBAAA;EACA,kHAAA;ACJJ;AC/BA;EACI,eAAA;EACA,MAAA;EACA,QAAA;EACA,OAAA;EACA,aAAA;EACA,yBCiBQ;EDhBR,iDCsmB0B;UDtmB1B,yCCsmB0B;AFrkB9B;;AC9BA;EACI,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,sBAAA;EACA,yBAAA;UAAA,8BAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,cAAA;EACA,YCMY;EDLZ,6BAAA;ADiCJ;AC9BQ;EACI,yBAAA;ADgCZ;;AC3BA;EACI,iBAAA;EACA,kBAAA;EACA,YCyBqB;AFKzB;;AC3BA;EACI,iBAAA;AD8BJ;AC5BI;EACI,aAAA;AD8BR;;AC1BA;EACI,aCUY;AFmBhB;;AC1BA;EACI,cCKW;AFwBf;;AC1BA,WAAA;AAEA;EACI,yBAAA;AD4BJ;AC1BI;EACI,YAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,yBCxBW;EDyBX,wBAAA;UAAA,gBAAA;EACA,mBAAA;AD4BR;AC1BI;EACI,kBAAA;EACA,WAAA;EACA,eAAA;EACA,iBAAA;EACA,UAAA;EACA,MAAA;EACA,cCmBG;AFSX;;ACrBI;EACI,kBAAA;EACA,gBAAA;ADwBR;ACvBQ;EACI,cCMD;AFmBX;;ACpBA;EACI;IACI,WAAA;EDuBN;;ECjBM;IACI,aAAA;EDoBV;ECjBM;IACI,qBAAA;EDmBV;AACF;ACfA;EACI,6DAAA;ADiBJ;;ACdA;EACI,YCtFY;EDuFZ,mCAAA;UAAA,2BAAA;EACA,cCtFgB;EDuFhB,SAAA;EACA,kBAAA;ADiBJ;ACfI;EACI,cC3FY;AF4GpB;;ACXA;EACI,YAAA;EACA,WAAA;EACA,yBCvCO;EDwCP,YAAA;ADcJ;;ACTQ;EACI,qBAAA;ADYZ;;ACNI;EACI,eAAA;EACA,cCnHY;AF4HpB;ACNI;EACI,kBAAA;EACA,qBAAA;EACA,WAAA;EACA,UAAA;EACA,yBChCE;EDiCF,kBAAA;EACA,SAAA;EACA,WAAA;ADQR;;ACHI;EACI,qBAAA;ADMR;ACJQ;EACI,yBC1ED;AFgFX;;ACAA;EACI,cAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,mBAAA;EACA,cAAA;EACA,6BAAA;EACA,cCrFO;AFwFX;ACDI;EACI,YAAA;ADGR;ACAI;EACI,cAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;ADER;ACCI;EACI,qBCtGG;AFuGX;;ACMQ;EACI,gBAAA;ADHZ;;ACSI;EACI,yBC/KS;AFyKjB;ACUY;EACI,2CAAA;ADRhB;ACYQ;EACI,oCAAA;ADVZ;ACcI;EACI,cC7LiB;AFiLzB;ACcQ;EACI,cChMa;AFoLzB;ACgBI;EACI,2CAAA;ADdR;ACkBQ;EACI,cC1Ma;AF0LzB;ACoBI;EACI,aAAA;ADlBR;ACqBI;EACI,cAAA;ADnBR;ACwBQ;EACI,wCAAA;EACA,WCpKD;AF8IX;ACwBQ;;EAEI,+BAAA;ADtBZ;;AC6BI;EACI,mBCpPU;AF0NlB;AC6BI;EACI,aAAA;AD3BR;AC8BI;EACI,cAAA;AD5BR;;ACgCA;EAEQ;IACI,gBAAA;ED9BV;ECgCU;IACI,qBAAA;IACA,sBAAA;ED9Bd;AACF;ACmCA;EACI;IACI,aAAA;EDjCN;AACF;ACqCI;EACI,WAAA;ADnCR;ACqCI;EACI,gBChRQ;EDiRR,6DAAA;ADnCR;;ACuCA;EAEQ;IACI,gBAAA;EDrCV;AACF;AC4CI;EACI,yBCxRY;AF8OpB;AC8CY;EACI,2CAAA;AD5ChB;ACgDQ;EACI,oCAAA;AD9CZ;ACkDI;EACI,cC1SiB;AF0PzB;ACkDQ;EACI,cC7Sa;AF6PzB;ACoDI;EACI,2CAAA;ADlDR;ACsDQ;EACI,cCvTa;AFmQzB;ACwDI;EACI,aAAA;ADtDR;ACyDI;EACI,cAAA;ADvDR;AC4DQ;EACI,wCAAA;EACA,WCjRD;AFuNX;AC4DQ;;EAEI,+BAAA;AD1DZ;;AG7SA;EACI,oBDykB0B;AFzR9B;AG9SI;EACI,6BAAA;EACA,UAAA;AHgTR;AG7SI;EACI,eAAA;EACA,yBAAA;EACA,gBAAA;AH+SR;;AI1TA;EACI,SAAA;EACA,4BAAA;EACA,kBAAA;EACA,QAAA;EACA,cFkCW;EEjCX,WFGa;EEFb,YF8BY;EE7BZ,iDFqmB0B;UErmB1B,yCFqmB0B;EEpmB1B,yBF6BQ;AFgSZ;;AI1TA;EACI;IACI,OAAA;EJ6TN;AACF;AIxTI;EACI,UFXmB;AFqU3B;;AIrTI;EACI,kBAAA;AJwTR;;AKnVA;EACI,yBHwzCgC;EGvzChC,iFAAA;UAAA,yEAAA;EACA,cAAA;EACA,eAAA;EACA,sCAAA;EAAA,8BAAA;EACA,YHyCc;EGxCd,aAAA;EACA,uBAAA;EACA,aAAA;EACA,MAAA;EACA,SAAA;ALsVJ;AKpVI;EACI,uBAAA;EACA,YAAA;EACA,WAAA;EACA,iBAAA;EACA,cHkEG;EGjEH,kBAAA;EACA,kBAAA;ALsVR;AKpVQ;EACI,uBAAA;ALsVZ;;AKhVA;EACI,2CAAA;EACA,kBAAA;EACA,OAAA;EACA,QAAA;EACA,MAAA;EACA,SAAA;EACA,aAAA;EACA,aAAA;EACA,qCAAA;EAAA,6BAAA;ALmVJ;;AK/UI;EACI,QAAA;ALkVR;AKhVI;EACI,cAAA;ALkVR;;AMxTI;EDrBA;IACI,cAAA;ELiVN;EKhVM;IACI,uBAAA;ELkVV;AACF;AOzYA;EACI,SAAA;AP2YJ;AOzYI;EACI,cAAA;EACA,WAAA;AP2YR;AOxYI;EACI,aAAA;AP0YR;AOxYQ;EACI,aAAA;AP0YZ;AOvYQ;EACI,cAAA;APyYZ;AOrYI;EACI,kBAAA;EACA,SAAA;EACA,gBAAA;EACA,wCAAA;UAAA,gCAAA;EACA,kCAAA;UAAA,0BAAA;EACA,+CAAA;EAAA,uCAAA;APuYR;;AOlYA;EACI,YLvBa;EKwBb,aAAA;EACA,mBL/BS;EKgCT,SAAA;EACA,aAAA;EACA,eAAA;EACA,SLhBY;EKiBZ,iDLskB0B;UKtkB1B,yCLskB0B;AFjM9B;;AOlYA;EACI,kBLlCa;EKmCb,gBAAA;APqYJ;AOnYI;EACI,yBAAA;EACA,gBL1BQ;AF+ZhB;;AOhYA;EACI,sBAAA;APmYJ;AO/XY;EACI,kCAAA;UAAA,0BAAA;APiYhB;AO3XQ;EACI,iBAAA;EACA,oCAAA;EACA,cAAA;EACA,YAAA;EACA,0CAAA;EAAA,kCAAA;EAAA,0BAAA;EAAA,kDAAA;EACA,eAAA;AP6XZ;AOvXY;EACI,cAAA;EACA,wBAAA;EACA,cL5EU;EK6EV,kBAAA;EACA,iBAAA;EACA,4BAAA;EAAA,oBAAA;EACA,gCLZa;EKab,gBAAA;APyXhB;AOvXgB;EACI,qBAAA;EACA,iBAAA;EACA,uBAAA;EACA,iBAAA;EACA,uBAAA;EACA,sBAAA;EACA,cLxFW;EKyFX,4BAAA;EAAA,oBAAA;EACA,aAAA;APyXpB;AOtXgB;EACI,cL7FY;AFqdhC;AOtXoB;EACI,cLhGQ;AFwdhC;AOnXY;EACI,eAAA;APqXhB;AOlXY;EACI,UAAA;APoXhB;AOhXoB;EACI,oCAAA;EACA,eAAA;EACA,cLnHM;AFqe9B;AO/WoB;EACI,UAAA;APiXxB;AO9W4B;EACI,oCAAA;EACA,iBAAA;APgXhC;;AOrWA;EACI,6BAAA;EACA,sBAAA;EACA,oBAAA;EACA,eAAA;EACA,eAAA;EACA,yBAAA;EACA,cL7I2B;EK8I3B,gBLtFmB;EKuFnB,gCL7EyB;EK8EzB,YAAA;APwWJ;;AOpWA;EACI,yBAAA;APuWJ;AOtWI;EACI,yBAAA;APwWR;AOvWQ;EACI,yBAAA;APyWZ;AOtWI;EACI,yBAAA;APwWR;AOtWI;EACI,yBAAA;APwWR;AOtWQ;EACI,yBAAA;APwWZ;;AOnWA;EACI;IACI,aAAA;EPsWN;;EOnWE;IACI,yBAAA;EPsWN;;EOlWM;IACI,cAAA;EPqWV;AACF;AO9VI;EACI,iBLxLmB;AFwhB3B;AO7VI;EACI,sBAAA;AP+VR;AO3VQ;EACI,aAAA;AP6VZ;AO1VQ;EACI,cAAA;AP4VZ;AOvVI;EACI,kBAAA;EACA,sBAAA;EACA,UAAA;APyVR;AOvVQ;;EAEI,4BAAA;APyVZ;AOtVQ;EACI,wBAAA;APwVZ;AOrVQ;EACI,oBAAA;APuVZ;AOjVY;;;EAGI,wBAAA;APmVhB;AOhVY;EACI,0BAAA;APkVhB;AO9UgB;EACI,aAAA;APgVpB;AO3UgB;EACI,kBAAA;EACA,mBAAA;AP6UpB;AO3UoB;EACI,kBAAA;EACA,gBAAA;EACA,wBAAA;EAAA,gBAAA;AP6UxB;AO3UwB;EAGI,cL/PI;AF0kBhC;AOxUwB;EACI,eAAA;EACA,gBAAA;AP0U5B;AOvUwB;EACI,aAAA;EACA,kBAAA;APyU5B;AOpUwB;EACI,kBAAA;EACA,yBAAA;EACA,cLvKlB;EKwKkB,yBAAA;EACA,wBAAA;EAAA,gBAAA;APsU5B;AOpU4B;EACI,cL5KtB;AFkfV;AOnU4B;EACI,eAAA;APqUhC;AOjUwB;EACI,cAAA;EACA,UL7RD;EK8RC,kBAAA;EACA,YAAA;EACA,uBAAA;EACA,2DAAA;UAAA,mDAAA;APmU5B;AOjU4B;EACI,2DAAA;UAAA,mDAAA;APmUhC;AOhU4B;EACI,wBAAA;UAAA,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,YAAA;EACA,UAAA;EACA,cLlTF;AFonB9B;AOhUgC;EACI,cLnTJ;AFqnBhC;AO3TgB;EACI,cAAA;EACA,aAAA;EACA,aAAA;EACA,yBLlUP;AF+nBb;AOzT4B;EACI,cAAA;EACA,WAAA;EACA,uBAAA;EACA,iBAAA;EACA,kBAAA;EACA,YAAA;AP2ThC;AOtT4B;EACI,kBAAA;EACA,WAAA;EACA,SAAA;EACA,iCAAA;UAAA,yBAAA;APwThC;AOlTwB;EACI,cL5QjB;AFgkBX;;AOtSQ;EACI,WL5RD;AFqkBX;AOvSQ;EACI,yBAAA;APySZ;AOtSI;EACI,mBLvWU;AF+oBlB;AOjSgB;EACI,cL9WW;AFipB/B;AOjSoB;EACI,cL/WY;AFkpBpC;AOhSoB;EACI,cLlXa;AFopBrC;AOhSwB;EACI,cLrXS;AFupBrC;AO1RwB;EACI,cLhYO;AF4pBnC;AO1R4B;EACI,cLjYK;AF6pBrC;AOnRI;EACI,kBAAA;APqRR;AOzQ4B;EACI,mBAAA;EACA,cLzZK;AFoqBrC;AO1QgC;EACI,cL3ZC;AFuqBrC;AOvQgC;EACI,cLnaD;AF4qBnC;AOxQoC;EACI,cL9aR;AFwrBhC;AOnQoB;EACI,yBAAA;APqQxB;AO5P4B;EACI,yBAAA;AP8PhC;AOtP+B;EACK,yBAAA;APwPpC;AOnPgC;EACI,yBAAA;APqPpC;AOrOI;EACI,yBAAA;APuOR;AOtOQ;EACI,yBAAA;APwOZ;AOvOY;EACI,yBAAA;APyOhB;AOtOQ;EACI,yBAAA;APwOZ;AOtOQ;EACI,yBAAA;APwOZ;AOtOY;EACI,yBAAA;APwOhB;AOnOI;EACI,cL1e4B;AF+sBpC;;AO/NI;EACI,yBAAA;APkOR;;AO3NI;EACI,YL/fY;AF6tBpB;AO5NQ;EAHJ;IAIQ,WAAA;EP+NV;AACF;AO7NI;EACI,YLtgBY;EKugBZ,kBAAA;AP+NR;AO7NQ;;EAEI,wBAAA;AP+NZ;AO5NI;EACI,kBL/gBY;AF6uBpB;AO5NI;EACI,WLlhBY;AFgvBpB;AO7NQ;EAFJ;IAGQ,OAAA;EPgOV;AACF;AO3NY;EACI,yBAAA;AP6NhB;AO1NgB;EACI,cAAA;AP4NpB;AOvNoB;EACI,oBAAA;APyNxB;AOpN4B;EACI,oBAAA;APsNhC;AO7MQ;EACI,iBLtjBe;AFqwB3B;AO5MY;EACI,gBAAA;AP8MhB;AO1M4B;EACI,qBAAA;AP4MhC;AOrMQ;EACI,ULvkBe;AF8wB3B;;AO9LQ;EACI,WLzgBD;AF0sBX;AO/LQ;EACI,yBAAA;APiMZ;AO7LI;EACI,mBLlfE;AFirBV;AO7LI;EACI,yBLrfE;AForBV;AO9LQ;EACI,aAAA;APgMZ;AO9LQ;EACI,cAAA;APgMZ;AOzLgB;EACI,+BAAA;AP2LpB;AOxLgB;EACI,+BAAA;AP0LpB;AOzLoB;EACI,+BAAA;AP2LxB;AOxLwB;EACE,oCAAA;AP0L1B;AOnLwB;EACI,+BAAA;APqL5B;AOvKwB;EACI,yBAAA;EACA,WLnkBjB;AF4uBX;AOxK4B;EACI,WLrkBrB;AF+uBX;AO/J4B;EACI,yBAAA;APiKhC;AOzJ+B;EACK,yBAAA;AP2JpC;AOtJgC;EACI,yBAAA;APwJpC;AO1II;EACI,sBAAA;AP4IR;AO3IQ;EACI,sBAAA;AP6IZ;AO5IY;EACI,sBAAA;AP8IhB;AO3IQ;EACI,sBAAA;AP6IZ;AO3IQ;EACI,sBAAA;AP6IZ;AO3IY;EACI,sBAAA;AP6IhB;AOxII;EACI,sBAAA;AP0IR;;AOrII;EACI,aAAA;APwIR;;AQn2BA;EACI,mBNyCU;EMxCV,yBAAA;EACA,iDN0mB0B;UM1mB1B,yCN0mB0B;EMzmB1B,gBNkBY;EMjBZ,eAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;ARs2BJ;AQp2BI;EACI,SAAA;EACA,UAAA;ARs2BR;AQj2BQ;EACI,eAAA;EACA,kBAAA;EACA,oBAAA;EACA,cNsBM;EMrBN,gCNkDiB;AFizB7B;AQj2BY;EACI,eAAA;EACA,sBAAA;EACA,qBAAA;ARm2BhB;AQj2BY;EACI,cNcS;EMbT,6BAAA;ARm2BhB;AQ/1BQ;EACI,cNOM;AF01BlB;AQh2BY;EACI,cNMS;AF41BzB;AQ71BY;EACI,cAAA;AR+1BhB;AQz1Bc;EACM,cNPK;EMQL,6BAAA;AR21BpB;;AMp1BI;EEGI;;IAEI,cAAA;ERq1BV;AACF;AM31BI;EEegB;IACI,eAAA;ER+0BtB;EQz0BM;IACI,sBAAA;IACA,gBAAA;ER20BV;EQr0Bc;IACI,SAAA;IACA,WAAA;ERu0BlB;EQp0BU;IACI,aAAA;IACA,kCAAA;ERs0Bd;EQn0BkB;IACI,WAAA;IACA,mDAAA;YAAA,2CAAA;IACA,kBAAA;ERq0BtB;EQh0BkB;IACI,kBAAA;IACA,iBAAA;IACA,UAAA;IACA,aAAA;ERk0BtB;EQ5zBc;IACI,cAAA;ER8zBlB;EQzzBM;IACI,cAAA;ER2zBV;;EQvzBE;IACI,aAAA;ER0zBN;AACF;AQvzBA;EACI,qBAAA;ARyzBJ;AQvzBI;EACI,qBAAA;EACA,mBAAA;EACA,yBAAA;EACA,WAAA;EACA,aAAA;EACA,qBAAA;EACA,UAAA;EACA,QAAA;EACA,iBAAA;EACA,kDAAA;UAAA,0CAAA;EACA,6BAAA;UAAA,qBAAA;EACA,qCAAA;EAAA,6BAAA;EACA,YAAA;ARyzBR;;AMt4BI;EEwFoB;IACI,WAAA;IACA,UAAA;ERkzB1B;AACF;AM74BI;EEsGI;IACI,aN5HI;EFs6Bd;EQzyBU;IACI,aN9HA;EFy6Bd;EQvyBM;IACI,cNpIG;EF66Bb;;EQryBE;IACI,iBAAA;IACA,gBAAA;IACA,UAAA;ERwyBN;EQtyBU;IACI,uBAAA;ERwyBd;EQnyBU;IACI,6BAAA;IACA,YAAA;IACA,wBAAA;YAAA,gBAAA;IACA,kBAAA;ERqyBd;EQpyBc;IACI,WAAA;ERsyBlB;EQpyBkB;IACI,WAAA;ERsyBtB;EQjyBU;IACI,kBAAA;IACA,6BAAA;ERmyBd;EQjyBc;IAEI,cNxGV;EF04BR;EQ5xBU;IACI,WAAA;IACA,kBAAA;ER8xBd;AACF;AMr8BI;EEiLQ;IACI,aN1LA;EFi9Bd;EQpxBU;IACI,cN/LD;EFq9Bb;EQlxBM;IACI,yBN3NK;EF++Bf;EQjxBc;IACI,+BAAA;ERmxBlB;EQjxBkB;IACI,+BAAA;ERmxBtB;EQ7wBkB;IACQ,0CAAA;ER+wB1B;AACF;ASphCA;EACI,yBPyDkB;AF69BtB;ASrhCI;EACI,yBPieoB;EOhepB,iBPqDgB;EOpDhB,cAAA;EACA,iDPumBsB;UOvmBtB,yCPumBsB;AFgb9B;ASphCI;EACI,iBP+CgB;EO9ChB,cAAA;ATshCR;ASnhCI;EACI,cAAA;EACA,+BAAA;ATqhCR;ASjhCQ;EACI,8BAAA;ATmhCZ;;AS1gCI;EACI,eAAA;AT6gCR;AS3gCI;EACI,iBPuBgB;AFs/BxB;;AUljCA;;;;;;uDAAA;AAOC;EACG,kBAAA;EACA,eAAA;EACA,qBAAA;EACA,gBAAA;EACA,yBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,wCAAA;AVqjCJ;;AUnjCE;EACE,kBAAA;EACA,kBAAA;EACA,YAAA;EACA,aAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,8BAAA;EAIA,qJAAA;EACA,qCAAA;EAGA,6BAAA;EACA,uDAAA;EAGA,uDAAA;EAAA,+CAAA;EAAA,uCAAA;EAAA,0DAAA;EACA,2CAAA;EAIA,mCAAA;EACA,oBAAA;AVsjCJ;;AUpjCE;EACE,oCAAA;EAIA,6KAAA;AVujCJ;;AUrjCE;EACE,8BAAA;AVwjCJ;;AUtjCE;EACE,oCAAA;AVyjCJ;;AUvjCE;EACE,mCAAA;EAGA,2BAAA;AV0jCJ;;AUxjCE;;EAEE,gCAAA;EAIA,wBAAA;EACA,2EAAA;AV2jCJ;;AUzjCE;;;;EAIE,mBAAA;EACA,sBAAA;EACA,eAAA;EACA,YAAA;EACA,aAAA;EACA,cAAA;EACA,kCAAA;EACA,cAAA;EACA,gBAAA;EACA,kBAAA;EACA,qBAAA;EACA,UAAA;AV4jCJ;;AU1jCE;EACE,qBAAA;EACA,oBAAA;AV6jCJ;;AU3jCE;EACE,SAAA;EACA,qBAAA;AV8jCJ;;AU5jCE;EACE,oBAAA;EACA,sBAAA;AV+jCJ;;AU7jCE;EACE,UAAA;AVgkCJ;;AU9jCE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,UAAA;AVikCJ;;AU/jCE;EACE,kBAAA;EACA,YAAA;EACA,aAAA;EACA,kBAAA;EACA,kBAAA;AVkkCJ;;AUhkCE;EACE,wBAAA;EACA,yDAAA;EACA,iDAAA;EACA,6BAAA;EAGA,qBAAA;AVmkCJ;;AUjkCE;EACE,uDAAA;EACA,+CAAA;AVokCJ;;AUlkCE;EACE,cAAA;AVqkCJ;;AUjkCI;EACI,0CAAA;AVokCR;;AU/jCI;EACI,yCAAA;AVkkCR;;AU9jCI;EACI,0CAAA;AVikCR;;AU7jCI;EACI,wCAAA;AVgkCR;;AU5jCI;EACI,yCAAA;AV+jCR;;AU3jCI;EACI,wCAAA;AV8jCR;;AW/tCA;EACE,YAAA;EACA,WAAA;AXkuCF;;AW/tCA;EACE,YAAA;EACA,WAAA;AXkuCF;;AW/tCA;EACE,cAAA;EACA,aAAA;AXkuCF;;AW/tCA;EACE,YAAA;EACA,WAAA;AXkuCF;;AW/tCA;EACE,cAAA;EACA,aAAA;AXkuCF;;AW/tCA;EACE,yBAAA;MAAA,sBAAA;UAAA,mBAAA;EACA,yBTsFQ;ESrFR,WTsDS;ESrDT,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,gBT+BmB;ES9BnB,YAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;EACA,WAAA;AXkuCF;;AW7tCA;EACE,kBAAA;EACA,oBAAA;EAAA,oBAAA;EAAA,aAAA;EACA,mBAAA;MAAA,eAAA;AXguCF;AW/tCE;EACE,kBAAA;EACA,yBAAA;EACA,kBAAA;EACA,4BAAA;EAAA,oBAAA;AXiuCJ;AWhuCI;EACE,kBAAA;EACA,mCAAA;UAAA,2BAAA;AXkuCN;;AYhxCQ;EACI,kBAAA;AZmxCZ;AY5wCgB;EACI,iBAAA;AZ8wCpB;AYxwCI;EACI,kBAAA;AZ0wCR;;AYrwCI;EACI,yBAAA;EACA,wBAAA;UAAA,gBAAA;AZwwCR;AYtwCI;EACI,kBAAA;EACA,kBAAA;AZwwCR;AYtwCQ;EACI,kBAAA;EACA,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,yBV0EF;EUzEE,WV0CD;EUzCC,kBAAA;EACA,kBAAA;EACA,UAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;AZwwCZ;AYjwCgB;EACI,iBAAA;AZmwCpB;;AavzCA;EACI,gCXsEyB;AFovC7B;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AavzCA;EACI,0BAAA;Ab0zCJ;;AanzCA;EACI,YAAA;EACA,WAAA;EACA,6BAAA;EACA,cAAA;EACA,yBAAA;EACA,kBAAA;EACA,cXuBO;EWtBP,kBAAA;EACA,4BAAA;EAAA,oBAAA;AbszCJ;AapzCI;EACI,cXmBG;EWlBH,yBXcG;AFwyCX;;AajzCA;EACI,eAAA;AbozCJ;;AajzCA;EACI,eAAA;AbozCJ;;AajzCA;EACI,gBAAA;AbozCJ;;AajzCA;EACI,gBAAA;AbozCJ;;AajzCA;EACI,gBAAA;AbozCJ;;Aa/yCA;EACI,kBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,YAAA;EACA,sBXdO;AFg0CX;;Aa7yCA;EACI,mBAAA;MAAA,WAAA;UAAA,OAAA;AbgzCJ;;AaxyCI;EACI,eAAA;EACA,wBAAA;EACA,2WAAA;Ab2yCR;;Act6CA;EACI,eAAA;EACA,MAAA;EACA,OAAA;EACA,QAAA;EACA,SAAA;EACA,yBZmzCgC;EYlzChC,aAAA;Ady6CJ;;Act6CA;EACI,kBAAA;EACA,SAAA;EACA,QAAA;EACA,mCAAA;UAAA,2BAAA;EACA,uBAAA;Ady6CJ;;Acr6CI;EACI,eAAA;EACA,cZ4FE;EY3FF,kBAAA;EACA,qBAAA;EACA,4CAAA;UAAA,oCAAA;Adw6CR;;Acp6CA;EACI;IACE,+BAAA;YAAA,uBAAA;Edu6CJ;Ecr6CE;IACE,iCAAA;YAAA,yBAAA;Edu6CJ;AACF;;Ac76CA;EACI;IACE,+BAAA;YAAA,uBAAA;Edu6CJ;Ecr6CE;IACE,iCAAA;YAAA,yBAAA;Edu6CJ;AACF;Aet8CA;EACE,eAAA;EACA,qBAAA;EACA,oBbq8BwC;AFmgB1C;Aev8CE;EACE,YAAA;EACA,cAAA;EACA,oBAAA;Afy8CJ;Aev8CE;EACE,cAAA;Afy8CJ;;Aer8CA;EACE,kBAAA;EACA,gBAAA;Afw8CF;;Aep8CA;EACE,eAAA;EACA,gBAAA;Afu8CF;;AgB59CA;EACI,eAAA;EACA,iBAAA;AhB+9CJ;AgB79CI;EACI,kBAAA;EACA,sBAAA;AhB+9CR;AgB79CQ;EACI,WAAA;EACA,gDAAA;EACA,kBAAA;EACA,OAAA;EACA,SAAA;EACA,SAAA;AhB+9CZ;AgB79CQ;EACI,kBAAA;EACA,WAAA;EACA,MAAA;EACA,UAAA;AhB+9CZ;AgB59CQ;EACI,mBAAA;AhB89CZ;;AiBz/CA;EACI,iBAAA;EACA,oBAAA;AjB4/CJ;AiB1/CI;EACI,mBAAA;EACA,gBAAA;AjB4/CR;;AiBt/CA;EACI,iBAAA;AjBy/CJ;;AiBp/CA;EACI,kBAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,UAAA;EACA,cAAA;AjBu/CJ;;AiBl/CA;EACE,cfuDS;AF87CX;AiBn/CE;EACE,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,cfgDO;Ee/CP,yBAAA;EACA,kBAAA;EACA,4BAAA;EAAA,oBAAA;EACA,kBAAA;EACA,kBAAA;EACA,sBAAA;AjBq/CJ;AiBl/CE;EACE,gBAAA;AjBo/CJ;AiBj/CM;EACE,Wf4BG;Ee3BH,yBf0DE;EezDF,qBfyDE;AF07CV;;AiBz+CI;EACI,yBfgBG;EefH,gBAAA;EACA,iBAAA;EACA,gBfTa;EeUb,kBAAA;AjB4+CR;;AiBr+CA;EACE,yBf0uCkC;EezuClC,yBAAA;EACA,sBfohB4B;EenhB5B,aAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;EACA,cAAA;AjBw+CF;AiBt+CE;EACE,eAAA;AjBw+CJ;;AiBp+CA;EACE,cAAA;AjBu+CF;;AiBn+CA;EACE,aAAA;AjBs+CF;AiBr+CE;EACE,gCAAA;AjBu+CJ;;AiBl+CE;EACI,gBAAA;AjBq+CN;AiBp+CM;EACI,WAAA;EACA,YAAA;EACA,4BAAA;EACA,0BAAA;EACA,6BAAA;EACA,qDAAA;EACA,yBAAA;EACA,0BAAA;AjBs+CV;;AkBzlDA;EACI;;;;;IAKI,wBAAA;ElB4lDN;;EkB1lDE;;;;;IAKI,UAAA;IACA,SAAA;ElB6lDN;;EkB1lDE;IACI,SAAA;ElB6lDN;AACF;AmBrnDA;EACE,kBAAA;EACA,4BAAA;EAAA,6BAAA;MAAA,0BAAA;UAAA,sBAAA;EACA,mBAAA;MAAA,eAAA;EACA,uBAAA;MAAA,oBAAA;UAAA,2BAAA;EACA,yBAAA;MAAA,yBAAA;EACA,wBAAA;MAAA,qBAAA;UAAA,uBAAA;AnBunDF;;AmBpnDA;EACE,gBAAA;EACA,cAAA;EACA,eAAA;EACA,kBAAA;EACA,mBAAA;AnBunDF;;AmBpnDA;EACE,kBAAA;EACA,kBAAA;EACA,gBAAA;EACA,UAAA;EACA,SAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,QAAA;EACA,sBAAA;EACA,uBAAA;EACA,UAAA;AnBunDF;;AmBpnDA;EACE,6BAAA;EACA,sCAAA;UAAA,8BAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,kBAAA;EACA,SAAA;EACA,mBAAA;EACA,UAAA;EACA,SAAA;EACA,iCAAA;AnBunDF;;AmBpnDA;EACE,kBAAA;EACA,yCAAA;UAAA,iCAAA;EACA,kBAAA;EACA,cAAA;EACA,YAAA;EAAc,mGAAA;EACd,WAAA;EACA,mBAAA;EACA,cAAA;EAAgB,mFAAA;EAChB,eAAA;EAAiB,kDAAA;EACjB,gBAAA;EAAkB,0CAAA;EAClB,qBAAA;EACA,uBAAA;AnB2nDF;;AmBxnDA;;EAEE,aAAA;AnB2nDF;;AmBxnDA;;EAEE,YAAA;EACA,cAAA;AnB2nDF;;AmBxnDA;EACE,gBAAA;EACA,eAAA;EACA,WAAA;EACA,oBAAA;AnB2nDF;;AmBxnDA;EACE,sCAAA;UAAA,8BAAA;EACA,YAAA;EACA,WAAA;EACA,cAAA;EACA,kBAAA;EACA,WAAA;EACA,eAAA;EACA,gBAAA;EACA,WAAA;EACA,UAAA;EACA,SAAA;EACA,oBAAA;EACA,yBAAA;MAAA,0BAAA;UAAA,kBAAA;EACA,oBAAA;MAAA,cAAA;EACA,0BAAA;MAAA,aAAA;AnB2nDF;;AmBxnDA;EACE,2BAAA;UAAA,mBAAA;EACA,cAAA;EACA,UAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,aAAA;EACA,YAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,oBAAA;EACA,WAAA;AnB2nDF;;AmBxnDA;EACE,UAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,oBAAA;EACA,gBAAA;AnB2nDF;;AmBxnDA;EACE,oBAAA;EACA,sBAAA;GAAA,qBAAA;OAAA,iBAAA;EACA,yBAAA;AnB2nDF;;AmBxnDA;EACE,mBAAA;AnB2nDF;;AmBxnDA;EACE,kBAAA;EACA,UAAA;EACA,UAAA;EACA,gBAAA;AnB2nDF;;AmBxnDA;EACE,kBAAA;EACA,WAAA;EACA,mBAAA;EACA,kBAAA;EACA,OAAA;EACA,QAAA;EACA,UAAA;EACA,uCAAA;EAAA,+BAAA;AnB2nDF;;AmBxnDA;EACE,0DAAA;EACA,YAAA;EACA,qCAAA;EAAA,6BAAA;AnB2nDF;;AmBxnDA;EACE,MAAA;EACA,WAAA;AnB2nDF;;AmBxnDA;EACE,QAAA;EACA,WAAA;AnB2nDF;;AmBxnDA;EACE,OAAA;EACA,YAAA;AnB2nDF;;AmBxnDA;EACE,YAAA;EACA,SAAA;EACA,UAAA;AnB2nDF;;AmBxnDA;EACE,WAAA;EACA,OAAA;EACA,QAAA;EACA,WAAA;EACA,aAAA;EACA,eAAA;EACA,WAAA;AnB2nDF;;AmBxnDA,gBAAA;AACA;EACE,WAAA;EACA,OAAA;AnB2nDF;;AmBxnDA;EACE,cAAA;EACA,eAAA;EACA,UAAA;EACA,kBAAA;EACA,aAAA;EACA,YAAA;EACA,kBAAA;EACA,kBAAA;AnB2nDF;;AmBxnDA;EACE,eAAA;EACA,OAAA;EACA,kBAAA;EACA,kBAAA;EACA,qBAAA;AnB2nDF;;AmBxnDA;EACE,YAAA;AnB2nDF;;AoB30DE;EACI,eAAA;EACA,iBAAA;EACA,yBAAA;ApB80DN;;AoBz0DE;EACI,mBlB2EK;EkB1EL,eAAA;EACA,iBAAA;EACA,eAAA;EACA,yBAAA;EACA,gBlB+CiB;AF6xDvB;;AoBv0DE;;;;;;;;;;EAUI,qBlBuDK;AFmxDX;AoBx0DE;EACI,mBAAA;ApB00DN;;AoBt0DA;EACE,mBlBmxCkC;EkBlxClC,qBlB8CS;EkB7CT,clBiDS;EkBhDT,0BAAA;EACA,wBAAA;UAAA,gBAAA;EACA,4BAAA;EACA,uBAAA;ApBy0DF;;AoBt0DA;;;EAGE,yBlB+DQ;EkB9DR,WlB+BS;EkB9BT,iBAAA;ApBy0DF;;AoBt0DA;EACE,kBAAA;EACA,YAAA;EACA,YAAA;EACA,oBAAA;EACA,eAAA;EACA,gBAAA;EACA,kBAAA;ApBy0DF;;AoBt0DA;EACE,2BAAA;EACA,iBAAA;ApBy0DF;;AoBt0DA;EACE,yBlB0CQ;AF+xDV;;AoBt0DA;EACE,WlBOS;AFk0DX;;AoBp0DI;EACE,qBlBIK;AFm0DX;AoBl0DI;EADF;IAEI,cAAA;EpBq0DJ;AACF;AoBn0DM;EACI,eAAA;EACA,iBAAA;EACA,yBAAA;ApBq0DV;AoBl0DM;EAEI;;;IAGI,WAAA;IACA,cAAA;IACA,kBAAA;IACA,WAAA;IACA,cAAA;EpBm0DZ;EoBh0DQ;IACI,WAAA;EpBk0DZ;EoB/zDQ;IACI,aAAA;EpBi0DZ;AACF;AoB9zDM;EACI,0BAAA;ApBg0DV;;AoB3zDA;EACE,yBlBvCS;AFq2DX;;AoB3zDA;EACE,iCAAA;ApB8zDF;;AoB1zDA;EACE,yBAAA;ApB6zDF;;AoB1zDA;EACE,mBAAA;ApB6zDF;;AqBr8DA;EACE,yBnBkFS;AFs3DX;AqBv8DE;EACE,uBAAA;EACE,qBAAA;EACA,qBAAA;EACA,gBAAA;EACA,cnBkFK;AFu3DX;AqBv8DM;EACE,yBnB0EG;AF+3DX;AqBt8DM;EACE,yBnBuGE;EmBtGF,gBAAA;EACA,eAAA;ArBw8DR;;AqBn8DA;EACE,+BAAA;ArBs8DF;;AqBn8DA;EACE,yBnB04BsC;EmBz4BtC,gCAAA;EACA,cnB0DS;AF44DX;AqBr8DE;EACE,aAAA;ArBu8DJ;;AqBh8DE;EACE,cAAA;ArBm8DJ;AqB/7DI;EACE,qCAAA;EACA,wCAAA;EACA,sCAAA;EACA,yCAAA;ArBi8DN;AqB77DE;EACE,yBAAA;EACA,cAAA;EACA,yBAAA;EACA,4BAAA;EACA,gCnB6iB0B;EmB5iB1B,mCnB4iB0B;AFm5C9B;;AsBv/DI;EACI,aAAA;AtB0/DR;AsBv/DI;EACI,cpB6GE;EoB5GF,gBpBsDa;AFm8DrB;AsBt/DI;EACI,sBpBuEG;EoBtEH,cpBuGE;EoBtGF,wBAAA;UAAA,gBAAA;AtBw/DR;;AuBpgEA;EACE,gBAAA;AvBugEF;;AuBpgEA;EACE,yBrB+ES;AFw7DX;;AuBpgEA;EACE,yBrB+yCkC;AFwtBpC;;AuBpgEA;EACE,yBAAA;AvBugEF;;AuBpgEA;EACE,yBrBuES;AFg8DX;;AuBngEE;EACE,yBAAA;AvBsgEJ;;AuBlgEA;EACE,UAAA;AvBqgEF;;AuB//DE;EACE,iBAAA;AvBkgEJ;AuBjgEI;EACE,0BAAA;AvBmgEN;AuBhgEE;EACE,yBAAA;AvBkgEJ;AuBhgEE;EACE,6BAAA;AvBkgEJ;;AuB3/DE;EACE,mBAAA;AvB8/DJ;AuB5/DE;EACE,yBAAA;AvB8/DJ;;AuBv/DQ;EACE,yBrByFK;AFi6Df;AuBv/DQ;EACE,yBAAA;EACA,qBrBoFK;AFq6Df;AuBx/DU;EACE,yBrBkFG;AFw6Df;AuBr/DU;EACE,yBrB4EG;AF26Df;AuBl/DU;EACE,qBAAA;AvBo/DZ;;AuBxgEQ;EACE,yBrByFK;AFk7Df;AuBxgEQ;EACE,yBAAA;EACA,qBrBoFK;AFs7Df;AuBzgEU;EACE,yBrBkFG;AFy7Df;AuBtgEU;EACE,yBrB4EG;AF47Df;AuBngEU;EACE,qBAAA;AvBqgEZ;;AuBzhEQ;EACE,yBrByFK;AFm8Df;AuBzhEQ;EACE,yBAAA;EACA,qBrBoFK;AFu8Df;AuB1hEU;EACE,yBrBkFG;AF08Df;AuBvhEU;EACE,yBrB4EG;AF68Df;AuBphEU;EACE,qBAAA;AvBshEZ;;AuB1iEQ;EACE,yBrByFK;AFo9Df;AuB1iEQ;EACE,yBAAA;EACA,qBrBoFK;AFw9Df;AuB3iEU;EACE,yBrBkFG;AF29Df;AuBxiEU;EACE,yBrB4EG;AF89Df;AuBriEU;EACE,qBAAA;AvBuiEZ;;AuB3jEQ;EACE,yBrByFK;AFq+Df;AuB3jEQ;EACE,yBAAA;EACA,qBrBoFK;AFy+Df;AuB5jEU;EACE,yBrBkFG;AF4+Df;AuBzjEU;EACE,yBrB4EG;AF++Df;AuBtjEU;EACE,qBAAA;AvBwjEZ;;AuB5kEQ;EACE,yBrByFK;AFs/Df;AuB5kEQ;EACE,yBAAA;EACA,qBrBoFK;AF0/Df;AuB7kEU;EACE,yBrBkFG;AF6/Df;AuB1kEU;EACE,yBrB4EG;AFggEf;AuBvkEU;EACE,qBAAA;AvBykEZ;;AuB7lEQ;EACE,yBrByFK;AFugEf;AuB7lEQ;EACE,yBAAA;EACA,qBrBoFK;AF2gEf;AuB9lEU;EACE,yBrBkFG;AF8gEf;AuB3lEU;EACE,yBrB4EG;AFihEf;AuBxlEU;EACE,qBAAA;AvB0lEZ;;AuB9mEQ;EACE,yBrByFK;AFwhEf;AuB9mEQ;EACE,yBAAA;EACA,qBrBoFK;AF4hEf;AuB/mEU;EACE,yBrBkFG;AF+hEf;AuB5mEU;EACE,yBrB4EG;AFkiEf;AuBzmEU;EACE,qBAAA;AvB2mEZ;;AuB/nEQ;EACE,yBrByFK;AFyiEf;AuB/nEQ;EACE,uBAAA;EACA,qBrBoFK;AF6iEf;AuBhoEU;EACE,yBrBkFG;AFgjEf;AuB7nEU;EACE,yBrB4EG;AFmjEf;AuB1nEU;EACE,mBAAA;AvB4nEZ;;AuBnnEI;EACE,6BAAA;EACA,6BAAA;EACA,2BrBLK;EqBML,oCAAA;EACA,+BAAA;AvBsnEN;AuBrnEM;EACE,cAAA;EACA,YAAA;EACA,kBAAA;EACA,YAAA;EACA,WAAA;EACA,mBrBdG;EqBeH,YAAA;EACA,aAAA;EACA,oBAAA;AvBunER;;AuBhnEE;EACE,6BAAA;EACA,6BAAA;EACA,2BrB3BO;EqB4BP,gCAAA;AvBmnEJ;AuBlnEI;EACE,cAAA;EACA,YAAA;EACA,kBAAA;EACA,YAAA;EACA,WAAA;EACA,mBrBnCK;EqBoCL,WAAA;EACA,YAAA;EACA,mBAAA;AvBonEN;AuBlnEE;EACE,cAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,SAAA;EACA,UAAA;EACA,8BAAA;AvBonEJ;;AwB5vEA;EACI,sCtBspB0B;AFymD9B;;AwB1vEI;;;;EAII,8BAAA;EACA,eAAA;AxB6vER;AwBvvEQ;;;EACI,aAAA;AxB2vEZ;AwBvvEI;EACI,mBtB8DG;EsB7DH,qBtB6DG;AF4rEX;AwBtvEI;EACI,eAAA;EACA,ctB0DG;AF8rEX;AwBrvEI;;EAEI,ctBqDG;EsBpDH,mBtBkDG;EsBjDH,eAAA;AxBuvER;AwBpvEI;EACI,yBAAA;EACA,WAAA;EACA,YAAA;EACA,SAAA;EACA,oCAAA;AxBsvER;;AyBhyEE;EACE,eAAA;EACA,gBvByDiB;AF0uErB;;AyB/xEA;EACE,eAAA;AzBkyEF;;AyB9xEE;EACE,qBvB0GM;EuBzGN,cvByGM;AFwrEV;AyB9xEI;EACE,yBvBmGI;AF6rEV;AyB7xEI;EACE,sCAAA;AzB+xEN;AyB5xEE;EACE,qBvB0FM;EuBzFN,cvByFM;AFqsEV;;AyBzxEE;EACE,wBAAA;UAAA,gBAAA;AzB4xEJ;;AyBvxEE;EACE,mBvByEM;AFitEV;AyBzxEI;EACE,mBvBuEI;AFotEV;AyB1xEM;EACE,mCAAA;AzB4xER;AyBvxEE;EACE,mBvB+DM;AF0tEV;;AyBrxEA;EACE,qDAAA;AzBwxEF;;A0B90EA;EACE,qBxBuzCkC;AF0hCpC;;A0B90EA;EACE,eAAA;A1Bi1EF;;A0B90EA;EACE,QAAA;A1Bi1EF;;A0B70EE;EACE,qBAAA;EACA,sBAAA;A1Bg1EJ;A0B90EI;EACE,gBAAA;A1Bg1EN;;A2Bj2EA;;eAAA;AAII;EACI,iDzBsmBsB;UyBtmBtB,yCzBsmBsB;EyBrmBtB,UAAA;A3Bm2ER;A2Bl2EQ;EACI,iDzBmmBkB;UyBnmBlB,yCzBmmBkB;EyBlmBlB,YAAA;A3Bo2EZ;A2B/1EQ;EACE,cAAA;EACA,gBAAA;A3Bi2EV;;A2Bz1EI;EACI,oCAAA;EACA,oDAAA;A3B41ER;;A2B91EI;EACI,oCAAA;EACA,qDAAA;A3Bi2ER;;A2Bn2EI;EACI,oCAAA;EACA,qDAAA;A3Bs2ER;;A2Bx2EI;EACI,oCAAA;EACA,mDAAA;A3B22ER;;A2B72EI;EACI,oCAAA;EACA,oDAAA;A3Bg3ER;;A2Bl3EI;EACI,oCAAA;EACA,mDAAA;A3Bq3ER;;A2Bv3EI;EACI,oCAAA;EACA,oDAAA;A3B03ER;;A2B53EI;EACI,oCAAA;EACA,kDAAA;A3B+3ER;;A2Bj4EI;EACI,oCAAA;EACA,qDAAA;A3Bo4ER;;A2B73EA;EACI,wCAAA;EACA,yBAAA;A3Bg4EJ;;A2B73EA;EACI,aAAA;EACA,yBAAA;EACA,gBAAA;EACA,yBAAA;A3Bg4EJ;;A4B36EA;EACE,c1BiHQ;AF6zEV;;A4B36EA;EACE,qB1B6GQ;AFi0EV;;A4B36EA;EACE,aAAA;EACA,SAAA;EACA,UAAA;A5B86EF;A4B76EE;EACE,cAAA;A5B+6EJ;A4B76EE;EACE,eAAA;EACA,gBAAA;EACA,c1BgGM;E0B/FN,eAAA;A5B+6EJ;;A6Bl8EA;EACE,cAAA;A7Bq8EF;A6Bp8EE;EACE,yB3Bi6BoC;E2Bh6BpC,yBAAA;EACA,YAAA;A7Bs8EJ;A6Br8EI;EACE,aAAA;A7Bu8EN;A6Bp8EI;EACE,iBAAA;EACA,kBAAA;EACA,c3ByEK;AF63EX;A6Bn8EI;EACE,YAAA;EACA,WAAA;EACA,UAAA;A7Bq8EN;A6Bn8EM;EACE,yDAAA;EACA,2BAAA;A7Bq8ER;A6Bj8EI;EACE,c3ByDK;AF04EX;;A6Bz7EM;EACE,oEAAA;EACA,sCAAA;A7B47ER;;A6Br7EE;EACI,aAAA;EACA,yB3BkCK;AFs5EX;A6Bv7EM;EACI,yBAAA;EACA,yB3Bg3B8B;E2B/2B9B,c3BkCC;E2BjCD,aAAA;A7By7EV;A6Bt7EE;EACI,yB3BsDI;AFk4EV;A6Bt7EE;EACI,yB3BuBK;E2BtBL,c3BgqC8B;AFwxCpC;A6Bv7EM;EACI,yB3BgDA;E2B/CA,W3BgBC;AFy6EX;;A6Bp7EA;EACE,iBAAA;A7Bu7EF;;A6Bp7EA;EACE,yBAAA;EACA,yB3BOS;E2BNT,iD3B+hB4B;U2B/hB5B,yC3B+hB4B;AFw5D9B;;A6Bn7EE;EACE,yBAAA;A7Bs7EJ;;A6Bj7EE;EACE,gBAAA;EACA,yB3B20BoC;E2B10BpC,oCAAA;A7Bo7EJ;A6Bl7EI;EACE,iBAAA;A7Bo7EN;A6Bl7EI;EACE,SAAA;EACA,c3BXK;AF+7EX;A6Bn7EM;EACI,c3BbC;AFk8EX;A6Bt7EM;EACI,c3BbC;AFk8EX;A6Bt7EM;EACI,c3BbC;AFk8EX;A6Bt7EM;EACI,c3BbC;AFk8EX;A6Bt7EM;EACI,c3BbC;AFk8EX;A6Bl7EI;EACE,yB3BpBK;E2BqBL,yBAAA;EACA,kBAAA;EACA,cAAA;A7Bo7EN;;A6B76EI;EACE,qB3B7BK;AF68EX;A6B56EE;EACE,gB3B1DmB;AFw+EvB;;A6Bx6EA;EACI,WAAA;EACA,WAAA;EACA,kBAAA;A7B26EJ;A6B16EE;EACE,WAAA;EACA,YAAA;EACA,kBAAA;A7B46EJ;;A6Bx6EA;EACE,eAAA;A7B26EF;;A6Bx6EA;;;EAGE,qBAAA;EACA,eAAA;EACA,iBAAA;EACA,c3B5DS;AFu+EX;A6Bz6EE;;;EACE,iBAAA;A7B66EJ;A6B16EM;;;EACE,gBAAA;EACA,kCAAA;A7B86ER;;A6Bv6EE;;;EAGA,+BAAA;A7B06EF;;A6Bt6EA;EACE,gBAAA;A7By6EF;;A6Bn6EA;EACE,iBAAA;EACA,YAAA;EACA,WAAA;A7Bs6EF;;A8BzlFA,eAAA;AACA;EACE,aAAA;A9B4lFF;A8B3lFE;EACE,cAAA;EACA,cAAA;EACA,WAAA;EACA,YAAA;EACA,yB5B8EO;E4B7EP,sBAAA;EACA,mBAAA;EACA,mBAAA;EACA,eAAA;EACA,qBAAA;EACA,kBAAA;EACA,kBAAA;EACA,gB5B6CiB;E4B5CjB,wCAAA;EAAA,gCAAA;A9B6lFJ;A8B5lFI;EACE,c5BuEK;E4BtEL,6BAAA;EACA,cAAA;EACA,oBAAA;EACA,gBAAA;EACA,eAAA;EACA,iBAAA;EACA,kBAAA;EACA,UAAA;EACA,WAAA;EACA,SAAA;EACA,kBAAA;EACA,qBAAA;EACA,gBAAA;EACA,wCAAA;EAAA,gCAAA;A9B8lFN;A8B3lFI;EACE,WAAA;EACA,kBAAA;EACA,SAAA;EACA,yB5B4CK;E4B3CL,wBAAA;UAAA,gBAAA;EACA,mBAAA;EACA,YAAA;EACA,WAAA;EACA,QAAA;EACA,wCAAA;EAAA,gCAAA;A9B6lFN;A8BzlFE;EACE,yB5B8DM;AF6hFV;;A8BvlFA;EACE,yB5ByDQ;AFiiFV;A8BzlFE;EACE,W5BwBO;E4BvBP,4BAAA;EACA,WAAA;EACA,SAAA;A9B2lFJ;A8BxlFE;EACE,UAAA;EACA,yB5BkBO;AFwkFX;;A8BtlFA;EACE,yB5B4CQ;AF6iFV;;A8BvlFA;;EAEE,W5BOS;AFmlFX;;A8BvlFA;EACE,yB5BuCQ;AFmjFV;;A8BvlFA;EACE,yBAAA;A9B0lFF;;A8BvlFA;EACE,yB5B0BQ;AFgkFV;;A8BvlFA;EACE,yB5B2BQ;AF+jFV;;A8BvlFA;EACE,yB5ByBQ;AFikFV;;A8BvlFA;EACE,yB5BkBQ;AFwkFV;;A8BvlFA;EACE,yB5BYQ;AF8kFV;;A8BvlFA;EACE,yB5BjBS;AF2mFX;;A8BvlFA;EACE,iBAAA;A9B0lFF;A8BzlFE;EACE,kBAAA;A9B2lFJ;;A+B3sFA;EACE,yBAAA;EACA,YAAA;EACA,uBAAA;A/B8sFF;A+B3sFM;EACE,gBAAA;A/B6sFR;A+B1sFQ;EAGE,oCAAA;EACA,sBAAA;EACA,wBAAA;UAAA,gBAAA;EACA,sBAAA;A/B0sFV;A+BvsFQ;;;EAII,mB7B4DD;AF4oFX;A+BrsFQ;;;EAII,c7BwDD;E6BvDC,YAAA;A/BssFZ;A+BnsFQ;EACI,yB7BiDD;AFopFX;;A+B7rFE;EACE,YAAA;A/BgsFJ;;AgCxuFQ;EACA,0BAAA;EACA,6BAAA;AhC2uFR;AgCpuFQ;EACE,yBAAA;EACA,4BAAA;AhCsuFV;;AiCrvFE;EACE,UAAA;AjCwvFJ;;AiCnvFE;EACE,iBAAA;AjCsvFJ;AiCpvFI;EAHF;IAII,kBAAA;EjCuvFJ;AACF;AiCpvFI;EACE,kBAAA;EACA,eAAA;AjCsvFN;;AiC/uFI;EACE,aAAA;AjCkvFN;;AiC5uFE;EACE,eAAA;AjC+uFJ;AiC5uFE;EACE,gBAAA;AjC8uFJ;;AiCzuFA;EACE,oCAAA;EACA,8BAAA;AjC4uFF;AiCluFM;;;;;EACE,UAAA;EACA,aAAA;EACA,iBAAA;EACA,oCAAA;EACA,eAAA;EACA,QAAA;AjCwuFR;AiCnuFM;;;;;EACE,UAAA;EACA,YAAA;EACA,iBAAA;EACA,oCAAA;EACA,SAAA;EACA,eAAA;AjCyuFR;AiChuFQ;;;;EAGE,kBAAA;EACA,mBAAA;AjCmuFV;AiC1tFI;EACI,yCAAA;AjC4tFR;AiC1tFQ;EACI,qCAAA;EACA,c/BgBF;AF4sFV;AiCxtFQ;EACI,wBAAA;AjC0tFZ;AiCttFI;EACI,qCAAA;EACA,oBAAA;EACA,0CAAA;AjCwtFR;;AiCntFA;EACE,gB/BrDqB;AF2wFvB;;AiC1sFU;;EACE,oD/B8ekB;U+B9elB,4C/B8ekB;E+B7elB,yB/BXF;E+BYE,YAAA;AjC8sFZ;AiCnsFU;;EACE,yB/B3BF;AFiuFV;;AiC5rFA;EACE,yB/BxCQ;E+ByCR,YAAA;EACA,W/BzES;E+B0ET,wBAAA;UAAA,gBAAA;EACA,kBAAA;EACA,kBAAA;EACA,WAAA;AjC+rFF;AiC7rFE;EACE,mBAAA;EACA,0CAAA;EACA,W/BlFO;AFixFX;;AM3xFI;E2BkGF;;IAEE,qBAAA;IACA,iBAAA;EjC6rFF;;EiC1rFA;IACE,aAAA;EjC6rFF;;EiCzrFE;IACE,kBAAA;IACA,cAAA;IACA,2BAAA;EjC4rFJ;;EiCxrFA;IACE,qBAAA;IACA,mB/BwQK;EFm7EP;AACF;AiCtrFE;EACE,yB/BlHO;AF0yFX;;AiC/qFA;EACE,yBAAA;AjCkrFF;;AiC1qFI;;EACE,kBAAA;AjC8qFN;AiC5qFM;;EACE,kBAAA;AjC+qFR;AiC7qFQ;;EACE,QAAA;EACA,SAAA;EACA,YAAA;EACA,WAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,W/BnJC;E+BoJD,sBAAA;EACA,mBAAA;EACA,+BAAA;UAAA,uBAAA;EACA,kBAAA;EACA,yBAAA;EACA,iBAAA;EACA,YAAA;EACA,yB/B5HA;AF4yFV;;AkC35FA;EACI,oCAAA;AlC85FJ;;AkC15FI;EACI,wCAAA;AlC65FR;AkC15FI;;;EAGI,oCAAA;EACA,2BAAA;AlC45FR;AkCz5FI;EACI,yBAAA;AlC25FR;AkCz5FQ;EACI,oCAAA;AlC25FZ;AkCt5FQ;EACI,oCAAA;AlCw5FZ;AkCp5FI;EACI,gCAAA;AlCs5FR;AkCn5FI;;;EAGI,8BAAA;AlCq5FR;AkCl5FI;EACI,yBAAA;AlCo5FR;AkCl5FQ;EACI,wBAAA;AlCo5FZ;AkCh5FI;EACI,oCAAA;AlCk5FR;AkC/4FI;;;EAGI,yBAAA;AlCi5FR;AkC94FI;EACI,0CAAA;AlCg5FR;;AkC74FA;EACI,wBAAA;AlCg5FJ;;AmCh9FA,aAAA;AACA;EACE,iBAAA;EACA,0BAAA;EACA,mBjCozCkC;EiCnzClC,kBAAA;AnCm9FF;AmCj9FE;EACE,eAAA;EACA,WAAA;AnCm9FJ;;AoCz9FI;EACI,kBAAA;ApC49FR;AoC19FQ;EACI,WAAA;EACA,WAAA;EACA,WAAA;EACA,yBlC0ED;EkCzEC,kBAAA;EACA,OAAA;EACA,SAAA;ApC49FZ;AoCz9FQ;EACI,qBAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,yBAAA;EACA,clC0FF;EkCzFE,kBAAA;EACA,kBAAA;EACA,kBAAA;EACA,yBlC8xCwB;AF6rDpC;AoCz9FY;EAZJ;IAaQ,cAAA;IACA,6BAAA;EpC49Fd;AACF;AoCx9FY;EACI,cAAA;EACA,eAAA;EACA,gBlC8nBc;AF41E9B;AoCx9FgB;EALJ;IAMQ,aAAA;EpC29FlB;AACF;AoCx9FY;EACI,6BAAA;EACA,clCyCL;AFi7FX;AoCx9FgB;EACI,yBlC8DV;EkC7DU,WlC8BT;AF47FX;AoCp9FI;EACI,iBAAA;EACA,eAAA;EACA,gBAAA;EACA,gBAAA;ApCs9FR;AoCp9FQ;EACI,qBAAA;ApCs9FZ;AoCp9FY;EACI,qBAAA;EACA,wBAAA;EACA,yBlC2CN;EkC1CM,WlCWL;EkCVK,sBAAA;ApCs9FhB;AoCl9FgB;EACI,mBAAA;EACA,yBAAA;ApCo9FpB;AoCh9FY;EACI,YAAA;ApCk9FhB;;AoC58FA;EACI,iBAAA;EACA,iBAAA;ApC+8FJ;;AqCxiGE;EACE,cAAA;ArC2iGJ;AqCziGE;EACE,uBAAA;ArC2iGJ;AqCxiGI;EACE,yBnC2EK;EmC1EL,cnC+EK;EmC9EL,yBAAA;ArC0iGN;AqCziGM;EACI,yBnCmGA;EmClGA,qBnCkGA;EmCjGA,WnCkEC;AFy+FX;AqCxiGI;EACE,YAAA;ArC0iGN;AqCziGM;EACE,QAAA;EACA,kCAAA;UAAA,0BAAA;EACA,oBAAA;ArC2iGR;AqCtiGI;EACE,eAAA;EACA,mBAAA;ArCwiGN;AqCpiGE;EACE,kBAAA;EACA,yBAAA;ArCsiGJ;AqCpiGI;EACE,oCAAA;ArCsiGN;AqCniGI;EACE,qBAAA;EACA,iBAAA;EACA,kBAAA;ArCqiGN;AqCpiGM;EACE,+BAAA;EACA,oCAAA;EACA,sBnCgCG;EmC/BH,kBAAA;EACA,yBAAA;EACA,WAAA;EACA,qBAAA;EACA,YAAA;EACA,OAAA;EACA,kBAAA;EACA,kBAAA;EACA,4BAAA;EACA,WAAA;EACA,wBAAA;ArCsiGR;AqCpiGM;EACE,cnCoBG;EmCnBH,qBAAA;EACA,eAAA;EACA,YAAA;EACA,OAAA;EACA,kBAAA;EACA,iBAAA;EACA,gBAAA;EACA,kBAAA;EACA,SAAA;EACA,WAAA;ArCsiGR;AqCniGI;EACE,eAAA;EACA,UAAA;EACA,UAAA;EACA,wBAAA;ArCqiGN;AqCniGM;EACE,aAAA;ArCqiGR;AqCjiGM;EACE,oBAAA;EACA,aAAA;ArCmiGR;AqC/hGM;EACE,gBAAA;EACA,kCAAA;EACA,gBAAA;ArCiiGR;AqC7hGM;EACE,yBnCnBG;EmCoBH,mBAAA;ArC+hGR;AqC3hGM;EACE,yBnCKE;EmCJF,qBnCIE;AFyhGV;AqC3hGM;EACE,WnC9BG;AF2jGX;AqCvhGI;EACE,oBAAA;EACA,yBnCPI;AFgiGV;AqCxhGM;EACE,WnCxCG;AFkkGX;;AqCnhGE;EAEI;IACE,qBAAA;ErCqhGN;AACF;;AsCxpGE;EACE,kCpC+7BoC;EoC97BpC,uBAAA;EACA,yBAAA;EACA,yBpC+5BoC;EoC95BpC,cpCgFO;EoC/EP,sBpC6lB0B;AF8jF9B;AsC1pGI;EACE,aAAA;EACA,qBpCw6BkC;AFovExC;;AuCvqGA;EACI,2BAAA;AvC0qGJ;AuCzqGI;EACI,iDAAA;EACA,arCmFG;AFwlGX;AuCzqGI;EACI,cAAA;AvC2qGR;;AuCvqGA;;EAEI,iDAAA;AvC0qGJ;;AuCvqGA;EACI,gBrC4CiB;AF8nGrB;;AuCvqGA;EACI,oBAAA;EACA,erC2Cc;AF+nGlB;;AuCvqGA;EACI,yBAAA;EACA,iDAAA;EACA,0BAAA;AvC0qGJ;;AuCvqGA;EACI,qBAAA;AvC0qGJ;;AuCrqGI;;EACI,iDAAA;EACA,arCgDG;AFynGX;;AwCltGA,eAAA;AACA;EACE,aAAA;AxCqtGF;;AwCltGA;EACE,iBAAA;EACA,0CAAA;EACA,YAAA;EACA,ctC4ES;EsC3ET,iDtCqmB4B;UsCrmB5B,yCtCqmB4B;EsCpmB5B,kBAAA;AxCqtGF;;AwCltGA;EACE,ctC0ES;AF2oGX;;AyCluGA;EACE,+BAAA;UAAA,uBAAA;EACA,sBAAA;EACA,uBAAA;EACA,oCAAA;EACA,oDvCymB4B;UuCzmB5B,4CvCymB4B;EuCxmB5B,4BAAA;EACA,kBAAA;EACA,gCAAA;AzCquGF;;AyCluGA;EACE,yBAAA;EACA,0BAAA;EACA,4BAAA;EACA,iDAAA;EACA,2BAAA;AzCquGF;;A0CpvGA;EACE,aAAA;EACA,mBxCgFS;EwC/ET,kBAAA;A1CuvGF;;A0CpvGA;EACE,cAAA;EACA,kBAAA;EACA,WxCwES;EwCvET,eAAA;EACA,iBAAA;EACA,mBxCoGQ;EwCnGR,kBAAA;EACA,kBAAA;A1CuvGF;;A0CpvGA;EACE,SAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,kBAAA;A1CuvGF;A0CtvGE;EACE,aAAA;EACA,mCAAA;EACA,oCAAA;EACA,8BAAA;A1CwvGJ;A0CtvGE;EACE,UAAA;EACA,mCAAA;EACA,oCAAA;EACA,iCAAA;A1CwvGJ;;A2C1xGA;EACI,YAAA;EACA,mBzCwFO;EyCvFP,czCgFO;EyC/EP,sCzCmpB0B;EyClpB1B,iBzCwpB0B;EyCvpB1B,gBAAA;A3C6xGJ;;A4ClyGI;EACE,qBAAA;A5CqyGN;;A4CjyGE;EACE,gBAAA;A5CoyGJ;A4CnyGI;EACE,gBAAA;A5CqyGN;;A6ClzGA;;oBAAA;AAGA;EACE,YAAA;EACA,WAAA;EACA,aAAA;EACA,kBAAA;A7CqzGF;;A6ClzGA;EACE,kBAAA;A7CqzGF;;A6CjzGE;EACE,c3C8EO;E2C7EP,gBAAA;A7CozGJ;A6ClzGE;EACE,eAAA;A7CozGJ;;A6ChzGA;EACE;IACE,WAAA;IACA,WAAA;E7CmzGF;;E6CjzGA;IACE,SAAA;E7CozGF;AACF;A6C/yGE;EACE,cAAA;EACA,c3CsDO;E2CrDP,iBAAA;EACA,gBAAA;A7CizGJ;A6ChzGI;EACE,c3C6EI;E2C5EJ,gBAAA;A7CkzGN;;A6C7yGA;EACE,cAAA;EACA,eAAA;A7CgzGF;A6C9yGE;EACE,kBAAA;EACA,cAAA;EACA,YAAA;EACA,iBAAA;EACA,eAAA;EACA,iCAAA;UAAA,yBAAA;A7CgzGJ;A6C9yGI;EACE,c3C+BK;AFixGX;A6C7yGI;EACE,mB3CwBK;E2CvBL,kCAAA;UAAA,0BAAA;A7C+yGN;A6C5yGI;EACE,WAAA;EACA,kBAAA;A7C8yGN;A6C3yGI;EACE,YAAA;A7C6yGN;A6C3yGM;;;EAGE,cAAA;EACA,WAAA;A7C6yGR;A6C1yGM;EACE,6BAAA;EACA,oBAAA;EACA,mBAAA;EACA,SAAA;EACA,QAAA;EACA,cAAA;EACA,YAAA;A7C4yGR;A6CzyGM;EACE,wBAAA;A7C2yGR;A6CxyGM;EACE,gBAAA;EACA,gBAAA;A7C0yGR;A6CvyGM;EACE,kBAAA;EACA,MAAA;EACA,WAAA;EACA,QAAA;EACA,uBAAA;EACA,gBAAA;EACA,mBAAA;EACA,gBAAA;A7CyyGR;A6CryGI;EACE,kBAAA;EACA,MAAA;EACA,WAAA;EACA,QAAA;EACA,SAAA;A7CuyGN;A6CryGM;;EAEE,kBAAA;EACA,MAAA;A7CuyGR;A6CpyGM;EACE,OAAA;EACA,YAAA;EACA,uBAAA;EACA,gBAAA;EACA,mBAAA;A7CsyGR;A6CnyGM;EACE,QAAA;EACA,YAAA;EACA,kBAAA;A7CqyGR;A6CjyGI;EAEE,yCAAA;UAAA,iCAAA;A7CkyGN;A6C9xGE;EACE,yB3C7DO;E2C8DP,gBAAA;EACA,cAAA;A7CgyGJ;A6C/xGM;EACE,cAAA;EACA,gBAAA;A7CiyGR;A6C3xGE;EACE,eAAA;EACA,YAAA;EACA,WAAA;EACA,kBAAA;EACA,qBAAA;EACA,2CAAA;UAAA,mCAAA;EACA,kBAAA;A7C6xGJ;A6C3xGI;EACE,UAAA;EACA,eAAA;A7C6xGN;A6C3xGI;EACE,UAAA;A7C6xGN;A6C1xGI;EACE,kBAAA;EACA,YAAA;EACA,WAAA;EACA,OAAA;EACA,eAAA;EACA,UAAA;EACA,gBAAA;EACA,kCAAA;UAAA,0BAAA;EACA,MAAA;A7C4xGN;A6C3xGM;EACE,iBAAA;EACA,oCAAA;EACA,MAAA;EACA,YAAA;EACA,cAAA;EACA,WAAA;EACA,kBAAA;EACA,iBAAA;EACA,SAAA;EACA,eAAA;A7C6xGR;;A6CvxGA;EACE;IACI,YAAA;E7C0xGJ;AACF;A8Cz+GA;;oBAAA;AAIA;EACI,UAAA;EACA,iBAAA;EACA,cAAA;A9C0+GJ;;A8Cx+GE;EACE,WAAA;EACA,cAAA;EACA,WAAA;A9C2+GJ;;A8Cz+GE;EACE,kBAAA;EACA,eAAA;EACA,cAAA;EACA,kBAAA;A9C4+GJ;A8C3+GI;EACE,8BAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,kBAAA;EACA,MAAA;EACA,UAAA;A9C6+GN;;A8C1+GE;EACE;IACE,kBAAA;IACA,eAAA;E9C6+GJ;E8C5+GI;IACE,SAAA;IACA,iBAAA;E9C8+GN;AACF;A8C1+GE;EACE,aAAA;EACA,kBAAA;A9C4+GJ;A8C3+GI;EACE,WAAA;EACA,WAAA;EACA,cAAA;A9C6+GN;;A8C1+GE;EACE,aAAA;A9C6+GJ;;A8C3+GE;EACE,gBAAA;A9C8+GJ;;A8C5+GE;EACE;IACE,aAAA;E9C++GJ;;E8C7+GE;IACE,aAAA;E9Cg/GJ;;E8C9+GE;IACE,gBAAA;E9Ci/GJ;AACF;A8C/+GE;EACE,kBAAA;EACA,SAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,kBAAA;EACA,iBAAA;EACA,eAAA;EACA,W5CUO;E4CTP,yB5CwCM;E4CvCN,sBAAA;A9Ci/GJ;A8C/+GI;EACE,gBAAA;A9Ci/GN;;A8C9+GE;EACE;IACE,WAAA;IACA,YAAA;IACA,iBAAA;IACA,SAAA;IACA,kBAAA;E9Ci/GJ;;E8C/+GE;IACE,kBAAA;E9Ck/GJ;;E8Ch/GE;IACE,mBAAA;IACA,mCAAA;IAEA,2BAAA;E9Cm/GJ;AACF;A8Ch/GE;EACE,kBAAA;EACA,yBAAA;EACA,iBAAA;EACA,YAAA;EACA,kBAAA;A9Ck/GJ;A8Ch/GI;EACE,WAAA;EACA,WAAA;EACA,cAAA;A9Ck/GN;A8Ch/GI;EACE,aAAA;A9Ck/GN;A8Ch/GI;EACE,mB5CFI;E4CGJ,qBAAA;EACA,YAAA;EACA,qBAAA;EACA,YAAA;EACA,eAAA;EACA,kBAAA;A9Ck/GN;A8Ch/GI;EACE,qBAAA;EACA,eAAA;A9Ck/GN;A8Ch/GI;EACE,eAAA;EACA,kBAAA;A9Ck/GN;;A8C9+GE;EACE,yBAAA;A9Ci/GJ;;A8C/+GE;EACE,WAAA;EACA,gBAAA;EACA,YAAA;A9Ck/GJ;;A8Ch/GE;EACE,WAAA;EACA,kBAAA;EACA,SAAA;EACA,WAAA;EACA,SAAA;EACA,QAAA;EACA,8BAAA;EACA,gCAAA;A9Cm/GJ;;A8Cj/GE;EACE;IACE,cAAA;IACA,cAAA;IACA,UAAA;E9Co/GJ;;E8Cl/GE;IACE,SAAA;IACA,UAAA;IACA,yBAAA;IACA,0B5C5EK;EFikHT;;E8Cn/GE;IACE,WAAA;E9Cs/GJ;;E8Cp/GE;IACE,kBAAA;IACA,WAAA;IACA,UAAA;IACA,SAAA;E9Cu/GJ;;E8Cr/GE;IACE,YAAA;E9Cw/GJ;;E8Ct/GE;IACE,SAAA;IACA,UAAA;IACA,WAAA;IACA,yBAAA;IACA,2B5C/FK;EFwlHT;;E8Cv/GE;IACE,YAAA;E9C0/GJ;;E8Cx/GE;IACE,UAAA;IACA,WAAA;IACA,iBAAA;E9C2/GJ;;E8Cz/GE;IACE,kBAAA;E9C4/GJ;;E8C1/GE;IACE,mBAAA;IACA,mCAAA;IAEA,2BAAA;E9C6/GJ;AACF;A8C1/GE;EACE;IACE,2CAAA;IAEA,mCAAA;E9C4/GJ;AACF;A+CrsHM;EACE,mBAAA;EACA,kBAAA;EACA,YAAA;EACA,qBAAA;EACA,YAAA;EACA,iBAAA;EACA,kBAAA;EACA,WAAA;A/CusHR;;A+CjsHE;EACE,4CAAA;EACA,4BAAA;EACA,sBAAA;EACA,2BAAA;A/CosHJ;A+CnsHI;EACE,kCAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,SAAA;EACA,QAAA;A/CqsHN;;A+C/rHE;EACE,iBAAA;EACA,gBAAA;EACA,kBAAA;A/CksHJ;A+C5rHI;EACE,eAAA;EACA,iBAAA;A/C8rHN;A+C7rHM;EACE,c7CDO;AFgsHf;A+C7rHM;EACE,a7CHQ;AFksHhB;;A+CxrHI;EACE,eAAA;EACA,gBAAA;EACA,kBAAA;EACA,wGAAA;A/C2rHN", "file": "app-dark-rtl.min.css", "sourcesContent": ["/*\r\nTemplate Name: Upcube -  Admin & Dashboard Template\r\nAuthor: Themesdesign\r\nVersion: 2.0.0\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\r\n\r\n\r\n//Fonts\r\n@import \"custom/fonts/fonts\";\r\n\r\n//Core files\r\n@import \"./node_modules/bootstrap/scss/functions\";\r\n@import \"./node_modules/bootstrap/scss/variables\";\r\n@import \"variables-dark\";\r\n@import \"./node_modules/bootstrap/scss/mixins.scss\";\r\n\r\n// Structure\r\n@import \"custom/structure/general\";\r\n@import \"custom/structure/topbar\";\r\n@import \"custom/structure/page-head\";\r\n@import \"custom/structure/footer\";\r\n@import \"custom/structure/right-sidebar\";\r\n@import \"custom/structure/vertical\";\r\n@import \"custom/structure/horizontal-nav\";\r\n@import \"custom/structure/layouts\";\r\n\r\n// Components\r\n@import \"custom/components/waves\";\r\n@import \"custom/components/avatar\";\r\n@import \"custom/components/accordion\";\r\n@import \"custom/components/helper\";\r\n@import \"custom/components/preloader\";\r\n@import \"custom/components/forms\";\r\n@import \"custom/components/widgets\";\r\n@import \"custom/components/demos\";\r\n@import \"custom/components/print\";\r\n\r\n// Plugins\r\n@import \"custom/plugins/custom-scrollbar\";\r\n@import \"custom/plugins/calendar\";\r\n@import \"custom/plugins/color-picker\";\r\n@import \"custom/plugins/session-timeout\";\r\n@import \"custom/plugins/round-slider\";\r\n@import \"custom/plugins/range-slider\";\r\n@import \"custom/plugins/sweatalert2\";\r\n@import \"custom/plugins/rating\";\r\n@import \"custom/plugins/toastr\";\r\n@import \"custom/plugins/parsley\";\r\n@import \"custom/plugins/select2\";\r\n@import \"custom/plugins/switch\";\r\n@import \"custom/plugins/datepicker\";\r\n@import \"custom/plugins/bootstrap-touchspin\";\r\n@import \"custom/plugins/datatable\";\r\n@import \"custom/plugins/form-editors\";\r\n@import \"custom/plugins/form-upload\";\r\n@import \"custom/plugins/form-wizard\";\r\n@import \"custom/plugins/responsive-table\";\r\n@import \"custom/plugins/table-editable\";\r\n@import \"custom/plugins/apexcharts\";\r\n@import \"custom/plugins/flot\";\r\n@import \"custom/plugins/sparkline-chart\";\r\n@import \"custom/plugins/google-map\";\r\n@import \"custom/plugins/vector-maps\";\r\n@import \"custom/plugins/x-editable\";\r\n\r\n// Pages\r\n@import \"custom/pages/email\";\r\n@import \"custom/pages/timeline\";\r\n@import \"custom/pages/extras-pages\";\r\n", "//\r\n// Fonts\r\n//\r\n\r\n// Nunito - Google Font\r\n@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap');\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 300;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 400;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 500;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 700;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\r\n}", "/*\nTemplate Name: Upcube -  Admin & Dashboard Template\nAuthor: Themesdesign\nVersion: 2.0.0\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap\");\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 300;\n  font-display: swap;\n  src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\n}\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1002;\n  background-color: #272d3e;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px / 2) 0 0;\n}\n.navbar-header .dropdown .show.header-item {\n  background-color: #232838;\n}\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 240px;\n}\n\n.logo {\n  line-height: 70px;\n}\n.logo .logo-sm {\n  display: none;\n}\n\n.logo-dark {\n  display: none;\n}\n\n.logo-light {\n  display: block;\n}\n\n/* Search */\n.app-search {\n  padding: calc(32px / 2) 0;\n}\n.app-search .form-control {\n  border: none;\n  height: 38px;\n  padding-left: 40px;\n  padding-right: 20px;\n  background-color: #2b3244;\n  box-shadow: none;\n  border-radius: 30px;\n}\n.app-search span {\n  position: absolute;\n  z-index: 10;\n  font-size: 16px;\n  line-height: 38px;\n  left: 13px;\n  top: 0;\n  color: #919bae;\n}\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px;\n}\n.megamenu-list li a {\n  color: #79859c;\n}\n\n@media (max-width: 991px) {\n  .navbar-brand-box {\n    width: auto;\n  }\n\n  .logo span.logo-lg {\n    display: none;\n  }\n  .logo span.logo-sm {\n    display: inline-block;\n  }\n}\n.page-content {\n  padding: calc(70px + 24px) calc(24px / 2) 60px calc(24px / 2);\n}\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: #919bae;\n  border: 0;\n  border-radius: 0px;\n}\n.header-item:hover {\n  color: #919bae;\n}\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: #2d3448;\n  padding: 3px;\n}\n\n.user-dropdown .dropdown-item i {\n  display: inline-block;\n}\n\n.noti-icon i {\n  font-size: 22px;\n  color: #919bae;\n}\n.noti-icon .noti-dot {\n  position: absolute;\n  display: inline-block;\n  height: 6px;\n  width: 6px;\n  background-color: #f32f53;\n  border-radius: 50%;\n  top: 20px;\n  right: 14px;\n}\n\n.notification-item .d-flex {\n  padding: 0.75rem 1rem;\n}\n.notification-item .d-flex:hover {\n  background-color: #2d3448;\n}\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #919bae;\n}\n.dropdown-icon-item img {\n  height: 24px;\n}\n.dropdown-icon-item span {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.dropdown-icon-item:hover {\n  border-color: #2d3448;\n}\n\n.fullscreen-enable [data-toggle=fullscreen] .ri-fullscreen-line:before {\n  content: \"\\ed73\";\n}\n\nbody[data-topbar=dark] #page-topbar {\n  background-color: #252b3b;\n}\nbody[data-topbar=dark] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=dark] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=dark] .header-item {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .header-item:hover {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=dark] .noti-icon i {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .logo-dark {\n  display: none;\n}\nbody[data-topbar=dark] .logo-light {\n  display: block;\n}\nbody[data-topbar=dark] .app-search .form-control {\n  background-color: rgba(43, 50, 68, 0.07);\n  color: #fff;\n}\nbody[data-topbar=dark] .app-search span,\nbody[data-topbar=dark] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\nbody[data-sidebar=dark] .navbar-brand-box {\n  background: #252b3b;\n}\nbody[data-sidebar=dark] .logo-dark {\n  display: none;\n}\nbody[data-sidebar=dark] .logo-light {\n  display: block;\n}\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static;\n  }\n  .navbar-header .dropdown .dropdown-menu {\n    left: 10px !important;\n    right: 10px !important;\n  }\n}\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none;\n  }\n}\nbody[data-layout=horizontal] .navbar-brand-box {\n  width: auto;\n}\nbody[data-layout=horizontal] .page-content {\n  margin-top: 70px;\n  padding: calc(55px + 24px) calc(24px / 2) 60px calc(24px / 2);\n}\n\n@media (max-width: 992px) {\n  body[data-layout=horizontal] .page-content {\n    margin-top: 15px;\n  }\n}\nbody[data-topbar=colored] #page-topbar {\n  background-color: #0f9cf3;\n}\nbody[data-topbar=colored] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=colored] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=colored] .header-item {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .header-item:hover {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=colored] .noti-icon i {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .logo-dark {\n  display: none;\n}\nbody[data-topbar=colored] .logo-light {\n  display: block;\n}\nbody[data-topbar=colored] .app-search .form-control {\n  background-color: rgba(43, 50, 68, 0.07);\n  color: #fff;\n}\nbody[data-topbar=colored] .app-search span,\nbody[data-topbar=colored] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.page-title-box {\n  padding-bottom: 24px;\n}\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0;\n}\n.page-title-box h4 {\n  font-size: 15px;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px / 2);\n  position: absolute;\n  right: 0;\n  color: #919bae;\n  left: 240px;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  background-color: #212734;\n}\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0;\n  }\n}\n.vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-layout=horizontal] .footer {\n  left: 0 !important;\n}\n\n.right-bar {\n  background-color: #252b3b;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0;\n}\n.right-bar .right-bar-toggle {\n  background-color: white;\n  height: 24px;\n  width: 24px;\n  line-height: 24px;\n  color: #252b3b;\n  text-align: center;\n  border-radius: 50%;\n}\n.right-bar .right-bar-toggle:hover {\n  background-color: white;\n}\n\n.rightbar-overlay {\n  background-color: rgba(239, 242, 247, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all 0.2s ease-out;\n}\n\n.right-bar-enabled .right-bar {\n  right: 0;\n}\n.right-bar-enabled .rightbar-overlay {\n  display: block;\n}\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto;\n  }\n  .right-bar .slimscroll-menu {\n    height: auto !important;\n  }\n}\n.metismenu {\n  margin: 0;\n}\n.metismenu li {\n  display: block;\n  width: 100%;\n}\n.metismenu .mm-collapse {\n  display: none;\n}\n.metismenu .mm-collapse:not(.mm-show) {\n  display: none;\n}\n.metismenu .mm-collapse.mm-show {\n  display: block;\n}\n.metismenu .mm-collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: 0.35s;\n  transition-property: height, visibility;\n}\n\n.vertical-menu {\n  width: 240px;\n  z-index: 1001;\n  background: #252b3b;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.main-content {\n  margin-left: 240px;\n  overflow: hidden;\n}\n.main-content .content {\n  padding: 0 15px 10px 15px;\n  margin-top: 70px;\n}\n\n#sidebar-menu {\n  padding: 10px 0 30px 0;\n}\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(-180deg);\n}\n#sidebar-menu .has-arrow:after {\n  content: \"\\f0140\";\n  font-family: \"Material Design Icons\";\n  display: block;\n  float: right;\n  transition: transform 0.2s;\n  font-size: 1rem;\n}\n#sidebar-menu ul li a {\n  display: block;\n  padding: 0.625rem 1.5rem;\n  color: #8590a5;\n  position: relative;\n  font-size: 13.3px;\n  transition: all 0.4s;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 500;\n}\n#sidebar-menu ul li a i {\n  display: inline-block;\n  min-width: 1.5rem;\n  padding-bottom: 0.125em;\n  font-size: 1.1rem;\n  line-height: 1.40625rem;\n  vertical-align: middle;\n  color: #8590a5;\n  transition: all 0.4s;\n  opacity: 0.75;\n}\n#sidebar-menu ul li a:hover {\n  color: #d7e4ec;\n}\n#sidebar-menu ul li a:hover i {\n  color: #d7e4ec;\n}\n#sidebar-menu ul li .badge {\n  margin-top: 4px;\n}\n#sidebar-menu ul li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 3.2rem;\n  font-size: 13px;\n  color: #8590a5;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 4.2rem;\n  font-size: 13.5px;\n}\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: 0.05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #8590a5;\n  font-weight: 600;\n  font-family: \"Inter\", sans-serif;\n  opacity: 0.5;\n}\n\n.mm-active {\n  color: #d7e4ec !important;\n}\n.mm-active > a {\n  color: #d7e4ec !important;\n}\n.mm-active > a i {\n  color: #d7e4ec !important;\n}\n.mm-active > i {\n  color: #d7e4ec !important;\n}\n.mm-active .active {\n  color: #d7e4ec !important;\n}\n.mm-active .active i {\n  color: #d7e4ec !important;\n}\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none;\n  }\n\n  .main-content {\n    margin-left: 0 !important;\n  }\n\n  body.sidebar-enable .vertical-menu {\n    display: block;\n  }\n}\n.vertical-collpsed .main-content {\n  margin-left: 70px;\n}\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important;\n}\n.vertical-collpsed .logo span.logo-lg {\n  display: none;\n}\n.vertical-collpsed .logo span.logo-sm {\n  display: block;\n}\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5;\n}\n.vertical-collpsed .vertical-menu .simplebar-mask,\n.vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n  overflow: visible !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-scrollbar {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-offset {\n  bottom: 0 !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n.vertical-collpsed .vertical-menu #sidebar-menu .badge,\n.vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n  height: inherit !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n  display: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n  position: relative;\n  white-space: nowrap;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n  padding: 15px 20px;\n  min-height: 55px;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n  color: #d7e4ec;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  font-size: 20px;\n  margin-left: 4px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n  display: none;\n  padding-left: 25px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  position: relative;\n  width: calc(190px + 70px);\n  color: #0f9cf3;\n  background-color: #1d222e;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #0f9cf3;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n  display: inline;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n  display: block;\n  left: 70px;\n  position: absolute;\n  width: 190px;\n  height: auto !important;\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  box-shadow: none;\n  padding: 8px 20px;\n  position: relative;\n  width: 190px;\n  z-index: 6;\n  color: #8590a5;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #d7e4ec;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  padding: 5px 0;\n  z-index: 9999;\n  display: none;\n  background-color: #252b3b;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n  display: block;\n  left: 190px;\n  height: auto !important;\n  margin-top: -36px;\n  position: absolute;\n  width: 190px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n  position: absolute;\n  right: 20px;\n  top: 12px;\n  transform: rotate(270deg);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n  color: #212529;\n}\n\nbody[data-sidebar=dark] .user-profile h4 {\n  color: #fff;\n}\nbody[data-sidebar=dark] .user-profile span {\n  color: #79859c !important;\n}\nbody[data-sidebar=dark] .vertical-menu {\n  background: #252b3b;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a i {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed {\n  min-height: 1400px;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background: #293041;\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #d7e4ec;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  background-color: #272d3e;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=dark] .mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .menu-title {\n  color: #8590a5;\n}\n\nbody[data-layout=horizontal] .main-content {\n  margin-left: 0 !important;\n}\n\nbody[data-sidebar-size=small] .navbar-brand-box {\n  width: 160px;\n}\n@media (max-width: 992px) {\n  body[data-sidebar-size=small] .navbar-brand-box {\n    width: auto;\n  }\n}\nbody[data-sidebar-size=small] .vertical-menu {\n  width: 160px;\n  text-align: center;\n}\nbody[data-sidebar-size=small] .vertical-menu .has-arrow:after,\nbody[data-sidebar-size=small] .vertical-menu .badge {\n  display: none !important;\n}\nbody[data-sidebar-size=small] .main-content {\n  margin-left: 160px;\n}\nbody[data-sidebar-size=small] .footer {\n  left: 160px;\n}\n@media (max-width: 991px) {\n  body[data-sidebar-size=small] .footer {\n    left: 0;\n  }\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li.menu-title {\n  background-color: #293041;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li a i {\n  display: block;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small].vertical-collpsed .main-content {\n  margin-left: 70px;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  display: inline-block;\n}\nbody[data-sidebar-size=small].vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-sidebar=colored] .user-profile h4 {\n  color: #fff;\n}\nbody[data-sidebar=colored] .user-profile span {\n  color: #79859c !important;\n}\nbody[data-sidebar=colored] .vertical-menu {\n  background: #0f9cf3;\n}\nbody[data-sidebar=colored] .navbar-brand-box {\n  background-color: #0f9cf3;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-dark {\n  display: none;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-light {\n  display: block;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li.menu-title {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a i {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a.waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.1);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li ul.sub-menu li a {\n  color: rgba(255, 255, 255, 0.5);\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #19a0f3;\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=colored] .mm-active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .menu-title {\n  color: #fff !important;\n}\n\n.vertical-collpsed .user-profile {\n  display: none;\n}\n\n.topnav {\n  background: #282e3f;\n  padding: 0 calc(24px / 2);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100;\n}\n.topnav .topnav-menu {\n  margin: 0;\n  padding: 0;\n}\n.topnav .navbar-nav .nav-link {\n  font-size: 15px;\n  position: relative;\n  padding: 1rem 1.3rem;\n  color: #919bae;\n  font-family: \"Inter\", sans-serif;\n}\n.topnav .navbar-nav .nav-link i {\n  font-size: 15px;\n  vertical-align: middle;\n  display: inline-block;\n}\n.topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n  color: #0f9cf3;\n  background-color: transparent;\n}\n.topnav .navbar-nav .dropdown-item {\n  color: #919bae;\n}\n.topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n  color: #0f9cf3;\n}\n.topnav .navbar-nav .nav-item .nav-link.active {\n  color: #0f9cf3;\n}\n.topnav .navbar-nav .dropdown.active > a {\n  color: #0f9cf3;\n  background-color: transparent;\n}\n\n@media (min-width: 1200px) {\n  body[data-layout=horizontal] .container-fluid,\nbody[data-layout=horizontal] .navbar-header {\n    max-width: 85%;\n  }\n}\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0;\n  }\n  .topnav .dropdown-item {\n    padding: 0.5rem 1.5rem;\n    min-width: 180px;\n  }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto;\n  }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 0.25rem 0.25rem;\n  }\n  .topnav .dropdown .dropdown-menu .arrow-down::after {\n    right: 15px;\n    transform: rotate(-135deg) translateY(-50%);\n    position: absolute;\n  }\n  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n    position: absolute;\n    top: 0 !important;\n    left: 100%;\n    display: none;\n  }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n\n  .navbar-toggle {\n    display: none;\n  }\n}\n.arrow-down {\n  display: inline-block;\n}\n.arrow-down:after {\n  border-color: initial;\n  border-style: solid;\n  border-width: 0 0 1px 1px;\n  content: \"\";\n  height: 0.4em;\n  display: inline-block;\n  right: 5px;\n  top: 50%;\n  margin-left: 10px;\n  transform: rotate(-45deg) translateY(-50%);\n  transform-origin: top;\n  transition: all 0.3s ease-out;\n  width: 0.4em;\n}\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: none;\n  }\n  .navbar-brand-box .logo-dark span.logo-sm {\n    display: none;\n  }\n  .navbar-brand-box .logo-light {\n    display: block;\n  }\n\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0;\n  }\n  .topnav .navbar-nav .nav-link {\n    padding: 0.75rem 1.1rem;\n  }\n  .topnav .dropdown .dropdown-menu {\n    background-color: transparent;\n    border: none;\n    box-shadow: none;\n    padding-left: 15px;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n    width: auto;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n    margin: 0px;\n  }\n  .topnav .dropdown .dropdown-item {\n    position: relative;\n    background-color: transparent;\n  }\n  .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n    color: #0f9cf3;\n  }\n  .topnav .arrow-down::after {\n    right: 15px;\n    position: absolute;\n  }\n}\n@media (min-width: 992px) {\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-dark {\n    display: none;\n  }\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-light {\n    display: block;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav {\n    background-color: #252b3b;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:focus, body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav > .dropdown.active > a {\n    color: rgba(255, 255, 255, 0.9) !important;\n  }\n}\nbody[data-layout-size=boxed] {\n  background-color: #1d222e;\n}\nbody[data-layout-size=boxed] #layout-wrapper {\n  background-color: #1d222e;\n  max-width: 1300px;\n  margin: 0 auto;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\nbody[data-layout-size=boxed] #page-topbar {\n  max-width: 1300px;\n  margin: 0 auto;\n}\nbody[data-layout-size=boxed] .footer {\n  margin: 0 auto;\n  max-width: calc(1300px - 240px);\n}\nbody[data-layout-size=boxed].vertical-collpsed .footer {\n  max-width: calc(1300px - 70px);\n}\n\nbody[data-layout=horizontal][data-layout-size=boxed] #page-topbar, body[data-layout=horizontal][data-layout-size=boxed] #layout-wrapper, body[data-layout=horizontal][data-layout-size=boxed] .footer {\n  max-width: 100%;\n}\nbody[data-layout=horizontal][data-layout-size=boxed] .container-fluid, body[data-layout=horizontal][data-layout-size=boxed] .navbar-header {\n  max-width: 1300px;\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(15, 156, 243, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(111, 208, 136, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(0, 151, 167, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(255, 187, 68, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(243, 47, 83, 0.4);\n}\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: #0f9cf3;\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -12px;\n  border: 2px solid #252b3b;\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.custom-accordion .card + .card {\n  margin-top: 0.5rem;\n}\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\f0415\";\n}\n.custom-accordion .card-header {\n  border-radius: 7px;\n}\n\n.custom-accordion-arrow .card {\n  border: 1px solid #2d3448;\n  box-shadow: none;\n}\n.custom-accordion-arrow .card-header {\n  padding-left: 45px;\n  position: relative;\n}\n.custom-accordion-arrow .card-header .accor-arrow-icon {\n  position: absolute;\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  line-height: 24px;\n  font-size: 16px;\n  background-color: #0f9cf3;\n  color: #fff;\n  border-radius: 50%;\n  text-align: center;\n  left: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {\n  content: \"\\f0142\";\n}\n\n.font-family-secondary {\n  font-family: \"Inter\", sans-serif;\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 1px solid #8590a5;\n  border-radius: 50%;\n  color: #8590a5;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: #919bae;\n  background-color: #252b3b;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000;\n}\n\n.flex-1 {\n  flex: 1;\n}\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n  background: transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat;\n}\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #252b3b;\n  z-index: 9999;\n}\n\n#status {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateY(-50%);\n  margin: -20px 0 0 -20px;\n}\n\n.spinner .spin-icon {\n  font-size: 56px;\n  color: #0f9cf3;\n  position: relative;\n  display: inline-block;\n  animation: spin 1.6s infinite linear;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(359deg);\n  }\n}\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em;\n}\n.form-check-right .form-check-input {\n  float: right;\n  margin-left: 0;\n  margin-right: -1.5em;\n}\n.form-check-right .form-check-label {\n  display: block;\n}\n\n.form-check {\n  position: relative;\n  text-align: left;\n}\n\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.activity-wid {\n  margin-top: 8px;\n  margin-left: 16px;\n}\n.activity-wid .activity-list {\n  position: relative;\n  padding: 0 0 40px 30px;\n}\n.activity-wid .activity-list:before {\n  content: \"\";\n  border-left: 2px dashed rgba(15, 156, 243, 0.25);\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  top: 32px;\n}\n.activity-wid .activity-list .activity-icon {\n  position: absolute;\n  left: -15px;\n  top: 0;\n  z-index: 9;\n}\n.activity-wid .activity-list:last-child {\n  padding-bottom: 0px;\n}\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px;\n}\n.button-items .btn {\n  margin-bottom: 12px;\n  margin-left: 8px;\n}\n\n.mfp-popup-form {\n  max-width: 1140px;\n}\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n\n.icon-demo-content {\n  color: #8590a5;\n}\n.icon-demo-content i {\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  line-height: 36px;\n  font-size: 22px;\n  color: #919bae;\n  border: 2px solid #2d3448;\n  border-radius: 4px;\n  transition: all 0.4s;\n  text-align: center;\n  margin-right: 16px;\n  vertical-align: middle;\n}\n.icon-demo-content .col-lg-4 {\n  margin-top: 24px;\n}\n.icon-demo-content .col-lg-4:hover i {\n  color: #fff;\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n}\n\n.grid-structure .grid-container {\n  background-color: #212529;\n  margin-top: 10px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  padding: 10px 20px;\n}\n\n.card-radio {\n  background-color: #252b3b;\n  border: 2px solid #2d3448;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n.card-radio:hover {\n  cursor: pointer;\n}\n\n.card-radio-label {\n  display: block;\n}\n\n.card-radio-input {\n  display: none;\n}\n.card-radio-input:checked + .card-radio {\n  border-color: #0f9cf3 !important;\n}\n\n.navs-carousel .owl-nav {\n  margin-top: 16px;\n}\n.navs-carousel .owl-nav button {\n  width: 30px;\n  height: 30px;\n  line-height: 28px !important;\n  font-size: 20px !important;\n  border-radius: 50% !important;\n  background-color: rgba(15, 156, 243, 0.25) !important;\n  color: #0f9cf3 !important;\n  margin: 4px 8px !important;\n}\n\n@media print {\n  .vertical-menu,\n.right-bar,\n.page-title-box,\n.navbar-header,\n.footer {\n    display: none !important;\n  }\n\n  .card-body,\n.main-content,\n.right-bar,\n.page-content,\nbody {\n    padding: 0;\n    margin: 0;\n  }\n\n  .card {\n    border: 0;\n  }\n}\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n.fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n\n.fc th.fc-widget-header {\n  background: #2d3448;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: #2d3448;\n}\n.fc-unthemed td.fc-today {\n  background: #2d3448;\n}\n\n.fc-button {\n  background: #252b3b;\n  border-color: #2d3448;\n  color: #f6f6f6;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important;\n}\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #0f9cf3;\n  color: #fff;\n  text-shadow: none;\n}\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center;\n}\n\n#external-events .external-event {\n  text-align: left !important;\n  padding: 8px 16px;\n}\n\n.fc-event, .fc-event-dot {\n  background-color: #0f9cf3;\n}\n\n.fc-event .fc-content {\n  color: #fff;\n}\n\n.fc .table-bordered td, .fc .table-bordered th {\n  border-color: #2d3448;\n}\n@media (max-width: 575.98px) {\n  .fc .fc-toolbar {\n    display: block;\n  }\n}\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n.fc .fc-toolbar .fc-right,\n.fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0;\n  }\n  .fc .fc-toolbar > * > * {\n    float: none;\n  }\n  .fc .fc-toolbar .fc-today-button {\n    display: none;\n  }\n}\n.fc .fc-toolbar .btn {\n  text-transform: capitalize;\n}\n\n.fc-bootstrap .fc-today.alert-info {\n  background-color: #2d3448;\n}\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\n  background-color: #000 !important;\n}\n\n[dir=rtl] .fc-header-toolbar {\n  direction: ltr !important;\n}\n\n[dir=rtl] .fc-toolbar > * > :not(:first-child) {\n  margin-left: 0.75em;\n}\n\n.sp-container {\n  background-color: #252b3b;\n}\n.sp-container button {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.71094rem;\n  border-radius: 0.2rem;\n  font-weight: 400;\n  color: #eff2f7;\n}\n.sp-container button.sp-palette-toggle {\n  background-color: #2d3448;\n}\n.sp-container button.sp-choose {\n  background-color: #6fd088;\n  margin-left: 5px;\n  margin-right: 0;\n}\n\n.sp-palette-container {\n  border-right: 1px solid #2d3448;\n}\n\n.sp-input {\n  background-color: #292f3f;\n  border-color: #2d3448 !important;\n  color: #8590a5;\n}\n.sp-input:focus {\n  outline: none;\n}\n\n[dir=rtl] .sp-alpha {\n  direction: rtl;\n}\n[dir=rtl] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important;\n}\n[dir=rtl] input.spectrum.with-add-on {\n  border: 1px solid #2d3448;\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\n\n#session-timeout-dialog .close {\n  display: none;\n}\n#session-timeout-dialog .countdown-holder {\n  color: #f32f53;\n  font-weight: 500;\n}\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f32f53;\n  box-shadow: none;\n}\n\n.rs-control {\n  margin: 0px auto;\n}\n\n.rs-path-color {\n  background-color: #2d3448;\n}\n\n.rs-bg-color {\n  background-color: #252b3b;\n}\n\n.rs-border {\n  border-color: transparent;\n}\n\n.rs-handle {\n  background-color: #f6f6f6;\n}\n\n.rs-circle-border .rs-border {\n  border: 8px solid #2d3448;\n}\n\n.rs-disabled {\n  opacity: 1;\n}\n\n.outer-border .rs-border {\n  border-width: 0px;\n}\n.outer-border .rs-border.rs-outer {\n  border: 14px solid #2d3448;\n}\n.outer-border .rs-handle {\n  margin-left: 0 !important;\n}\n.outer-border .rs-path-color {\n  background-color: transparent;\n}\n\n.outer-border-dot .rs-border.rs-outer {\n  border: 16px dotted;\n}\n.outer-border-dot .rs-handle {\n  margin-left: 0 !important;\n}\n\n.rs-range-primary .rs-range-color {\n  background-color: #0f9cf3;\n}\n.rs-range-primary .rs-handle-dot {\n  background-color: #84ccf9;\n  border-color: #0f9cf3;\n}\n.rs-range-primary .rs-handle-dot:after {\n  background-color: #0f9cf3;\n}\n.rs-range-primary.rs-circle-border .rs-handle {\n  background-color: #0f9cf3;\n}\n.rs-range-primary.outer-border-dot .rs-border.rs-outer {\n  border-color: #84ccf9;\n}\n\n.rs-range-secondary .rs-range-color {\n  background-color: #919bae;\n}\n.rs-range-secondary .rs-handle-dot {\n  background-color: #d7dbe2;\n  border-color: #919bae;\n}\n.rs-range-secondary .rs-handle-dot:after {\n  background-color: #919bae;\n}\n.rs-range-secondary.rs-circle-border .rs-handle {\n  background-color: #919bae;\n}\n.rs-range-secondary.outer-border-dot .rs-border.rs-outer {\n  border-color: #d7dbe2;\n}\n\n.rs-range-success .rs-range-color {\n  background-color: #6fd088;\n}\n.rs-range-success .rs-handle-dot {\n  background-color: #cbeed4;\n  border-color: #6fd088;\n}\n.rs-range-success .rs-handle-dot:after {\n  background-color: #6fd088;\n}\n.rs-range-success.rs-circle-border .rs-handle {\n  background-color: #6fd088;\n}\n.rs-range-success.outer-border-dot .rs-border.rs-outer {\n  border-color: #cbeed4;\n}\n\n.rs-range-info .rs-range-color {\n  background-color: #0097a7;\n}\n.rs-range-info .rs-handle-dot {\n  background-color: #22eaff;\n  border-color: #0097a7;\n}\n.rs-range-info .rs-handle-dot:after {\n  background-color: #0097a7;\n}\n.rs-range-info.rs-circle-border .rs-handle {\n  background-color: #0097a7;\n}\n.rs-range-info.outer-border-dot .rs-border.rs-outer {\n  border-color: #22eaff;\n}\n\n.rs-range-warning .rs-range-color {\n  background-color: #ffbb44;\n}\n.rs-range-warning .rs-handle-dot {\n  background-color: #ffe8be;\n  border-color: #ffbb44;\n}\n.rs-range-warning .rs-handle-dot:after {\n  background-color: #ffbb44;\n}\n.rs-range-warning.rs-circle-border .rs-handle {\n  background-color: #ffbb44;\n}\n.rs-range-warning.outer-border-dot .rs-border.rs-outer {\n  border-color: #ffe8be;\n}\n\n.rs-range-danger .rs-range-color {\n  background-color: #f32f53;\n}\n.rs-range-danger .rs-handle-dot {\n  background-color: #faa3b3;\n  border-color: #f32f53;\n}\n.rs-range-danger .rs-handle-dot:after {\n  background-color: #f32f53;\n}\n.rs-range-danger.rs-circle-border .rs-handle {\n  background-color: #f32f53;\n}\n.rs-range-danger.outer-border-dot .rs-border.rs-outer {\n  border-color: #faa3b3;\n}\n\n.rs-range-pink .rs-range-color {\n  background-color: #e83e8c;\n}\n.rs-range-pink .rs-handle-dot {\n  background-color: #f5abcd;\n  border-color: #e83e8c;\n}\n.rs-range-pink .rs-handle-dot:after {\n  background-color: #e83e8c;\n}\n.rs-range-pink.rs-circle-border .rs-handle {\n  background-color: #e83e8c;\n}\n.rs-range-pink.outer-border-dot .rs-border.rs-outer {\n  border-color: #f5abcd;\n}\n\n.rs-range-light .rs-range-color {\n  background-color: #2d3448;\n}\n.rs-range-light .rs-handle-dot {\n  background-color: #5c6a93;\n  border-color: #2d3448;\n}\n.rs-range-light .rs-handle-dot:after {\n  background-color: #2d3448;\n}\n.rs-range-light.rs-circle-border .rs-handle {\n  background-color: #2d3448;\n}\n.rs-range-light.outer-border-dot .rs-border.rs-outer {\n  border-color: #5c6a93;\n}\n\n.rs-range-dark .rs-range-color {\n  background-color: #eff2f7;\n}\n.rs-range-dark .rs-handle-dot {\n  background-color: white;\n  border-color: #eff2f7;\n}\n.rs-range-dark .rs-handle-dot:after {\n  background-color: #eff2f7;\n}\n.rs-range-dark.rs-circle-border .rs-handle {\n  background-color: #eff2f7;\n}\n.rs-range-dark.outer-border-dot .rs-border.rs-outer {\n  border-color: white;\n}\n\n.rs-handle-arrow .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #f6f6f6;\n  margin: -6px 0px 0px 14px !important;\n  border-width: 6px 104px 6px 4px;\n}\n.rs-handle-arrow .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 22px;\n  width: 22px;\n  background: #f6f6f6;\n  right: -11px;\n  bottom: -11px;\n  border-radius: 100px;\n}\n\n.rs-handle-arrow-dash .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #f6f6f6;\n  margin: -8px 0 0 14px !important;\n}\n.rs-handle-arrow-dash .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 12px;\n  width: 12px;\n  background: #f6f6f6;\n  right: -6px;\n  bottom: -6px;\n  border-radius: 100%;\n}\n.rs-handle-arrow-dash .rs-handle:after {\n  display: block;\n  content: \" \";\n  width: 80px;\n  position: absolute;\n  top: -1px;\n  right: 0px;\n  border-top: 2px dotted #f6f6f6;\n}\n\n.irs {\n  font-family: var(--bs-font-sans-serif);\n}\n\n.irs--round .irs-bar,\n.irs--round .irs-to,\n.irs--round .irs-from,\n.irs--round .irs-single {\n  background: #0f9cf3 !important;\n  font-size: 11px;\n}\n.irs--round .irs-to:before,\n.irs--round .irs-from:before,\n.irs--round .irs-single:before {\n  display: none;\n}\n.irs--round .irs-line {\n  background: #2d3448;\n  border-color: #2d3448;\n}\n.irs--round .irs-grid-text {\n  font-size: 11px;\n  color: #8590a5;\n}\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #8590a5;\n  background: #2d3448;\n  font-size: 11px;\n}\n.irs--round .irs-handle {\n  border: 2px solid #0f9cf3;\n  width: 16px;\n  height: 16px;\n  top: 29px;\n  background-color: #252b3b !important;\n}\n\n.swal2-container .swal2-title {\n  font-size: 24px;\n  font-weight: 500;\n}\n\n.swal2-content {\n  font-size: 16px;\n}\n\n.swal2-icon.swal2-question {\n  border-color: #0097a7;\n  color: #0097a7;\n}\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #6fd088;\n}\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(111, 208, 136, 0.3);\n}\n.swal2-icon.swal2-warning {\n  border-color: #ffbb44;\n  color: #ffbb44;\n}\n\n.swal2-styled:focus {\n  box-shadow: none;\n}\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #0f9cf3;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n  background: #0f9cf3;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n  background: rgba(15, 156, 243, 0.3);\n}\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #0f9cf3;\n}\n\n.swal2-loader {\n  border-color: #0f9cf3 transparent #0f9cf3 transparent;\n}\n\n.symbol {\n  border-color: #252b3b;\n}\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px;\n}\n\n.rating-symbol-foreground {\n  top: 0px;\n}\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle;\n}\n.rating-star > span.badge {\n  margin-left: 4px;\n}\n\n/* =============\n   Notification\n============= */\n#toast-container > div {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 1;\n}\n#toast-container > div:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 0.9;\n}\n#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {\n  min-width: 96%;\n  margin: 4px auto;\n}\n\n.toast-primary {\n  border: 2px solid #0f9cf3 !important;\n  background-color: rgba(15, 156, 243, 0.8) !important;\n}\n\n.toast-secondary {\n  border: 2px solid #919bae !important;\n  background-color: rgba(145, 155, 174, 0.8) !important;\n}\n\n.toast-success {\n  border: 2px solid #6fd088 !important;\n  background-color: rgba(111, 208, 136, 0.8) !important;\n}\n\n.toast-info {\n  border: 2px solid #0097a7 !important;\n  background-color: rgba(0, 151, 167, 0.8) !important;\n}\n\n.toast-warning {\n  border: 2px solid #ffbb44 !important;\n  background-color: rgba(255, 187, 68, 0.8) !important;\n}\n\n.toast-danger {\n  border: 2px solid #f32f53 !important;\n  background-color: rgba(243, 47, 83, 0.8) !important;\n}\n\n.toast-pink {\n  border: 2px solid #e83e8c !important;\n  background-color: rgba(232, 62, 140, 0.8) !important;\n}\n\n.toast-light {\n  border: 2px solid #2d3448 !important;\n  background-color: rgba(45, 52, 72, 0.8) !important;\n}\n\n.toast-dark {\n  border: 2px solid #eff2f7 !important;\n  background-color: rgba(239, 242, 247, 0.8) !important;\n}\n\n.toast-error {\n  background-color: rgba(243, 47, 83, 0.8);\n  border: 2px solid #f32f53;\n}\n\n.toastr-options {\n  padding: 24px;\n  background-color: #293041;\n  margin-bottom: 0;\n  border: 1px solid #2d3448;\n}\n\n.error {\n  color: #f32f53;\n}\n\n.parsley-error {\n  border-color: #f32f53;\n}\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0;\n}\n.parsley-errors-list.filled {\n  display: block;\n}\n.parsley-errors-list > li {\n  font-size: 12px;\n  list-style: none;\n  color: #f32f53;\n  margin-top: 5px;\n}\n\n.select2-container {\n  display: block;\n}\n.select2-container .select2-selection--single {\n  background-color: #292f3f;\n  border: 1px solid #2d3448;\n  height: 38px;\n}\n.select2-container .select2-selection--single:focus {\n  outline: none;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  line-height: 36px;\n  padding-left: 12px;\n  color: #8590a5;\n}\n.select2-container .select2-selection--single .select2-selection__arrow {\n  height: 34px;\n  width: 34px;\n  right: 3px;\n}\n.select2-container .select2-selection--single .select2-selection__arrow b {\n  border-color: #8590a5 transparent transparent transparent;\n  border-width: 6px 6px 0 6px;\n}\n.select2-container .select2-selection--single .select2-selection__placeholder {\n  color: #79859c;\n}\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #8590a5 transparent !important;\n  border-width: 0 6px 6px 6px !important;\n}\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #252b3b;\n}\n.select2-container--default .select2-search--dropdown .select2-search__field {\n  border: 1px solid #2d3448;\n  background-color: #292f3f;\n  color: #919bae;\n  outline: none;\n}\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #0f9cf3;\n}\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #2d3448;\n  color: #e9ecef;\n}\n.select2-container--default .select2-results__option[aria-selected=true]:hover {\n  background-color: #0f9cf3;\n  color: #fff;\n}\n\n.select2-results__option {\n  padding: 6px 12px;\n}\n\n.select2-dropdown {\n  border: 1px solid #2d3448;\n  background-color: #252b3b;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.select2-search input {\n  border: 1px solid #2d3448;\n}\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #292f3f;\n  border: 1px solid #2d3448 !important;\n}\n.select2-container .select2-selection--multiple .select2-selection__rendered {\n  padding: 2px 10px;\n}\n.select2-container .select2-selection--multiple .select2-search__field {\n  border: 0;\n  color: #8590a5;\n}\n.select2-container .select2-selection--multiple .select2-search__field::placeholder {\n  color: #8590a5;\n}\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  background-color: #252b3b;\n  border: 1px solid #2d3448;\n  border-radius: 1px;\n  padding: 0 7px;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #79859c;\n}\n.select2-container--default .select2-results__group {\n  font-weight: 600;\n}\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px;\n}\n.select2-result-repository__avatar img {\n  width: 100%;\n  height: auto;\n  border-radius: 2px;\n}\n\n.select2-result-repository__statistics {\n  margin-top: 7px;\n}\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #8590a5;\n}\n.select2-result-repository__forks .fa,\n.select2-result-repository__stargazers .fa,\n.select2-result-repository__watchers .fa {\n  margin-right: 4px;\n}\n.select2-result-repository__forks .fa.fa-flash::before,\n.select2-result-repository__stargazers .fa.fa-flash::before,\n.select2-result-repository__watchers .fa.fa-flash::before {\n  content: \"\\f0e7\";\n  font-family: \"Font Awesome 5 Free\";\n}\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.select2-result-repository__meta {\n  overflow: hidden;\n}\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px;\n}\n\n/* CSS Switch */\ninput[switch] {\n  display: none;\n}\ninput[switch] + label {\n  font-size: 1em;\n  line-height: 1;\n  width: 56px;\n  height: 24px;\n  background-color: #79859c;\n  background-image: none;\n  border-radius: 2rem;\n  padding: 0.16667rem;\n  cursor: pointer;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n  font-weight: 500;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:before {\n  color: #eff2f7;\n  content: attr(data-off-label);\n  display: block;\n  font-family: inherit;\n  font-weight: 500;\n  font-size: 12px;\n  line-height: 21px;\n  position: absolute;\n  right: 1px;\n  margin: 3px;\n  top: -2px;\n  text-align: center;\n  min-width: 1.66667rem;\n  overflow: hidden;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:after {\n  content: \"\";\n  position: absolute;\n  left: 3px;\n  background-color: #252b3b;\n  box-shadow: none;\n  border-radius: 2rem;\n  height: 20px;\n  width: 20px;\n  top: 2px;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch]:checked + label {\n  background-color: #0f9cf3;\n}\n\ninput[switch]:checked + label {\n  background-color: #0f9cf3;\n}\ninput[switch]:checked + label:before {\n  color: #fff;\n  content: attr(data-on-label);\n  right: auto;\n  left: 3px;\n}\ninput[switch]:checked + label:after {\n  left: 33px;\n  background-color: #252b3b;\n}\n\ninput[switch=bool] + label {\n  background-color: #f32f53;\n}\n\ninput[switch=bool] + label:before, input[switch=bool]:checked + label:before,\ninput[switch=default]:checked + label:before {\n  color: #fff;\n}\n\ninput[switch=bool]:checked + label {\n  background-color: #6fd088;\n}\n\ninput[switch=default]:checked + label {\n  background-color: #a2a2a2;\n}\n\ninput[switch=primary]:checked + label {\n  background-color: #0f9cf3;\n}\n\ninput[switch=success]:checked + label {\n  background-color: #6fd088;\n}\n\ninput[switch=info]:checked + label {\n  background-color: #0097a7;\n}\n\ninput[switch=warning]:checked + label {\n  background-color: #ffbb44;\n}\n\ninput[switch=danger]:checked + label {\n  background-color: #f32f53;\n}\n\ninput[switch=dark]:checked + label {\n  background-color: #eff2f7;\n}\n\n.square-switch {\n  margin-right: 7px;\n}\n.square-switch input[switch] + label, .square-switch input[switch] + label:after {\n  border-radius: 4px;\n}\n\n.datepicker {\n  border: 1px solid #212529;\n  padding: 8px;\n  z-index: 999 !important;\n}\n.datepicker table tr th {\n  font-weight: 500;\n}\n.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n  background-color: #0f9cf3 !important;\n  background-image: none;\n  box-shadow: none;\n  color: #fff !important;\n}\n.datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n.datepicker table tr td span.focused,\n.datepicker table tr td span:hover {\n  background: #252b3b;\n}\n.datepicker table tr td.new, .datepicker table tr td.old,\n.datepicker table tr td span.new,\n.datepicker table tr td span.old {\n  color: #8590a5;\n  opacity: 0.6;\n}\n.datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n  background-color: #2d3448;\n}\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px;\n}\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.dataTables_wrapper.container-fluid {\n  padding: 0;\n}\n\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right;\n}\n@media (max-width: 767px) {\n  div.dataTables_wrapper div.dataTables_filter {\n    text-align: center;\n  }\n}\ndiv.dataTables_wrapper div.dataTables_filter input {\n  margin-left: 0.5em;\n  margin-right: 0;\n}\n\n.datatable td:focus {\n  outline: none;\n}\n\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {\n  padding-left: 0;\n}\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {\n  padding-right: 0;\n}\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important;\n}\ntable.dataTable thead .sorting:before,\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_desc:before,\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:before {\n  left: auto;\n  right: 0.5rem;\n  content: \"\\f0360\";\n  font-family: \"Material Design Icons\";\n  font-size: 1rem;\n  top: 9px;\n}\ntable.dataTable thead .sorting:after,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc:after,\ntable.dataTable thead .sorting_asc_disabled:after,\ntable.dataTable thead .sorting_desc_disabled:after {\n  left: auto;\n  right: 0.5em;\n  content: \"\\f035d\";\n  font-family: \"Material Design Icons\";\n  top: 15px;\n  font-size: 1rem;\n}\ntable.dataTable thead tr th.sorting_asc, table.dataTable thead tr th.sorting_desc, table.dataTable thead tr th.sorting,\ntable.dataTable thead tr td.sorting_asc,\ntable.dataTable thead tr td.sorting_desc,\ntable.dataTable thead tr td.sorting {\n  padding-left: 12px;\n  padding-right: 30px;\n}\ntable.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n  background-color: rgba(15, 156, 243, 0.2);\n}\ntable.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n  border-color: rgba(15, 156, 243, 0.2);\n  color: #0f9cf3;\n}\ntable.dataTable tbody td:focus {\n  outline: none !important;\n}\ntable.dataTable tbody th.focus, table.dataTable tbody td.focus {\n  outline: 2px solid #0f9cf3 !important;\n  outline-offset: -1px;\n  background-color: rgba(15, 156, 243, 0.15);\n}\n\n.dataTables_info {\n  font-weight: 600;\n}\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  background-color: #6fd088;\n  bottom: auto;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #f32f53;\n}\n\ndiv.dt-button-info {\n  background-color: #0f9cf3;\n  border: none;\n  color: #fff;\n  box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21;\n}\ndiv.dt-button-info h2 {\n  border-bottom: none;\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #fff;\n}\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous,\nli.paginate_button.next {\n    display: inline-block;\n    font-size: 1.5rem;\n  }\n\n  li.paginate_button {\n    display: none;\n  }\n\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1rem 0 0 !important;\n  }\n\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1rem;\n  }\n}\n.activate-select .sorting_1 {\n  background-color: #212529;\n}\n\n.table-bordered {\n  border: 1px solid #2d3448;\n}\n\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td {\n  position: relative;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control {\n  padding-left: 30px;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {\n  top: 64%;\n  left: 5px;\n  height: 14px;\n  width: 14px;\n  margin-top: -14px;\n  display: block;\n  position: absolute;\n  color: #fff;\n  border: 2px solid #fff;\n  border-radius: 14px;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  line-height: 12px;\n  content: \"+\";\n  background-color: #0f9cf3;\n}\n\n.tox-tinymce {\n  border: 2px solid #2d3448 !important;\n}\n\n.tox .tox-statusbar {\n  border-top: 1px solid #2d3448 !important;\n}\n.tox .tox-menubar,\n.tox .tox-edit-area__iframe,\n.tox .tox-statusbar {\n  background-color: #252b3b !important;\n  background: none !important;\n}\n.tox .tox-mbtn {\n  color: #f6f6f6 !important;\n}\n.tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n  background-color: #2d3448 !important;\n}\n.tox .tox-tbtn:hover {\n  background-color: #2d3448 !important;\n}\n.tox .tox-toolbar__primary {\n  border-color: #2d3448 !important;\n}\n.tox .tox-toolbar,\n.tox .tox-toolbar__overflow,\n.tox .tox-toolbar__primary {\n  background: #2d3448 !important;\n}\n.tox .tox-tbtn {\n  color: #f6f6f6 !important;\n}\n.tox .tox-tbtn svg {\n  fill: #f6f6f6 !important;\n}\n.tox .tox-edit-area__iframe {\n  background-color: #252b3b !important;\n}\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: #f6f6f6 !important;\n}\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #232938 !important;\n}\n\n.tox-tinymce-aux {\n  z-index: 1050 !important;\n}\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed #79859c;\n  background: #252b3b;\n  border-radius: 6px;\n}\n.dropzone .dz-message {\n  font-size: 24px;\n  width: 100%;\n}\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  position: relative;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav:before {\n  content: \"\";\n  width: 100%;\n  height: 2px;\n  background-color: #2d3448;\n  position: absolute;\n  left: 0;\n  top: 26px;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n  display: inline-block;\n  width: 38px;\n  height: 38px;\n  line-height: 34px;\n  border: 2px solid #0f9cf3;\n  color: #0f9cf3;\n  text-align: center;\n  border-radius: 50%;\n  position: relative;\n  background-color: #252b3b;\n}\n@media (max-width: 991.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: block;\n    margin: 0 auto 8px !important;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n  display: block;\n  margin-top: 8px;\n  font-weight: 600;\n}\n@media (max-width: 575.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n    display: none;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n  background-color: transparent;\n  color: #f6f6f6;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n  background-color: #0f9cf3;\n  color: #fff;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n  display: inline-block;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n  display: inline-block;\n  padding: 0.47rem 0.75rem;\n  background-color: #0f9cf3;\n  color: #fff;\n  border-radius: 0.25rem;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n  cursor: not-allowed;\n  background-color: #36acf5;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n  float: right;\n}\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px;\n}\n\n.table-rep-plugin .btn-toolbar {\n  display: block;\n}\n.table-rep-plugin .table-responsive {\n  border: none !important;\n}\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #2d3448;\n  color: #eff2f7;\n  border: 1px solid #292f42;\n}\n.table-rep-plugin .btn-group .btn-default.btn-primary {\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n  color: #fff;\n}\n.table-rep-plugin .btn-group.pull-right {\n  float: right;\n}\n.table-rep-plugin .btn-group.pull-right .dropdown-menu {\n  right: 0;\n  transform: none !important;\n  top: 100% !important;\n}\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal;\n}\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: #79859c !important;\n}\n.table-rep-plugin .checkbox-row:hover {\n  background-color: #293041 !important;\n}\n.table-rep-plugin .checkbox-row label {\n  display: inline-block;\n  padding-left: 5px;\n  position: relative;\n}\n.table-rep-plugin .checkbox-row label::before {\n  -o-transition: 0.3s ease-in-out;\n  -webkit-transition: 0.3s ease-in-out;\n  background-color: #fff;\n  border-radius: 3px;\n  border: 1px solid #2d3448;\n  content: \"\";\n  display: inline-block;\n  height: 17px;\n  left: 0;\n  margin-left: -20px;\n  position: absolute;\n  transition: 0.3s ease-in-out;\n  width: 17px;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row label::after {\n  color: #252b3b;\n  display: inline-block;\n  font-size: 11px;\n  height: 16px;\n  left: 0;\n  margin-left: -20px;\n  padding-left: 3px;\n  padding-top: 1px;\n  position: absolute;\n  top: -1px;\n  width: 16px;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label {\n  opacity: 0.65;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:focus + label::before {\n  outline-offset: -2px;\n  outline: none;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  content: \"\\f00c\";\n  font-family: \"Font Awesome 5 Free\";\n  font-weight: 900;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label::before {\n  background-color: #212529;\n  cursor: not-allowed;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::before {\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  color: #fff;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #0f9cf3;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header table {\n  color: #fff;\n}\n\n@media (min-width: 992px) {\n  body[data-layout=horizontal] .fixed-solution .sticky-table-header {\n    top: 120px !important;\n  }\n}\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #2d3448;\n  background-color: #292f3f;\n  color: #8590a5;\n  border-radius: 0.25rem;\n}\n.table-edits input:focus, .table-edits select:focus {\n  outline: none;\n  border-color: #31384c;\n}\n\n.apex-charts {\n  min-height: 10px !important;\n}\n.apex-charts text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #8590a5;\n}\n.apex-charts .apexcharts-canvas {\n  margin: 0 auto;\n}\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important;\n}\n\n.apexcharts-legend-series {\n  font-weight: 500;\n}\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #32394e;\n}\n\n.apexcharts-legend-text {\n  color: #919bae !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important;\n}\n\n.apexcharts-pie-label {\n  fill: #fff !important;\n}\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #8590a5;\n}\n\n/* Flot chart */\n.flot-charts-height {\n  height: 320px;\n}\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(239, 242, 247, 0.9);\n  z-index: 100;\n  color: #212529;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  border-radius: 4px;\n}\n\n.legendLabel {\n  color: #8590a5;\n}\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #eff2f7 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #f8f9fa !important;\n}\n\n.jqsfield {\n  color: #252b3b !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important;\n}\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #212529;\n  border-radius: 3px;\n}\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #0f9cf3;\n  border-radius: 4px;\n  padding: 10px 20px;\n}\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute;\n}\n.gmaps-overlay_arrow.above {\n  bottom: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-top: 16px solid #0f9cf3;\n}\n.gmaps-overlay_arrow.below {\n  top: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-bottom: 16px solid #0f9cf3;\n}\n\n.jvectormap-label {\n  border: none;\n  background: #eff2f7;\n  color: #212529;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.9rem;\n  padding: 5px 8px;\n}\n\n.editable-input .form-control {\n  display: inline-block;\n}\n\n.editable-buttons {\n  margin-left: 7px;\n}\n.editable-buttons .editable-cancel {\n  margin-left: 7px;\n}\n\n/* ==============\n  Email\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px;\n}\n\n.email-rightbar {\n  margin-left: 260px;\n}\n\n.chat-user-box p.user-title {\n  color: #eff2f7;\n  font-weight: 600;\n}\n.chat-user-box p {\n  font-size: 12px;\n}\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%;\n  }\n\n  .email-rightbar {\n    margin: 0;\n  }\n}\n.mail-list a {\n  display: block;\n  color: #919bae;\n  line-height: 24px;\n  padding: 8px 5px;\n}\n.mail-list a.active {\n  color: #f32f53;\n  font-weight: 500;\n}\n\n.message-list {\n  display: block;\n  padding-left: 0;\n}\n.message-list li {\n  position: relative;\n  display: block;\n  height: 50px;\n  line-height: 50px;\n  cursor: default;\n  transition-duration: 0.3s;\n}\n.message-list li a {\n  color: #919bae;\n}\n.message-list li:hover {\n  background: #2d3448;\n  transition-duration: 0.05s;\n}\n.message-list li .col-mail {\n  float: left;\n  position: relative;\n}\n.message-list li .col-mail-1 {\n  width: 320px;\n}\n.message-list li .col-mail-1 .star-toggle,\n.message-list li .col-mail-1 .checkbox-wrapper-mail,\n.message-list li .col-mail-1 .dot {\n  display: block;\n  float: left;\n}\n.message-list li .col-mail-1 .dot {\n  border: 4px solid transparent;\n  border-radius: 100px;\n  margin: 22px 26px 0;\n  height: 0;\n  width: 0;\n  line-height: 0;\n  font-size: 0;\n}\n.message-list li .col-mail-1 .checkbox-wrapper-mail {\n  margin: 15px 10px 0 20px;\n}\n.message-list li .col-mail-1 .star-toggle {\n  margin-top: 18px;\n  margin-left: 5px;\n}\n.message-list li .col-mail-1 .title {\n  position: absolute;\n  top: 0;\n  left: 110px;\n  right: 0;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  margin-bottom: 0;\n}\n.message-list li .col-mail-2 {\n  position: absolute;\n  top: 0;\n  left: 320px;\n  right: 0;\n  bottom: 0;\n}\n.message-list li .col-mail-2 .subject,\n.message-list li .col-mail-2 .date {\n  position: absolute;\n  top: 0;\n}\n.message-list li .col-mail-2 .subject {\n  left: 0;\n  right: 200px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n.message-list li .col-mail-2 .date {\n  right: 0;\n  width: 170px;\n  padding-left: 80px;\n}\n.message-list li.active, .message-list li.active:hover {\n  box-shadow: inset 3px 0 0 #0f9cf3;\n}\n.message-list li.unread {\n  background-color: #2d3448;\n  font-weight: 500;\n  color: #dee4ef;\n}\n.message-list li.unread a {\n  color: #dee4ef;\n  font-weight: 500;\n}\n.message-list .checkbox-wrapper-mail {\n  cursor: pointer;\n  height: 20px;\n  width: 20px;\n  position: relative;\n  display: inline-block;\n  box-shadow: inset 0 0 0 1px #79859c;\n  border-radius: 1px;\n}\n.message-list .checkbox-wrapper-mail input {\n  opacity: 0;\n  cursor: pointer;\n}\n.message-list .checkbox-wrapper-mail input:checked ~ label {\n  opacity: 1;\n}\n.message-list .checkbox-wrapper-mail label {\n  position: absolute;\n  height: 20px;\n  width: 20px;\n  left: 0;\n  cursor: pointer;\n  opacity: 0;\n  margin-bottom: 0;\n  transition-duration: 0.05s;\n  top: 0;\n}\n.message-list .checkbox-wrapper-mail label:before {\n  content: \"\\f012c\";\n  font-family: \"Material Design Icons\";\n  top: 0;\n  height: 20px;\n  color: #dee4ef;\n  width: 20px;\n  position: absolute;\n  margin-top: -16px;\n  left: 4px;\n  font-size: 13px;\n}\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px;\n  }\n}\n/* ==============\n  Timeline\n===================*/\n.cd-container {\n  width: 90%;\n  max-width: 1170px;\n  margin: 0 auto;\n}\n\n.cd-container::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n#cd-timeline {\n  margin-bottom: 2em;\n  margin-top: 2em;\n  padding: 2em 0;\n  position: relative;\n}\n#cd-timeline::before {\n  border-left: 3px solid #2d3448;\n  content: \"\";\n  height: 100%;\n  left: 18px;\n  position: absolute;\n  top: 0;\n  width: 3px;\n}\n\n@media only screen and (min-width: 1170px) {\n  #cd-timeline {\n    margin-bottom: 3em;\n    margin-top: 3em;\n  }\n  #cd-timeline::before {\n    left: 50%;\n    margin-left: -2px;\n  }\n}\n.cd-timeline-block {\n  margin: 2em 0;\n  position: relative;\n}\n.cd-timeline-block:after {\n  clear: both;\n  content: \"\";\n  display: table;\n}\n\n.cd-timeline-block:first-child {\n  margin-top: 0;\n}\n\n.cd-timeline-block:last-child {\n  margin-bottom: 0;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-block {\n    margin: 4em 0;\n  }\n\n  .cd-timeline-block:first-child {\n    margin-top: 0;\n  }\n\n  .cd-timeline-block:last-child {\n    margin-bottom: 0;\n  }\n}\n.cd-timeline-img {\n  position: absolute;\n  top: 20px;\n  left: 0;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 30px;\n  font-size: 20px;\n  color: #fff;\n  background-color: #0f9cf3;\n  border: 5px solid #fff;\n}\n.cd-timeline-img i {\n  margin-left: 1px;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-img {\n    width: 40px;\n    height: 40px;\n    line-height: 30px;\n    left: 50%;\n    margin-left: -20px;\n  }\n\n  .cssanimations .cd-timeline-img.is-hidden {\n    visibility: hidden;\n  }\n\n  .cssanimations .cd-timeline-img.bounce-in {\n    visibility: visible;\n    -webkit-animation: cd-bounce-1 0.6s;\n    -moz-animation: cd-bounce-1 0.6s;\n    animation: cd-bounce-1 0.6s;\n  }\n}\n.cd-timeline-content {\n  border-radius: 5px;\n  border: 1px solid #2d3448;\n  margin-left: 60px;\n  padding: 1em;\n  position: relative;\n}\n.cd-timeline-content:after {\n  clear: both;\n  content: \"\";\n  display: table;\n}\n.cd-timeline-content h2 {\n  margin-top: 0;\n}\n.cd-timeline-content .cd-read-more {\n  background: #0f9cf3;\n  border-radius: 0.25em;\n  color: white;\n  display: inline-block;\n  float: right;\n  font-size: 14px;\n  padding: 0.8em 1em;\n}\n.cd-timeline-content .cd-date {\n  display: inline-block;\n  font-size: 14px;\n}\n.cd-timeline-content h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n}\n\n.no-touch .cd-timeline-content .cd-read-more:hover {\n  background-color: #bac4cb;\n}\n\n.cd-timeline-content .cd-date {\n  float: left;\n  padding: 0.8em 0;\n  opacity: 0.7;\n}\n\n.cd-timeline-content::before {\n  content: \"\";\n  position: absolute;\n  top: 16px;\n  right: 100%;\n  height: 0;\n  width: 0;\n  border: 12px solid transparent;\n  border-right: 12px solid #2d3448;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-content {\n    margin-left: 0;\n    padding: 1.6em;\n    width: 45%;\n  }\n\n  .cd-timeline-content::before {\n    top: 24px;\n    left: 100%;\n    border-color: transparent;\n    border-left-color: #2d3448;\n  }\n\n  .cd-timeline-content .cd-read-more {\n    float: left;\n  }\n\n  .cd-timeline-content .cd-date {\n    position: absolute;\n    width: 100%;\n    left: 122%;\n    top: 20px;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content {\n    float: right;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content::before {\n    top: 24px;\n    left: auto;\n    right: 100%;\n    border-color: transparent;\n    border-right-color: #2d3448;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-read-more {\n    float: right;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-date {\n    left: auto;\n    right: 122%;\n    text-align: right;\n  }\n\n  .cssanimations .cd-timeline-content.is-hidden {\n    visibility: hidden;\n  }\n\n  .cssanimations .cd-timeline-content.bounce-in {\n    visibility: visible;\n    -webkit-animation: cd-bounce-2 0.6s;\n    -moz-animation: cd-bounce-2 0.6s;\n    animation: cd-bounce-2 0.6s;\n  }\n}\n@media only screen and (min-width: 1170px) {\n  .cssanimations .cd-timeline-block:nth-child(even) .cd-timeline-content.bounce-in {\n    -webkit-animation: cd-bounce-2-inverse 0.6s;\n    -moz-animation: cd-bounce-2-inverse 0.6s;\n    animation: cd-bounce-2-inverse 0.6s;\n  }\n}\n.social-links li a {\n  background: #353d55;\n  border-radius: 50%;\n  color: white;\n  display: inline-block;\n  height: 30px;\n  line-height: 30px;\n  text-align: center;\n  width: 30px;\n}\n\n.auth-body-bg {\n  background-image: url(../images/auth-bg.jpg);\n  background-repeat: no-repeat;\n  background-size: cover;\n  background-position: center;\n}\n.auth-body-bg .bg-overlay {\n  background: rgba(51, 51, 51, 0.97);\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0px;\n  top: 0px;\n}\n\n.wrapper-page {\n  margin: 7.5% auto;\n  max-width: 460px;\n  position: relative;\n}\n.wrapper-page .auth-logo {\n  font-size: 28px;\n  line-height: 70px;\n}\n.wrapper-page .auth-logo.logo-light {\n  display: block;\n}\n.wrapper-page .auth-logo.logo-dark {\n  display: none;\n}\n\n.ex-page-content h1 {\n  font-size: 98px;\n  font-weight: 700;\n  line-height: 150px;\n  text-shadow: rgba(61, 61, 61, 0.3) 1px 1px, rgba(61, 61, 61, 0.2) 2px 2px, rgba(61, 61, 61, 0.3) 3px 3px;\n}", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1002;\r\n    background-color: $header-bg;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n\r\n    .dropdown {\r\n        .show.header-item {\r\n            background-color: darken($header-bg, 2%);\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-dark {\r\n    display: $display-block;\r\n}\r\n\r\n.logo-light {\r\n    display: $display-none;\r\n}\r\n\r\n/* Search */\r\n\r\n.app-search {\r\n    padding: calc(#{$header-height - 38px} / 2) 0;\r\n\r\n    .form-control {\r\n        border: none;\r\n        height: 38px;\r\n        padding-left: 40px;\r\n        padding-right: 20px;\r\n        background-color: $topbar-search-bg;\r\n        box-shadow: none;\r\n        border-radius: 30px;\r\n    }\r\n    span {\r\n        position: absolute;\r\n        z-index: 10;\r\n        font-size: 16px;\r\n        line-height: 38px;\r\n        left: 13px;\r\n        top: 0;\r\n        color: $gray-600;\r\n    }\r\n}\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height} + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: $header-item-color;\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: $header-item-color;\r\n    }\r\n\r\n\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: $gray-300;\r\n    padding: 3px;\r\n}\r\n\r\n.user-dropdown{\r\n    .dropdown-item{\r\n        i{\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 22px;\r\n        color: $header-item-color;\r\n    }\r\n\r\n    .noti-dot{\r\n        position: absolute;\r\n        display: inline-block;\r\n        height: 6px;\r\n        width: 6px;    \r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        top: 20px;\r\n        right: 14px;   \r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .d-flex {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: $gray-300;\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: $border-color;\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .ri-fullscreen-line:before {\r\n            content: \"\\ed73\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: $header-dark-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(55px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 15px;\r\n        }    \r\n    }\r\n}\r\n\r\n\r\n\r\nbody[data-topbar=\"colored\"] {\r\n    #page-topbar { \r\n        background-color: $header-colored-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}", "// Variables\n\n//\n// custom-variables\n//\n\n// Vertical Sidebar - Default Light\n$sidebar-bg: #252b3b;\n$sidebar-menu-item-color: #8590a5;\n$sidebar-menu-sub-item-color: #8590a5;\n$sidebar-menu-item-icon-color: #8590a5;\n$sidebar-menu-item-hover-color: #d7e4ec;\n$sidebar-menu-item-active-color: #d7e4ec;\n$sidebar-width:  240px;\n$sidebar-collapsed-width:  70px;\n$sidebar-width-sm:  160px;\n\n// Vertical Sidebar - Dark\n$sidebar-dark-bg: #252b3b; //252b3b\n$sidebar-dark-menu-item-color: #8590a5;\n$sidebar-dark-menu-sub-item-color: #8590a5;\n$sidebar-dark-menu-item-icon-color: #8590a5;\n$sidebar-dark-menu-item-hover-color: #ffffff;\n$sidebar-dark-menu-item-active-color: #ffffff;\n\n// Topbar - Deafult Light\n$header-height: 70px;\n$header-bg: #272d3e;\n$header-item-color: #919bae;\n\n// Topbar - Dark\n$header-dark-bg: #252b3b;\n$header-dark-item-color: #e9ecef;\n\n// Topbar - Colored\n$header-colored-bg: #0f9cf3;\n\n// Topbar Search\n$topbar-search-bg: #2b3244;\n\n// Footer\n$footer-height: 60px;\n$footer-bg: #212734;\n$footer-color: #919bae;\n\n// Horizontal nav\n$topnav-bg:   #282e3f;\n$menu-item-color: #919bae;\n$menu-item-active-color: #0f9cf3;\n\n// Right Sidebar\n$rightbar-width:  280px;\n\n// Display\n$display-none: block;\n$display-block: none;\n\n// Brand \n$navbar-brand-box-width: 240px;\n\n// Boxed layout width\n$boxed-layout-width:    1300px;\n$boxed-body-bg:       #1d222e;\n\n// Font Weight\n$font-weight-medium: 500;\n$font-weight-semibold: 600;\n\n// apex charts\n$apex-grid-color: #32394e;\n\n// table\n$table-head-bg:               rgba($gray-600, .05);\n$table-dark-border-color:     tint-color($gray-800, 7.5%);\n\n// Custom Font\n$font-family-secondary:      'Inter', sans-serif;\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff;\n$gray-100: #212529;\n$gray-200: #252b3b;\n$gray-300: #2d3448;\n$gray-400: #79859c;\n$gray-500: #8590a5;\n$gray-600: #919bae;\n$gray-700: #f6f6f6;\n$gray-800: #eff2f7;\n$gray-900: #f8f9fa;\n$black:    #000;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n);\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n$blue:    #5438DC;\n$indigo:  #564ab1;\n$purple:  #0f9cf3;\n$pink:    #e83e8c;\n$red:     #f32f53;\n$orange:  #f1734f;\n$yellow:  #ffbb44;\n$green:   #6fd088;\n$teal:    #050505;\n$cyan:    #0097a7;\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n);\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $purple;\n$secondary:     $gray-600;\n$success:       $green;\n$info:          $cyan;\n$warning:       $yellow;\n$danger:        $red;\n$light:         $gray-300;\n$dark:          $gray-800;\n// scss-docs-end theme-color-variables\n$theme-colors: ();\n// scss-docs-start theme-colors-map\n$theme-colors: map-merge(\n  (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"pink\":       $pink,\n  \"light\":      $light,\n  \"dark\":       $dark\n  ),\n  $theme-colors\n);\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\");\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   1.7;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black;\n$color-contrast-light:     $white;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%);\n$blue-200: tint-color($blue, 60%);\n$blue-300: tint-color($blue, 40%);\n$blue-400: tint-color($blue, 20%);\n$blue-500: $blue;\n$blue-600: shade-color($blue, 20%);\n$blue-700: shade-color($blue, 40%);\n$blue-800: shade-color($blue, 60%);\n$blue-900: shade-color($blue, 80%);\n\n$indigo-100: tint-color($indigo, 80%);\n$indigo-200: tint-color($indigo, 60%);\n$indigo-300: tint-color($indigo, 40%);\n$indigo-400: tint-color($indigo, 20%);\n$indigo-500: $indigo;\n$indigo-600: shade-color($indigo, 20%);\n$indigo-700: shade-color($indigo, 40%);\n$indigo-800: shade-color($indigo, 60%);\n$indigo-900: shade-color($indigo, 80%);\n\n$purple-100: tint-color($purple, 80%);\n$purple-200: tint-color($purple, 60%);\n$purple-300: tint-color($purple, 40%);\n$purple-400: tint-color($purple, 20%);\n$purple-500: $purple;\n$purple-600: shade-color($purple, 20%);\n$purple-700: shade-color($purple, 40%);\n$purple-800: shade-color($purple, 60%);\n$purple-900: shade-color($purple, 80%);\n\n$pink-100: tint-color($pink, 80%);\n$pink-200: tint-color($pink, 60%);\n$pink-300: tint-color($pink, 40%);\n$pink-400: tint-color($pink, 20%);\n$pink-500: $pink;\n$pink-600: shade-color($pink, 20%);\n$pink-700: shade-color($pink, 40%);\n$pink-800: shade-color($pink, 60%);\n$pink-900: shade-color($pink, 80%);\n\n$red-100: tint-color($red, 80%);\n$red-200: tint-color($red, 60%);\n$red-300: tint-color($red, 40%);\n$red-400: tint-color($red, 20%);\n$red-500: $red;\n$red-600: shade-color($red, 20%);\n$red-700: shade-color($red, 40%);\n$red-800: shade-color($red, 60%);\n$red-900: shade-color($red, 80%);\n\n$orange-100: tint-color($orange, 80%);\n$orange-200: tint-color($orange, 60%);\n$orange-300: tint-color($orange, 40%);\n$orange-400: tint-color($orange, 20%);\n$orange-500: $orange;\n$orange-600: shade-color($orange, 20%);\n$orange-700: shade-color($orange, 40%);\n$orange-800: shade-color($orange, 60%);\n$orange-900: shade-color($orange, 80%);\n\n$yellow-100: tint-color($yellow, 80%);\n$yellow-200: tint-color($yellow, 60%);\n$yellow-300: tint-color($yellow, 40%);\n$yellow-400: tint-color($yellow, 20%);\n$yellow-500: $yellow;\n$yellow-600: shade-color($yellow, 20%);\n$yellow-700: shade-color($yellow, 40%);\n$yellow-800: shade-color($yellow, 60%);\n$yellow-900: shade-color($yellow, 80%);\n\n\n$green-100: tint-color($green, 80%);\n$green-200: tint-color($green, 60%);\n$green-300: tint-color($green, 40%);\n$green-400: tint-color($green, 20%);\n$green-500: $green;\n$green-600: shade-color($green, 20%);\n$green-700: shade-color($green, 40%);\n$green-800: shade-color($green, 60%);\n$green-900: shade-color($green, 80%);\n\n\n$teal-100: tint-color($teal, 80%);\n$teal-200: tint-color($teal, 60%);\n$teal-300: tint-color($teal, 40%);\n$teal-400: tint-color($teal, 20%);\n$teal-500: $teal;\n$teal-600: shade-color($teal, 20%);\n$teal-700: shade-color($teal, 40%);\n$teal-800: shade-color($teal, 60%);\n$teal-900: shade-color($teal, 80%);\n\n$cyan-100: tint-color($cyan, 80%);\n$cyan-200: tint-color($cyan, 60%);\n$cyan-300: tint-color($cyan, 40%);\n$cyan-400: tint-color($cyan, 20%);\n$cyan-500: $cyan;\n$cyan-600: shade-color($cyan, 20%);\n$cyan-700: shade-color($cyan, 40%);\n$cyan-800: shade-color($cyan, 60%);\n$cyan-900: shade-color($cyan, 80%);\n\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n);\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n);\n\n$purples: (\n  \"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n);\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n);\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n);\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n);\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n);\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n);\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n);\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n);\n\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n);\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                false;\n$enable-rounded:              true;\n$enable-shadows:              false;\n$enable-gradients:            false;\n$enable-transitions:          true;\n$enable-reduced-motion:       true;\n$enable-smooth-scroll:        true;\n$enable-grid-classes:         true;\n$enable-button-pointers:      true;\n$enable-rfs:                  true;\n$enable-validation-icons:     true;\n$enable-negative-margins:     true;\n$enable-deprecation-messages: true;\n$enable-important-utilities:  true;\n\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs-;\n\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0));\n// scss-docs-end variable-gradient\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem;\n$spacers: (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n);\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null);\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n);\n// scss-docs-end position-map\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   #1d222e;\n$body-color:                $gray-400;\n$body-text-align:           null;\n\n/// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\":  to-rgb($body-color)\n  )\n);\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\");\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg)\n  )\n);\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\");\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary;\n$link-decoration:                         none;\n$link-shade-percentage:                   20%;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage);\n$link-hover-decoration:                   underline;\n\n$stretched-link-pseudo-element:           after;\n$stretched-link-z-index:                  1;\n\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n);\n// scss-docs-end grid-breakpoints\n\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n);\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12;\n$grid-gutter-width:           24px;\n$grid-row-columns:            6;\n\n$gutters: $spacers;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width * 0.5;\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px;\n$border-widths: (\n  0: 0,\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n);\n\n$border-color:                $gray-300;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem;\n$border-radius-lg:            .4rem;\n$border-radius-sm:            .2rem;\n$border-radius-pill:          50rem;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow-sm:               0 1px 1px rgba($black,.05);\n$box-shadow:                  0 2px 4px rgba($black,.08);\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175);\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075);\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white;\n$component-active-bg:         $primary;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em;\n$caret-vertical-align:        $caret-width * .85;\n$caret-spacing:               $caret-width * .85;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out;\n$transition-fade:             opacity .15s linear;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease;\n$transition-collapse-width:   width .35s ease;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n);\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      'Nunito', sans-serif;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif);\n$font-family-code:            var(--#{$variable-prefix}font-monospace);\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null;\n$font-size-base:              0.9rem;\n$font-size-sm:                $font-size-base * .875;\n$font-size-lg:                $font-size-base * 1.25;\n\n\n$font-weight-lighter:         lighter;\n$font-weight-light:           300;\n$font-weight-normal:          400;\n$font-weight-bold:            600;\n$font-weight-bolder:          bolder;\n\n$font-weight-base:            $font-weight-normal;\n$line-height-base:            1.5;\n$line-height-sm:              1.25;\n$line-height-lg:              2;\n\n$h1-font-size:                $font-size-base * 2.5;\n$h2-font-size:                $font-size-base * 2;\n$h3-font-size:                $font-size-base * 1.75;\n$h4-font-size:                $font-size-base * 1.5;\n$h5-font-size:                $font-size-base * 1.25;\n$h6-font-size:                $font-size-base;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n);\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * 0.5;\n$headings-font-family:        null;\n$headings-font-style:         null;\n$headings-font-weight:        500;\n$headings-line-height:        1.2;\n$headings-color:              null;\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 6rem,\n  2: 5.5rem,\n  3: 4.5rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n);\n\n$display-font-weight: 300;\n$display-line-height: $headings-line-height;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25;\n$lead-font-weight:            300;\n\n$small-font-size:             80%;\n\n$sub-sup-font-size:           .75em;\n$text-muted:                  $gray-600;\n\n$initialism-font-size:        $small-font-size;\n\n$blockquote-margin-y:         $spacer;\n$blockquote-font-size:        $font-size-base * 1.25;\n$blockquote-footer-color:     $gray-600;\n$blockquote-footer-font-size: $small-font-size;\n\n$hr-margin-y:                 $spacer;\n$hr-color:                    inherit;\n$hr-height:                   $border-width;\n$hr-opacity:                  .2;\n\n$legend-margin-bottom:        .5rem;\n$legend-font-size:            1.5rem;\n$legend-font-weight:          null;\n\n$mark-padding:                .2em;\n\n$dt-font-weight:              $font-weight-bold;\n\n$nested-kbd-font-weight:      $font-weight-bold;\n\n$list-inline-padding:         .5rem;\n\n$mark-bg:                     #fcf8e3;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .75rem;\n$table-cell-padding-x:        .75rem;\n$table-cell-padding-y-sm:     .3rem;\n$table-cell-padding-x-sm:     .3rem;\n\n$table-cell-vertical-align:   top;\n\n$table-color:                 $body-color;\n$table-bg:                    null;\n$table-accent-bg:             transparent;\n\n$table-th-font-weight:        null;\n\n\n$table-striped-color:         $table-color;\n$table-striped-bg-factor:     .05;\n$table-striped-bg:            rgba($gray-500, .05);\n\n$table-active-color:          $table-color;\n$table-active-bg-factor:      .1;\n$table-active-bg:             $table-hover-bg;\n\n$table-hover-color:           $table-color;\n$table-hover-bg-factor:       .075;\n$table-hover-bg:              rgba($gray-500, .05);\n\n$table-border-factor:         .1;\n$table-border-width:          $border-width;\n$table-border-color:          $border-color;\n\n$table-striped-order:         odd;\n$table-group-separator-color: $border-color;\n\n$table-caption-color:         $text-muted;\n\n$table-bg-scale:              -80%;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $light,\n);\n// scss-docs-end table-variables\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .47rem;\n$input-btn-padding-x:         .75rem;\n$input-btn-font-family:       null;\n$input-btn-font-size:         $font-size-base;\n$input-btn-line-height:       $line-height-base;\n\n$input-btn-focus-width:       .15rem;\n$input-btn-focus-color-opacity: .25;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity);\n$input-btn-focus-blur:          0;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color;\n\n$input-btn-padding-y-sm:      .25rem;\n$input-btn-padding-x-sm:      .5rem;\n$input-btn-font-size-sm:      $font-size-sm;\n\n$input-btn-padding-y-lg:      .5rem;\n$input-btn-padding-x-lg:      1rem;\n$input-btn-font-size-lg:      $font-size-lg;\n\n$input-btn-border-width:      $border-width;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y;\n$btn-padding-x:               $input-btn-padding-x;\n$btn-font-family:             $input-btn-font-family;\n$btn-font-size:               $input-btn-font-size;\n$btn-line-height:             $input-btn-line-height;\n$btn-white-space:             null; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm;\n$btn-padding-x-sm:            $input-btn-padding-x-sm;\n$btn-font-size-sm:            $input-btn-font-size-sm;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg;\n$btn-padding-x-lg:            $input-btn-padding-x-lg;\n$btn-font-size-lg:            $input-btn-font-size-lg;\n\n$btn-border-width:            $input-btn-border-width;\n\n$btn-font-weight:             $font-weight-normal;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075);\n$btn-focus-width:             $input-btn-focus-width;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow;\n$btn-disabled-opacity:        .65;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125);\n\n$btn-link-color:              $link-color;\n$btn-link-hover-color:        $link-hover-color;\n$btn-link-disabled-color:     $gray-600;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius;\n$btn-border-radius-sm:        $border-radius-sm;\n$btn-border-radius-lg:        $border-radius-lg;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$btn-hover-bg-shade-amount:       15%;\n$btn-hover-bg-tint-amount:        15%;\n$btn-hover-border-shade-amount:   20%;\n$btn-hover-border-tint-amount:    10%;\n$btn-active-bg-shade-amount:      20%;\n$btn-active-bg-tint-amount:       20%;\n$btn-active-border-shade-amount:  25%;\n$btn-active-border-tint-amount:   10%;\n// scss-docs-end btn-variables\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem;\n$form-text-font-size:                   $small-font-size;\n$form-text-font-style:                  null;\n$form-text-font-weight:                 null;\n$form-text-color:                       $text-muted;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem;\n$form-label-font-size:                  null;\n$form-label-font-style:                 null;\n$form-label-font-weight:                null;\n$form-label-color:                      null;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y;\n$input-padding-x:                       $input-btn-padding-x;\n$input-font-family:                     $input-btn-font-family;\n$input-font-size:                       $input-btn-font-size;\n$input-font-weight:                     $font-weight-base;\n$input-line-height:                     $input-btn-line-height;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm;\n$input-padding-x-sm:                    $input-btn-padding-x-sm;\n$input-font-size-sm:                    $input-btn-font-size-sm;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg;\n$input-padding-x-lg:                    $input-btn-padding-x-lg;\n$input-font-size-lg:                    $input-btn-font-size-lg;\n\n$input-bg:                              tint-color($gray-200, 2%);\n$input-disabled-bg:                     tint-color($gray-200, 1%);\n$input-disabled-border-color:           null;\n\n$input-color:                           $gray-500;\n$input-border-color:                    $gray-300;\n$input-border-width:                    $input-btn-border-width;\n$input-box-shadow:                      $box-shadow-inset;\n\n$input-border-radius:                   $border-radius;\n$input-border-radius-lg:                $border-radius-lg;\n$input-border-radius-sm:                $border-radius-sm;\n\n$input-focus-bg:                        tint-color($input-bg, 1%);\n$input-focus-border-color:              tint-color($input-border-color, 2%);\n$input-focus-color:                     $input-color;\n$input-focus-width:                     $input-btn-focus-width;\n$input-focus-box-shadow:                none;\n\n$input-placeholder-color:               $gray-600;\n$input-plaintext-color:                 $body-color;\n\n$input-height-border:                   $input-border-width * 2;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2);\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y);\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * 0.5);\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false));\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false));\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false));\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em;\n$form-check-min-height:                   $font-size-base * $line-height-base;\n$form-check-padding-start:                $form-check-input-width + .5em;\n$form-check-margin-bottom:                .125rem;\n$form-check-label-color:                  null;\n$form-check-label-cursor:                 null;\n$form-check-transition:                   background-color .15s ease-in-out, background-position .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-check-input-active-filter:          brightness(90%);\n\n$form-check-input-bg:                     $gray-300;\n$form-check-input-border:                 1px solid $gray-400;\n$form-check-input-border-radius:          .25em;\n$form-check-radio-border-radius:          50%;\n$form-check-input-focus-border:           $input-focus-border-color;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow;\n\n$form-check-input-checked-color:          $component-active-color;\n$form-check-input-checked-bg-color:       $component-active-bg;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\");\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\");\n\n$form-check-input-indeterminate-color:          $component-active-color;\n$form-check-input-indeterminate-bg-color:       $component-active-bg;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\");\n\n$form-check-input-disabled-opacity:        .5;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity;\n\n$form-check-inline-margin-end:    1rem;\n// scss-docs-end form-check-variables\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(255, 255, 255, .5);\n$form-switch-width:               2em;\n$form-switch-padding-start:       $form-switch-width + .5em;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\");\n$form-switch-border-radius:       $form-switch-width;\n$form-switch-transition:          background-position .15s ease-in-out;\n\n$form-switch-focus-color:         $form-switch-color;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\");\n\n$form-switch-checked-color:       $component-active-color;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\");\n$form-switch-checked-bg-position: right center;\n// scss-docs-end form-switch-variables\n$form-check-inline-margin-end:    1rem;\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y;\n$input-group-addon-padding-x:           $input-padding-x;\n$input-group-addon-font-weight:         $input-font-weight;\n$input-group-addon-color:               $input-color;\n$input-group-addon-bg:                  $gray-300;\n$input-group-addon-border-color:        $input-border-color;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y;\n$form-select-padding-x:             $input-padding-x;\n$form-select-font-family:           $input-font-family;\n$form-select-font-size:             $input-font-size;\n$form-select-indicator-padding:     1.75rem; // Extra padding to account for the presence of the background-image based indicator\n$form-select-font-weight:           $input-font-weight;\n$form-select-line-height:           $input-line-height;\n$form-select-color:                 $input-color;\n$form-select-disabled-color:        $gray-600;\n$form-select-bg:                    $input-bg;\n$form-select-disabled-bg:           $gray-200;\n$form-select-disabled-border-color: $input-disabled-border-color;\n$form-select-bg-position:           right $form-select-padding-x center;\n$form-select-bg-size:               16px 12px; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\");\n\n$form-select-feedback-icon-padding-end: add(1em * .75, (2 * $form-select-padding-y * .75) + $form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-position:    center right ($form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half;\n\n$form-select-border-width:        $input-border-width;\n$form-select-border-color:        $input-border-color;\n$form-select-border-radius:       $border-radius;\n$form-select-box-shadow:          $box-shadow-inset;\n\n$form-select-focus-border-color:  $input-focus-border-color;\n$form-select-focus-width:         $input-focus-width;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color;\n\n$form-select-padding-y-sm:        $input-padding-y-sm;\n$form-select-padding-x-sm:        $input-padding-x-sm;\n$form-select-font-size-sm:        $input-font-size-sm;\n\n$form-select-padding-y-lg:        $input-padding-y-lg;\n$form-select-padding-x-lg:        $input-padding-x-lg;\n$form-select-font-size-lg:        $input-font-size-lg;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100%;\n$form-range-track-height:         .5rem;\n$form-range-track-cursor:         pointer;\n$form-range-track-bg:             $gray-300;\n$form-range-track-border-radius:  1rem;\n$form-range-track-box-shadow:     $box-shadow-inset;\n\n$form-range-thumb-width:                   1rem;\n$form-range-thumb-height:                  $form-range-thumb-width;\n$form-range-thumb-bg:                      $component-active-bg;\n$form-range-thumb-border:                  0;\n$form-range-thumb-border-radius:           1rem;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1);\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%);\n$form-range-thumb-disabled-bg:             $gray-500;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color;\n$form-file-button-bg:             $input-group-addon-bg;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%);\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border);\n$form-floating-padding-x:         $input-padding-x;\n$form-floating-padding-y:         1rem;\n$form-floating-input-padding-t:   1.625rem;\n$form-floating-input-padding-b:   .625rem;\n$form-floating-label-opacity:     .65;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem);\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top;\n$form-feedback-font-size:           $form-text-font-size;\n$form-feedback-font-style:          $form-text-font-style;\n$form-feedback-valid-color:         $success;\n$form-feedback-invalid-color:       $danger;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\");\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\");\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n);\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000;\n$zindex-sticky:                     1020;\n$zindex-fixed:                      1030;\n$zindex-modal-backdrop:             1040;\n$zindex-offcanvas:                  1050;\n$zindex-modal-backdrop:             1050;\n$zindex-modal:                      1060;\n$zindex-popover:                    1070;\n$zindex-tooltip:                    1080;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem;\n$nav-link-padding-x:                1rem;\n$nav-link-font-size:                null;\n$nav-link-font-weight:              null;\n$nav-link-color:                    null;\n$nav-link-hover-color:              null;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;\n$nav-link-disabled-color:           $gray-600;\n\n$nav-tabs-border-color:             lighten($gray-300, 4%);\n$nav-tabs-border-width:             $border-width;\n$nav-tabs-border-radius:            $border-radius;\n$nav-tabs-link-hover-border-color:  lighten($gray-300, 4%) lighten($gray-300, 4%) $nav-tabs-border-color;\n$nav-tabs-link-active-color:        $gray-700;\n$nav-tabs-link-active-bg:           $gray-200;\n$nav-tabs-link-active-border-color: lighten($gray-300, 4%) lighten($gray-300, 4%) $nav-tabs-link-active-bg;\n\n$nav-pills-border-radius:           $border-radius;\n$nav-pills-link-active-color:       $component-active-color;\n$nav-pills-link-active-bg:          $component-active-bg;\n// scss-docs-end nav-variables\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * 0.5;\n$navbar-padding-x:                  null;\n\n$navbar-nav-link-padding-x:         .5rem;\n\n$navbar-brand-font-size:            $font-size-lg;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * 0.5;\n$navbar-brand-margin-end:           1rem;\n\n$navbar-toggler-padding-y:          .25rem;\n$navbar-toggler-padding-x:          .75rem;\n$navbar-toggler-font-size:          $font-size-lg;\n$navbar-toggler-border-radius:      $btn-border-radius;\n$navbar-toggler-focus-width:        $btn-focus-width;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55);\n$navbar-dark-hover-color:           rgba($white, .75);\n$navbar-dark-active-color:          $white;\n$navbar-dark-disabled-color:        rgba($white, .25);\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-dark-toggler-border-color:  rgba($white, .1);\n\n$navbar-light-color:                rgba($black, .55);\n$navbar-light-hover-color:          rgba($black, .7);\n$navbar-light-active-color:         rgba($black, .9);\n$navbar-light-disabled-color:       rgba($black, .3);\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-light-toggler-border-color: rgba($black, .1);\n\n$navbar-light-brand-color:                $navbar-light-active-color;\n$navbar-light-brand-hover-color:          $navbar-light-active-color;\n$navbar-dark-brand-color:                 $navbar-dark-active-color;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem;\n$dropdown-padding-x:                0;\n$dropdown-padding-y:                .5rem;\n$dropdown-spacer:                   .125rem;\n$dropdown-font-size:                $font-size-base;\n$dropdown-color:                    $body-color;\n$dropdown-bg:                       $gray-200;\n$dropdown-border-color:             lighten($gray-200, 4%);\n$dropdown-border-radius:            $border-radius;\n$dropdown-border-width:             1px;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width});\n$dropdown-divider-bg:               $gray-200;\n$dropdown-divider-margin-y:         $spacer * 0.5;\n$dropdown-box-shadow:               $box-shadow;\n\n$dropdown-link-color:               $gray-500;\n$dropdown-link-hover-color:         darken($gray-900, 5%);\n$dropdown-link-hover-bg:            $gray-300;\n\n$dropdown-link-active-color:        darken($gray-900, 5%);\n$dropdown-link-active-bg:           $gray-300;\n\n$dropdown-link-disabled-color:      $gray-600;\n\n$dropdown-item-padding-y:           .35rem;\n$dropdown-item-padding-x:           1.5rem;\n\n$dropdown-header-color:             $gray-600;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300;\n$dropdown-dark-bg:                  $gray-800;\n$dropdown-dark-border-color:        $dropdown-border-color;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg;\n$dropdown-dark-box-shadow:          null;\n$dropdown-dark-link-color:          $dropdown-dark-color;\n$dropdown-dark-link-hover-color:    $white;\n$dropdown-dark-link-hover-bg:       rgba($white, .15);\n$dropdown-dark-link-active-color:   $dropdown-link-active-color;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg;\n$dropdown-dark-link-disabled-color: $gray-500;\n$dropdown-dark-header-color:        $gray-500;\n// scss-docs-end dropdown-dark-variables\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .5rem;\n$pagination-padding-x:              .75rem;\n$pagination-padding-y-sm:           .25rem;\n$pagination-padding-x-sm:           .5rem;\n$pagination-padding-y-lg:           .75rem;\n$pagination-padding-x-lg:           1.5rem;\n\n$pagination-color:                  $gray-500;\n$pagination-bg:                     $gray-200;\n$pagination-border-width:           $border-width;\n$pagination-border-radius:          $border-radius;\n$pagination-margin-start:           -$pagination-border-width;\n$pagination-border-color:           $gray-300;\n\n$pagination-focus-color:            $link-hover-color;\n$pagination-focus-bg:               $gray-200;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow;\n$pagination-focus-outline:          0;\n\n$pagination-hover-color:            $link-hover-color;\n$pagination-hover-bg:               $gray-200;\n$pagination-hover-border-color:     $gray-300;\n\n$pagination-active-color:           $component-active-color;\n$pagination-active-bg:              $component-active-bg;\n$pagination-active-border-color:    $pagination-active-bg;\n\n$pagination-disabled-color:         $gray-600;\n$pagination-disabled-bg:            $gray-200;\n$pagination-disabled-border-color:  $gray-300;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$pagination-border-radius-sm:       $border-radius-sm;\n$pagination-border-radius-lg:       $border-radius-lg;\n// scss-docs-end pagination-variables\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5;\n$placeholder-opacity-min:           .2;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     1.25rem;\n$card-spacer-x:                     1.25rem;\n$card-title-spacer-y:               $spacer * 0.5;\n$card-border-width:                 0;\n$card-border-radius:                $border-radius;\n$card-border-color:                 $gray-300;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width});\n$card-cap-padding-y:                $card-spacer-y * 0.5;\n$card-cap-padding-x:                $card-spacer-x;\n$card-cap-bg:                       $gray-300;\n$card-cap-color:                    null;\n$card-height:                       null;\n$card-color:                        null;\n$card-bg:                           #252b3b;\n\n$card-title-desc:                   $gray-400;\n\n$card-img-overlay-padding:          $spacer;\n\n$card-group-margin:                 $grid-gutter-width * 0.5;\n// scss-docs-end card-variables\n\n// Accordion\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem;\n$accordion-padding-x:                     1.25rem;\n$accordion-color:                         $body-color;\n$accordion-bg:                            transparent;\n$accordion-border-width:                  $border-width;\n$accordion-border-color:                  $border-color;\n$accordion-border-radius:                 $border-radius;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width);\n\n$accordion-body-padding-y:                $accordion-padding-y;\n$accordion-body-padding-x:                $accordion-padding-x;\n\n$accordion-button-padding-y:              $accordion-padding-y;\n$accordion-button-padding-x:              $accordion-padding-x;\n$accordion-button-color:                  $accordion-color;\n$accordion-button-bg:                     $accordion-bg;\n$accordion-transition:                    $btn-transition, border-radius .15s ease;\n$accordion-button-active-bg:              rgba($component-active-bg, 0.1);\n$accordion-button-active-color:           shade-color($primary, 10%);\n\n$accordion-button-focus-border-color:     $accordion-button-active-bg;\n$accordion-button-focus-box-shadow:       none;\n\n$accordion-icon-width:                    16px;\n$accordion-icon-color:                    $accordion-color;\n$accordion-icon-active-color:             $accordion-button-active-color;\n$accordion-icon-transition:               transform .2s ease-in-out;\n$accordion-icon-transform:                rotate(180deg);\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm;\n$tooltip-max-width:                 200px;\n$tooltip-color:                     $white;\n$tooltip-bg:                        $black;\n$tooltip-border-radius:             $border-radius;\n$tooltip-opacity:                   .9;\n$tooltip-padding-y:                 .4rem;\n$tooltip-padding-x:                 .7rem;\n$tooltip-margin:                    0;\n\n$tooltip-arrow-width:               .8rem;\n$tooltip-arrow-height:              .4rem;\n$tooltip-arrow-color:               $tooltip-bg;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x;\n$form-feedback-tooltip-font-size:     $tooltip-font-size;\n$form-feedback-tooltip-line-height:   $line-height-base;\n$form-feedback-tooltip-opacity:       $tooltip-opacity;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius;\n// scss-docs-start tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm;\n$popover-bg:                        $white;\n$popover-max-width:                 276px;\n$popover-border-width:              $border-width;\n$popover-border-color:              $gray-200;\n$popover-border-radius:             $border-radius-lg;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width);\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2);\n\n$popover-header-bg:                 shade-color($popover-bg, 4%);\n$popover-header-color:              $gray-200;\n$popover-header-padding-y:          .5rem;\n$popover-header-padding-x:          .75rem;\n\n$popover-body-color:                $body-color;\n$popover-body-padding-y:            $popover-header-padding-y;\n$popover-body-padding-x:            $popover-header-padding-x;\n\n\n$popover-arrow-width:               1rem;\n$popover-arrow-height:              .5rem;\n$popover-arrow-color:               $popover-bg;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05);\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px;\n$toast-padding-x:                   .75rem;\n$toast-padding-y:                   .25rem;\n$toast-font-size:                   .875rem;\n$toast-color:                       null;\n$toast-background-color:            rgba($white, .85);\n$toast-border-width:                1px;\n$toast-border-color:                rgba(0, 0, 0, .1);\n$toast-border-radius:               .25rem;\n$toast-box-shadow:                  $box-shadow;\n$toast-spacing:                     $container-padding-x;\n\n$toast-header-color:                $gray-600;\n$toast-header-background-color:     rgba($white, .85);\n$toast-header-border-color:         rgba(0, 0, 0, .05);\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   75%;\n$badge-font-weight:                 $font-weight-medium;\n$badge-color:                       $white;\n$badge-padding-y:                   .25em;\n$badge-padding-x:                   .4em;\n$badge-border-radius:               $border-radius;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               1rem;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem;\n$modal-dialog-margin:               .5rem;\n$modal-dialog-margin-y-sm-up:       1.75rem;\n\n$modal-title-line-height:           $line-height-base;\n\n$modal-content-color:               null;\n$modal-content-bg:                  $gray-200;\n$modal-content-border-color:        $gray-300;\n$modal-content-border-width:        $border-width;\n$modal-content-border-radius:       $border-radius-lg;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);\n$modal-content-box-shadow-xs:       $box-shadow-sm;\n$modal-content-box-shadow-sm-up:    $box-shadow;\n\n\n$modal-backdrop-bg:                 $black;\n$modal-backdrop-opacity:            .5;\n$modal-header-border-color:         $border-color;\n$modal-footer-border-color:         $modal-header-border-color;\n$modal-header-border-width:         $modal-content-border-width;\n$modal-footer-border-width:         $modal-header-border-width;\n$modal-header-padding-y:            $modal-inner-padding;\n$modal-header-padding-x:            $modal-inner-padding;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility\n\n$modal-sm:                          300px;\n$modal-md:                          500px;\n$modal-lg:                          800px;\n$modal-xl:                          1140px;\n\n$modal-fade-transform:              translate(0, -50px);\n$modal-show-transform:              none;\n$modal-transition:                  transform .3s ease-out;\n$modal-scale-transform:             scale(1.02);\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   .75rem;\n$alert-padding-x:                   1.25rem;\n$alert-margin-bottom:               1rem;\n$alert-border-radius:               $border-radius;\n$alert-link-font-weight:            $font-weight-bold;\n$alert-border-width:                $border-width;\n\n$alert-bg-scale:                    -80%;\n$alert-border-scale:                -70%;\n$alert-color-scale:                 40%;\n\n$alert-dismissible-padding-r:       $alert-padding-x * 3; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   .625rem;\n$progress-font-size:                $font-size-base * .75;\n$progress-bg:                       $gray-300;\n$progress-border-radius:            $border-radius;\n$progress-box-shadow:               $box-shadow-inset;\n$progress-bar-color:                $white;\n$progress-bar-bg:                   $primary;\n$progress-bar-animation-timing:     1s linear infinite;\n$progress-bar-transition:           width .6s ease;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  null;\n$list-group-bg:                     $gray-200;\n$list-group-border-color:           $border-color;\n$list-group-border-width:           $border-width;\n$list-group-border-radius:          $border-radius;\n\n$list-group-item-padding-y:         .75rem;\n$list-group-item-padding-x:         1.25rem;\n$list-group-item-bg-scale:          -80%;\n$list-group-item-color-scale:       40%;\n\n$list-group-hover-bg:               $gray-100;\n$list-group-active-color:           $component-active-color;\n$list-group-active-bg:              $component-active-bg;\n$list-group-active-border-color:    $list-group-active-bg;\n\n$list-group-disabled-color:         $gray-600;\n$list-group-disabled-bg:            $list-group-bg;\n\n$list-group-action-color:           $gray-700;\n$list-group-action-hover-color:     $list-group-action-color;\n\n$list-group-action-active-color:    $body-color;\n$list-group-action-active-bg:       $gray-200;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem;\n$thumbnail-bg:                      $body-bg;\n$thumbnail-border-width:            $border-width;\n$thumbnail-border-color:            $gray-300;\n$thumbnail-border-radius:           $border-radius;\n$thumbnail-box-shadow:              $box-shadow-sm;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size;\n$figure-caption-color:              $gray-600;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null;\n$breadcrumb-padding-y:              .75rem;\n$breadcrumb-padding-x:              1rem;\n$breadcrumb-item-padding:           .5rem;\n\n$breadcrumb-margin-bottom:          1rem;\n\n$breadcrumb-bg:                     null;\n$breadcrumb-divider-color:          $gray-600;\n$breadcrumb-active-color:           $gray-600;\n$breadcrumb-divider:                quote(\"\\F0142\");\n$breadcrumb-divider-flipped:        $breadcrumb-divider;\n$breadcrumb-border-radius:          null;\n// scss-docs-end breadcrumb-variables\n\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white;\n$carousel-control-width:             15%;\n$carousel-control-opacity:           .5;\n$carousel-control-hover-opacity:     .9;\n$carousel-control-transition:        opacity .15s ease;\n\n$carousel-indicator-width:           30px;\n$carousel-indicator-height:          3px;\n$carousel-indicator-hit-area-height: 10px;\n$carousel-indicator-spacer:          3px;\n$carousel-indicator-opacity:         .5;\n$carousel-indicator-active-bg:       $white;\n$carousel-indicator-active-opacity:  1;\n$carousel-indicator-transition:      opacity .6s ease;\n\n$carousel-caption-width:             70%;\n$carousel-caption-color:             $white;\n$carousel-caption-padding-y:         1.25rem;\n$carousel-caption-spacer:            1.25rem;\n\n$carousel-control-icon-width:        2rem;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\");\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\");\n\n$carousel-transition-duration:       .6s;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black;\n$carousel-dark-caption-color:        $black;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100);\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem;\n$spinner-height:          $spinner-width;\n$spinner-vertical-align:  -.125em;\n$spinner-border-width:    .25em;\n$spinner-animation-speed: .75s;\n\n$spinner-width-sm:        1rem;\n$spinner-height-sm:       $spinner-width-sm;\n$spinner-border-width-sm: .2em;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em;\n$btn-close-height:           $btn-close-width;\n$btn-close-padding-x:        .25em;\n$btn-close-padding-y:        $btn-close-padding-x;\n$btn-close-color:            $white;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-bg-dark:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$black}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-focus-shadow:     none;\n$btn-close-opacity:          .5;\n$btn-close-hover-opacity:    .75;\n$btn-close-focus-opacity:    1;\n$btn-close-disabled-opacity: .25;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%);\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding;\n$offcanvas-padding-x:               $modal-inner-padding;\n$offcanvas-horizontal-width:        400px;\n$offcanvas-vertical-height:         30vh;\n$offcanvas-transition-duration:     .3s;\n$offcanvas-border-color:            $modal-content-border-color;\n$offcanvas-border-width:            $modal-content-border-width;\n$offcanvas-title-line-height:       $modal-title-line-height;\n$offcanvas-bg-color:                $modal-content-bg;\n$offcanvas-color:                   $modal-content-color;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    87.5%;\n$code-color:                        $pink;\n\n$kbd-padding-y:                     .2rem;\n$kbd-padding-x:                     .4rem;\n$kbd-font-size:                     $code-font-size;\n$kbd-color:                         $white;\n$kbd-bg:                            $gray-900;\n\n$pre-color:                         $gray-900;", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n    padding-bottom: $grid-gutter-width;\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        font-size: 15px;\r\n        text-transform: uppercase;\r\n        font-weight: 600;\r\n    }\r\n}\r\n", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    color: $footer-color;\r\n    left: $sidebar-width;\r\n    height: $footer-height;\r\n    box-shadow: $box-shadow;\r\n    background-color: $footer-bg;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 10px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(-180deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0140\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.5rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 13.3px;\r\n                transition: all .4s;\r\n                font-family: $font-family-secondary;\r\n                font-weight: 500;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.5rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 1.1rem;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                    opacity: .75;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 4px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 3.2rem;\r\n                        font-size: 13px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4.2rem;\r\n                                font-size: 13.5px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n    font-family: $font-family-secondary;\r\n    opacity: .5;\r\n}\r\n\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 20px;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            color: $primary;\r\n                            background-color: darken($sidebar-bg, 4%);\r\n                            transition: none;\r\n\r\n                            i{\r\n                                color: $primary;\r\n                            }\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n\r\n    .user-profile{\r\n        h4{\r\n            color: $white;\r\n        }\r\n        span{\r\n            color: $gray-400 !important;\r\n        }\r\n    }\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1400px;\r\n\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: lighten($card-bg, 1%);\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n\r\n        @media (max-width: 992px) {\r\n            width: auto;\r\n        }\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n        @media (max-width: 991px){\r\n            left: 0;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            &.menu-title{\r\n                background-color: lighten($sidebar-dark-bg, 2%);\r\n            }\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li {\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        li {\r\n                            a{\r\n                                padding-left: 1.5rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n// Colored Sidebar\r\n\r\nbody[data-sidebar=\"colored\"] {\r\n    .user-profile{\r\n        h4{\r\n            color: $white;\r\n        }\r\n        span{\r\n            color: $gray-400 !important;\r\n        }\r\n    }\r\n\r\n    .vertical-menu {\r\n        background: $primary;\r\n    }\r\n    .navbar-brand-box{\r\n        background-color: $primary;\r\n        .logo-dark{\r\n            display: none;\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                &.menu-title{\r\n                    color: rgba($white, 0.6);\r\n                }\r\n  \r\n                a{\r\n                    color: rgba($white, 0.6);\r\n                    i{\r\n                        color: rgba($white, 0.6);\r\n                    }\r\n                    &.waves-effect {\r\n                        .waves-ripple {\r\n                          background: rgba($white, 0.1);\r\n                        }\r\n                    }\r\n                }\r\n  \r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            color: rgba($white,.5);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                >ul{\r\n                    >li{\r\n                        &:hover>a{\r\n                            background-color: lighten($primary, 2%);\r\n                            color: $white;\r\n                            i{\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                       \r\n                    }\r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $white !important;\r\n        > a{\r\n            color: $white !important;\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $white !important;\r\n        }\r\n        .active {\r\n            color: $white !important;\r\n\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $white !important;\r\n    }\r\n}\r\n\r\n.vertical-collpsed {\r\n    .user-profile{\r\n        display: none;\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: $topnav-bg;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 15px;\r\n            position: relative;\r\n            padding: 1rem 1.3rem;\r\n            color: $menu-item-color;\r\n            font-family: $font-family-secondary;\r\n\r\n            i{\r\n                font-size: 15px;\r\n                vertical-align: middle;\r\n                display: inline-block;\r\n            }\r\n            &:focus, &:hover{\r\n                color: $menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: $menu-item-color;\r\n            &.active, &:hover{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: $menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: $display-block;\r\n            span.logo-sm{\r\n                display: $display-block;\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 15px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n        \r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n\r\n        .topnav{\r\n            background-color: $header-dark-bg;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: $boxed-body-bg;\r\n    #layout-wrapper {\r\n        background-color: $body-bg;\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: $primary;\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n\n// avatar group\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n  .avatar-group-item {\n    margin-left: -12px;\n    border: 2px solid $card-bg;\n    border-radius: 50%;\n    transition: all 0.2s;\n    &:hover{\n      position: relative;\n      transform: translateY(-2px);\n    }\n  }\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .card-header{\r\n        border-radius: 7px;\r\n    }\r\n}\r\n\r\n.custom-accordion-arrow{\r\n    .card{\r\n        border: 1px solid $border-color;\r\n        box-shadow: none;\r\n    }\r\n    .card-header{\r\n        padding-left: 45px;\r\n        position: relative;\r\n\r\n        .accor-arrow-icon{\r\n            position: absolute;\r\n            display: inline-block;\r\n            width: 24px;\r\n            height: 24px;\r\n            line-height: 24px;\r\n            font-size: 16px;\r\n            background-color: $primary;\r\n            color: $white;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            left: 10px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-arrow-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n// Font Family\n.font-family-secondary {\n    font-family: $font-family-secondary;\n}\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 2px);\n    display: block;\n    border: 1px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n\n.w-xs {\n    min-width: 80px;\n}\n\n.w-sm {\n    min-width: 95px;\n}\n\n.w-md {\n    min-width: 110px;\n}\n\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n// overlay\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    opacity: 0.7;\n    background-color: $black;\n}\n\n// flex-1\n\n.flex-1{\n    flex: 1;\n}\n\n\n\n// alert\n\n.alert-dismissible {\n    .btn-close {\n        font-size: 10px;\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\n        background: transparent escape-svg($btn-close-bg-dark) center / $btn-close-width auto no-repeat;\n    }\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner{\r\n    .spin-icon {\r\n        font-size: 56px;\r\n        color: $primary;\r\n        position: relative;\r\n        display: inline-block;\r\n        animation: spin 1.6s infinite linear;\r\n    }\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(359deg);\r\n    }\r\n}\r\n  ", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;;\r\n  .form-check-input{\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n  .form-check-label{\r\n    display: block;\r\n  }\r\n}\r\n\r\n.form-check{\r\n  position: relative;\r\n  text-align: left /*rtl: right*/;\r\n}\r\n\r\n\r\n.form-check-label{\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n\r\n\r\n// activity widget\r\n\r\n.activity-wid{\r\n    margin-top: 8px;\r\n    margin-left: 16px;\r\n\r\n    .activity-list{\r\n        position: relative;\r\n        padding: 0 0 40px 30px;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            border-left: 2px dashed rgba($primary,0.25);\r\n            position: absolute;\r\n            left: 0;\r\n            bottom: 0;\r\n            top: 32px\r\n        }\r\n        .activity-icon{\r\n            position: absolute;\r\n            left: -15px;\r\n            top: 0;\r\n            z-index: 9;\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n}", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n  color: $gray-500;\r\n\r\n  i{\r\n    display: inline-block;\r\n    width: 40px;\r\n    height: 40px;\r\n    line-height: 36px;\r\n    font-size: 22px;\r\n    color: $gray-600;\r\n    border: 2px solid $gray-300;\r\n    border-radius: 4px;\r\n    transition: all 0.4s;\r\n    text-align: center;\r\n    margin-right: 16px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .col-lg-4 {\r\n    margin-top: 24px;\r\n\r\n    &:hover {\r\n      i {\r\n        color: $white;\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  display: block;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.fc-toolbar {\r\n  h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $light;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n      font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n.fc-unthemed{\r\n  .fc-content, \r\n  .fc-divider, \r\n  .fc-list-heading td, \r\n  .fc-list-view, \r\n  .fc-popover, \r\n  .fc-row, \r\n  tbody, \r\n  td, \r\n  th, \r\n  thead{\r\n      border-color: $light;\r\n  }\r\n  td.fc-today {\r\n      background: lighten($gray-200, 4%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $card-bg;\r\n  border-color: $border-color;\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n#external-events .external-event {\r\n  text-align: left!important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.fc-event, .fc-event-dot{\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content{\r\n  color: $white;\r\n}\r\n\r\n.fc {\r\n  .table-bordered {\r\n    td, th {\r\n      border-color: $table-group-separator-color;\r\n    }\r\n  }\r\n  \r\n  .fc-toolbar {\r\n    @media (max-width: 575.98px) {\r\n      display: block;\r\n    }\r\n    \r\n      h2 {\r\n          font-size: 16px;\r\n          line-height: 30px;\r\n          text-transform: uppercase;\r\n      }\r\n\r\n      @media (max-width: 767.98px) {\r\n\r\n          .fc-left,\r\n          .fc-right,\r\n          .fc-center {\r\n              float: none;\r\n              display: block;\r\n              text-align: center;\r\n              clear: both;\r\n              margin: 10px 0;\r\n          }\r\n\r\n          >*>* {\r\n              float: none;\r\n          }\r\n\r\n          .fc-today-button {\r\n              display: none;\r\n          }\r\n      }\r\n      \r\n      .btn {\r\n          text-transform: capitalize;\r\n      }\r\n\r\n  }\r\n}\r\n.fc-bootstrap .fc-today.alert-info{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  background-color: $black !important;\r\n}\r\n\r\n// RTL\r\n[dir=\"rtl\"] .fc-header-toolbar {\r\n  direction: ltr !important;\r\n}\r\n\r\n[dir=\"rtl\"] .fc-toolbar>*>:not(:first-child) {\r\n  margin-left: .75em;\r\n}\r\n\r\n\r\n\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid $border-color;\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: rtl;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "\r\n//\r\n// Round slider\r\n//\r\n\r\n.rs-control{\r\n  margin: 0px auto;\r\n}\r\n\r\n.rs-path-color{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.rs-bg-color{\r\n  background-color: $card-bg;\r\n}\r\n\r\n.rs-border{\r\n  border-color: transparent;\r\n}\r\n\r\n.rs-handle{\r\n  background-color: $gray-700;\r\n}\r\n\r\n.rs-circle-border{\r\n  .rs-border{\r\n    border: 8px solid $gray-300;\r\n  }\r\n}\r\n\r\n.rs-disabled{\r\n  opacity: 1;\r\n}\r\n\r\n// Outer border\r\n\r\n.outer-border {\r\n  .rs-border{\r\n    border-width: 0px;\r\n    &.rs-outer  {\r\n      border: 14px solid $gray-300;\r\n  }\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n  .rs-path-color{\r\n    background-color: transparent;\r\n  }\r\n}\r\n\r\n// Outer border dot\r\n\r\n.outer-border-dot {\r\n  .rs-border.rs-outer  {\r\n    border: 16px dotted;\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .rs-range-#{$color} {\r\n        .rs-range-color{\r\n          background-color: $value;\r\n        }\r\n\r\n        .rs-handle-dot{\r\n          background-color: lighten(($value), 24%);\r\n          border-color: $value;\r\n          &:after{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.rs-circle-border{\r\n          .rs-handle{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.outer-border-dot {\r\n          .rs-border.rs-outer  {\r\n            border-color: lighten(($value), 24%);\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n// rs-handle-arrow\r\n\r\n.rs-handle-arrow{\r\n    .rs-handle  {\r\n      background-color: transparent;\r\n      border: 8px solid transparent;\r\n      border-right-color:$gray-700;\r\n      margin: -6px 0px 0px 14px !important;\r\n      border-width: 6px 104px 6px 4px;\r\n      &:before  {\r\n        display: block;\r\n        content: \" \";\r\n        position: absolute;\r\n        height: 22px;\r\n        width: 22px;\r\n        background:$gray-700;\r\n        right: -11px;\r\n        bottom: -11px;\r\n        border-radius: 100px;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.rs-handle-arrow-dash{\r\n  .rs-handle  {\r\n    background-color: transparent;\r\n    border: 8px solid transparent;\r\n    border-right-color: $gray-700;\r\n    margin: -8px 0 0 14px !important;\r\n    &:before  {\r\n      display: block;\r\n      content: \" \";\r\n      position: absolute;\r\n      height: 12px;\r\n      width: 12px;\r\n      background: $gray-700;\r\n      right: -6px;\r\n      bottom: -6px;\r\n      border-radius: 100%;\r\n  }\r\n  &:after{\r\n    display: block;\r\n    content: \" \";\r\n    width: 80px;\r\n    position: absolute;\r\n    top: -1px;\r\n    right: 0px;\r\n    border-top: 2px dotted $gray-700\r\n  }\r\n}\r\n\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.irs--round {\r\n\r\n    .irs-bar,\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        background: $primary !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        &:before {\r\n            display: none;\r\n        }\r\n    }\r\n\r\n    .irs-line {\r\n        background: $gray-300;\r\n        border-color: $gray-300;\r\n    }\r\n\r\n    .irs-grid-text {\r\n        font-size: 11px;\r\n        color: $gray-500;\r\n    }\r\n\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-500;\r\n        background: $gray-300;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-handle {\r\n        border: 2px solid $primary;\r\n        width: 16px;\r\n        height: 16px;\r\n        top: 29px;\r\n        background-color: $card-bg !important;\r\n    }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 24px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-content{\r\n  font-size: 16px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n\r\n    &.toast-top-full-width, &.toast-bottom-full-width{\r\n        > div{\r\n          min-width: 96%;\r\n          margin: 4px auto;\r\n        }\r\n      }\r\n\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}\r\n\r\n.toastr-options{\r\n    padding: 24px;\r\n    background-color: lighten($gray-200, 2%);\r\n    margin-bottom: 0;\r\n    border: 1px solid $border-color;\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n  display: block;\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: $body-color;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid $gray-300;\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: $gray-200;\r\n      border: 1px solid $gray-300;\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: $gray-400;\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $gray-100;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}", "\r\n//\r\n// Bootstrap touchspin\r\n//\r\n\r\n\r\n.bootstrap-touchspin{\r\n    &.input-group{\r\n      &>.input-group-prepend{\r\n        &>.btn, &>.input-group-text{\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  \r\n    &.input-group{\r\n      &>.input-group-append{\r\n        &>.btn, &>.input-group-text{\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }", "//\r\n// datatable.scss\r\n\r\n\r\n.dataTables_wrapper {\r\n  &.container-fluid {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter {\r\n    text-align: right;\r\n\r\n    @media (max-width: 767px) {\r\n      text-align: center;\r\n    }\r\n\r\n\r\n    input {\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.datatable {\r\n  td {\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n  }\r\n}\r\n\r\ndiv.table-responsive>div.dataTables_wrapper>div.row>div[class^=\"col-\"] {\r\n  &:first-child {\r\n    padding-left: 0;\r\n  }\r\n\r\n  &:last-child {\r\n    padding-right: 0;\r\n  }\r\n}\r\n\r\n\r\ntable.dataTable {\r\n  border-collapse: collapse !important;\r\n  margin-bottom: 15px !important;\r\n\r\n  // Change icons view\r\n  thead {\r\n\r\n    .sorting,\r\n    .sorting_asc,\r\n    .sorting_desc,\r\n    .sorting_asc_disabled,\r\n    .sorting_desc_disabled {\r\n      &:before {\r\n        left: auto;\r\n        right: 0.5rem;\r\n        content: \"\\F0360\";\r\n        font-family: \"Material Design Icons\";\r\n        font-size: 1rem;\r\n        top: 9px;\r\n\r\n\r\n      }\r\n\r\n      &:after {\r\n        left: auto;\r\n        right: 0.5em;\r\n        content: \"\\F035D\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 15px;\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n\r\n    tr {\r\n\r\n      th,\r\n      td {\r\n\r\n        &.sorting_asc,\r\n        &.sorting_desc,\r\n        &.sorting {\r\n          padding-left: 12px;\r\n          padding-right: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  tbody {\r\n    // Multi select table\r\n\r\n    > tr.selected, >tr>.selected {\r\n        background-color: rgba($primary,.2);\r\n        \r\n        td {\r\n            border-color: rgba($primary,.2);\r\n            color: $primary;\r\n        }\r\n    }\r\n    td {\r\n        &:focus {\r\n            outline: none !important;\r\n        }\r\n    }\r\n    // Key Tables\r\n    th.focus,td.focus{\r\n        outline: 2px solid $primary !important;\r\n        outline-offset: -1px;\r\n        background-color: rgba($primary, 0.15);\r\n    }\r\n}\r\n}\r\n\r\n.dataTables_info {\r\n  font-weight: $font-weight-semibold;\r\n}\r\n\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n  >tbody {\r\n    >tr[role=row] {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            box-shadow: $box-shadow-lg;\r\n            background-color: $success;\r\n            bottom: auto;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    >tr.parent {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            background-color: $danger;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Data Table copy button\r\ndiv.dt-button-info {\r\n  background-color: $primary;\r\n  border: none;\r\n  color: $white;\r\n  box-shadow: none;\r\n  border-radius: 3px;\r\n  text-align: center;\r\n  z-index: 21;\r\n\r\n  h2 {\r\n    border-bottom: none;\r\n    background-color: rgba($white, 0.2);\r\n    color: $white;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n\r\n  li.paginate_button.previous,\r\n  li.paginate_button.next {\r\n    display: inline-block;\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  li.paginate_button {\r\n    display: none;\r\n  }\r\n\r\n  .dataTables_paginate {\r\n    ul {\r\n      text-align: center;\r\n      display: block;\r\n      margin: $spacer 0 0 !important;\r\n    }\r\n  }\r\n\r\n  div.dt-buttons {\r\n    display: inline-table;\r\n    margin-bottom: $spacer;\r\n  }\r\n}\r\n\r\n// Active status\r\n.activate-select {\r\n  .sorting_1 {\r\n    background-color: $gray-100;\r\n  }\r\n}\r\n\r\n\r\n\r\n// datatable\r\n\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\n\r\n.table,\r\ntable {\r\n  &.dataTable {\r\n    &.dtr-inline.collapsed>tbody>tr>td {\r\n      position: relative;\r\n\r\n      &.dtr-control {\r\n        padding-left: 30px;\r\n\r\n        &:before {\r\n          top: 64%;\r\n          left: 5px;\r\n          height: 14px;\r\n          width: 14px;\r\n          margin-top: -14px;\r\n          display: block;\r\n          position: absolute;\r\n          color: $white;\r\n          border: 2px solid $white;\r\n          border-radius: 14px;\r\n          box-sizing: content-box;\r\n          text-align: center;\r\n          text-indent: 0 !important;\r\n          line-height: 12px;\r\n          content: '+';\r\n          background-color: $primary;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce \r\n\r\n.tox-tinymce {\r\n    border: 2px solid $gray-300 !important;\r\n}\r\n\r\n.tox {\r\n    .tox-statusbar {\r\n        border-top: 1px solid $gray-300 !important;\r\n    }\r\n\r\n    .tox-menubar,\r\n    .tox-edit-area__iframe,\r\n    .tox-statusbar {\r\n        background-color: $card-bg !important;\r\n        background: none !important;\r\n    }\r\n\r\n    .tox-mbtn {\r\n        color: $gray-700 !important;\r\n\r\n        &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-tbtn {\r\n        &:hover {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar__primary {\r\n        border-color: $gray-300 !important;\r\n    }\r\n\r\n    .tox-toolbar,\r\n    .tox-toolbar__overflow,\r\n    .tox-toolbar__primary {\r\n        background: $gray-300 !important;\r\n    }\r\n\r\n    .tox-tbtn {\r\n        color: $gray-700 !important;\r\n\r\n        svg {\r\n            fill: $gray-700 !important;\r\n        }\r\n    }\r\n\r\n    .tox-edit-area__iframe {\r\n        background-color: $card-bg !important;\r\n    }\r\n\r\n    .tox-statusbar a,\r\n    .tox-statusbar__path-item,\r\n    .tox-statusbar__wordcount {\r\n        color: $gray-700 !important;\r\n    }\r\n\r\n    &:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\r\n        border-right: 1px solid darken($gray-300, 5%) !important;\r\n    }\r\n}\r\n.tox-tinymce-aux {\r\n    z-index: 1050 !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed $gray-400;\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard {\r\n\r\n    .twitter-bs-wizard-nav {\r\n        position: relative;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            width: 100%;\r\n            height: 2px;\r\n            background-color: $gray-300;\r\n            position: absolute;\r\n            left: 0;\r\n            top: 26px;\r\n        }\r\n\r\n        .step-number {\r\n            display: inline-block;\r\n            width: 38px;\r\n            height: 38px;\r\n            line-height: 34px;\r\n            border: 2px solid $primary;\r\n            color: $primary;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n            position: relative;\r\n            background-color: $card-bg;\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n                margin: 0 auto 8px !important;\r\n            }\r\n        }\r\n\r\n        .nav-link {\r\n            .step-title {\r\n                display: block;\r\n                margin-top: 8px;\r\n                font-weight: $font-weight-bold;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: $gray-700;\r\n\r\n                .step-number {\r\n                    background-color: $primary;\r\n                    color: $white;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        padding-top: 24px;\r\n        padding-left: 0;\r\n        list-style: none;\r\n        margin-bottom: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n\r\n            a {\r\n                display: inline-block;\r\n                padding: .47rem .75rem;\r\n                background-color: $primary;\r\n                color: $white;\r\n                border-radius: .25rem;\r\n            }\r\n\r\n            &.disabled {\r\n                a {\r\n                    cursor: not-allowed;\r\n                    background-color: lighten($primary, 8%);\r\n                }\r\n            }\r\n\r\n            &.next {\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content {\r\n    padding-top: 24px;\r\n    min-height: 262px;\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $light;\r\n      color: $dark;\r\n      border: 1px solid darken($light, 2%);\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 50px !important;;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}", "\r\n\r\n/* Flot chart */\r\n.flot-charts-height {\r\n  height: 320px;\r\n}\r\n\r\n.flotTip {\r\n  padding: 8px 12px;\r\n  background-color: rgba($dark, 0.9);\r\n  z-index: 100;\r\n  color: $gray-100;\r\n  box-shadow: $box-shadow;\r\n  border-radius: 4px;\r\n}\r\n\r\n.legendLabel{\r\n  color: $gray-500;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// x editable.scss\r\n//\r\n\r\n.editable-input{\r\n    .form-control{\r\n      display: inline-block;\r\n    }\r\n  }\r\n  \r\n  .editable-buttons{\r\n    margin-left: 7px;\r\n    .editable-cancel{\r\n      margin-left: 7px;\r\n    }\r\n  }", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: $dark;\r\n    font-weight: 600;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: $gray-300;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: $gray-300;\r\n    font-weight: 500;\r\n    color: darken($dark,5%);\r\n      a{\r\n        color: darken($dark,5%);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "/* ==============\r\n  Timeline\r\n===================*/\r\n\r\n.cd-container {\r\n    width: 90%;\r\n    max-width: 1170px;\r\n    margin: 0 auto;\r\n  }\r\n  .cd-container::after {\r\n    content: '';\r\n    display: table;\r\n    clear: both;\r\n  }\r\n  #cd-timeline {\r\n    margin-bottom: 2em;\r\n    margin-top: 2em;\r\n    padding: 2em 0;\r\n    position: relative;\r\n    &::before {\r\n      border-left: 3px solid $light;\r\n      content: '';\r\n      height: 100%;\r\n      left: 18px;\r\n      position: absolute;\r\n      top: 0;\r\n      width: 3px;\r\n    }\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    #cd-timeline {\r\n      margin-bottom: 3em;\r\n      margin-top: 3em;\r\n      &::before {\r\n        left: 50%;\r\n        margin-left: -2px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .cd-timeline-block {\r\n    margin: 2em 0;\r\n    position: relative;\r\n    &:after {\r\n      clear: both;\r\n      content: \"\";\r\n      display: table;\r\n    }\r\n  }\r\n  .cd-timeline-block:first-child {\r\n    margin-top: 0;\r\n  }\r\n  .cd-timeline-block:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-block {\r\n      margin: 4em 0;\r\n    }\r\n    .cd-timeline-block:first-child {\r\n      margin-top: 0;\r\n    }\r\n    .cd-timeline-block:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  .cd-timeline-img {\r\n    position: absolute;\r\n    top: 20px;\r\n    left: 0;\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    font-size: 20px;\r\n    color: $white;\r\n    background-color: $primary;\r\n    border: 5px solid $white;\r\n  \r\n    i {\r\n      margin-left: 1px;\r\n    }\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-img {\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 30px;\r\n      left: 50%;\r\n      margin-left: -20px;\r\n    }\r\n    .cssanimations .cd-timeline-img.is-hidden {\r\n      visibility: hidden;\r\n    }\r\n    .cssanimations .cd-timeline-img.bounce-in {\r\n      visibility: visible;\r\n      -webkit-animation: cd-bounce-1 0.6s;\r\n      -moz-animation: cd-bounce-1 0.6s;\r\n      animation: cd-bounce-1 0.6s;\r\n    }\r\n  }\r\n  \r\n  .cd-timeline-content {\r\n    border-radius: 5px;\r\n    border: 1px solid $light;\r\n    margin-left: 60px;\r\n    padding: 1em;\r\n    position: relative;\r\n  \r\n    &:after {\r\n      clear: both;\r\n      content: \"\";\r\n      display: table;\r\n    }\r\n    h2 {\r\n      margin-top: 0;\r\n    }\r\n    .cd-read-more {\r\n      background: $primary;\r\n      border-radius: 0.25em;\r\n      color: white;\r\n      display: inline-block;\r\n      float: right;\r\n      font-size: 14px;\r\n      padding: .8em 1em;\r\n    }\r\n    .cd-date {\r\n      display: inline-block;\r\n      font-size: 14px;\r\n    }\r\n    h3 {\r\n      font-size: 18px;\r\n      margin: 0 0 15px 0;\r\n    }\r\n  }\r\n  \r\n  .no-touch .cd-timeline-content .cd-read-more:hover {\r\n    background-color: #bac4cb;\r\n  }\r\n  .cd-timeline-content .cd-date {\r\n    float: left;\r\n    padding: .8em 0;\r\n    opacity: .7;\r\n  }\r\n  .cd-timeline-content::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 16px;\r\n    right: 100%;\r\n    height: 0;\r\n    width: 0;\r\n    border: 12px solid transparent;\r\n    border-right: 12px solid $light;\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-content {\r\n      margin-left: 0;\r\n      padding: 1.6em;\r\n      width: 45%;\r\n    }\r\n    .cd-timeline-content::before {\r\n      top: 24px;\r\n      left: 100%;\r\n      border-color: transparent;\r\n      border-left-color: $light;\r\n    }\r\n    .cd-timeline-content .cd-read-more {\r\n      float: left;\r\n    }\r\n    .cd-timeline-content .cd-date {\r\n      position: absolute;\r\n      width: 100%;\r\n      left: 122%;\r\n      top: 20px;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content {\r\n      float: right;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content::before {\r\n      top: 24px;\r\n      left: auto;\r\n      right: 100%;\r\n      border-color: transparent;\r\n      border-right-color: $light;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-read-more {\r\n      float: right;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-date {\r\n      left: auto;\r\n      right: 122%;\r\n      text-align: right;\r\n    }\r\n    .cssanimations .cd-timeline-content.is-hidden {\r\n      visibility: hidden;\r\n    }\r\n    .cssanimations .cd-timeline-content.bounce-in {\r\n      visibility: visible;\r\n      -webkit-animation: cd-bounce-2 0.6s;\r\n      -moz-animation: cd-bounce-2 0.6s;\r\n      animation: cd-bounce-2 0.6s;\r\n    }\r\n  }\r\n  \r\n  @media only screen and (min-width: 1170px) {\r\n    .cssanimations .cd-timeline-block:nth-child(even) .cd-timeline-content.bounce-in {\r\n      -webkit-animation: cd-bounce-2-inverse 0.6s;\r\n      -moz-animation: cd-bounce-2-inverse 0.6s;\r\n      animation: cd-bounce-2-inverse 0.6s;\r\n    }\r\n  }", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// Directory page\r\n\r\n.social-links {\r\n    li {\r\n      a {\r\n        background: lighten($light,4%);\r\n        border-radius: 50%;\r\n        color: lighten($dark,15%);\r\n        display: inline-block;\r\n        height: 30px;\r\n        line-height: 30px;\r\n        text-align: center;\r\n        width: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Authentication\r\n  .auth-body-bg{\r\n    background-image: url(../images/auth-bg.jpg);\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    .bg-overlay {\r\n      background: rgba(51, 51, 51, 0.97);\r\n      width: 100%;\r\n      height: 100%;\r\n      position: absolute;\r\n      right: 0;\r\n      bottom: 0;\r\n      left: 0px;\r\n      top: 0px;\r\n    }\r\n  }\r\n\r\n  \r\n\r\n  .wrapper-page {\r\n    margin: 7.5% auto;\r\n    max-width: 460px;\r\n    position: relative;\r\n  \r\n    // .logo-admin {\r\n    //   font-size: 28px;\r\n    //   line-height: 70px;\r\n    // }\r\n    .auth-logo{\r\n      font-size: 28px;\r\n      line-height: 70px;\r\n      &.logo-light{\r\n        display: $display-none;\r\n      }\r\n      &.logo-dark {\r\n        display: $display-block;\r\n      }\r\n    }\r\n  }\r\n  // Error Page\r\n\r\n  .ex-page-content {\r\n    h1 {\r\n      font-size: 98px;\r\n      font-weight: 700;\r\n      line-height: 150px;\r\n      text-shadow: rgba(61, 61, 61, 0.3) 1px 1px, rgba(61, 61, 61, 0.2) 2px 2px, rgba(61, 61, 61, 0.3) 3px 3px;\r\n    }\r\n  }\r\n  \r\n"]}