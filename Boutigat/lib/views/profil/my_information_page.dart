import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UpdateProfileController extends GetxController {
  var isLoading = false.obs;
  var showErrorMessage = "".obs;

  final AuthController authController = Get.find<AuthController>();


  bool validateProfile(String firstName, String lastName) {
    if (firstName.isEmpty || lastName.isEmpty) {
      showErrorMessage("Les champs prénom et nom ne peuvent pas être vides.");
      return false;
    }
    return true;
  }

  void updateProfile(String firstName, String lastName) async {
    isLoading(true);
    showErrorMessage("");

    if (!validateProfile(firstName, lastName)) {
      isLoading(false);
      return;
    }

    try {
      bool success = await AuthService.updateProfile(
        firstName: firstName,
        lastName: lastName,
      );

      if (success) {
        Get.back();

        await authController.getAndSaveUser();

        // Get.snakbar("Succès", "Les informations du profil ont été mises à jour avec succès.");
      } else {
        showErrorMessage("Erreur lors de la mise à jour du profil.");
      }
    } catch (e) {
      showErrorMessage("Erreur lors de la mise à jour du profil: ${e.toString()}");
    } finally {
      isLoading(false);
    }
  }
}

class MyInformationPage extends StatelessWidget {
  final String firstName;
  final String lastName;

  MyInformationPage({Key? key, required this.firstName, required this.lastName}) : super(key: key);

  final UpdateProfileController updateProfileController = Get.put(UpdateProfileController());

  @override
  Widget build(BuildContext context) {
    TextEditingController firstNameController = TextEditingController(text: firstName);
    TextEditingController lastNameController = TextEditingController(text: lastName);

    return Scaffold(
      appBar: AppBar(
        title: Text("user_name".tr),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              height: 80,
              child: TextField(
                controller: firstNameController,
                decoration: InputDecoration(
                  labelText: "first_name".tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
            ),
            SizedBox(height: 16.0),
            Container(
              height: 80,
              child: TextField(
                controller: lastNameController,
                decoration: InputDecoration(
                  labelText: "last_name".tr,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
            ),
            SizedBox(height: 16.0),
            Obx(() {
              return updateProfileController.isLoading.value
                  ? CircularProgressIndicator()
                  : Container(
                      width: 380,
                      child: ElevatedButton(
                        onPressed: () {
                          updateProfileController.updateProfile(
                            firstNameController.text,
                            lastNameController.text,
                          );
                        },
                        child: Text("save".tr),
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Theme.of(context).colorScheme.onPrimary,
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          textStyle: const TextStyle(fontSize: 16),
                        ),
                      ),
                    );
            }),
            SizedBox(height: 16.0),
            Obx(() {
              return updateProfileController.showErrorMessage.value.isNotEmpty
                  ? Text(
                      updateProfileController.showErrorMessage.value,
                      style: TextStyle(color: Colors.red),
                    )
                  : Container();
            }),
          ],
        ),
      ),
    );
  }
}
