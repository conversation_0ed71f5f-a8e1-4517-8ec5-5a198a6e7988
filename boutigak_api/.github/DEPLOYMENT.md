# 🚀 GitHub Actions Deployment Guide

This repository includes automated deployment to two production servers using GitHub Actions.

## 📋 Server Configuration

The deployment workflow is configured for the following servers:

### App Server 1
- **Host**: **************
- **Path**: /var/www/html/boutigak_api
- **User**: root
- **Port**: 22

### App Server 2
- **Host**: ************
- **Path**: /var/www/boutigak_api
- **User**: root
- **Port**: 22

## 🔧 Setup Instructions

### 1. Configure GitHub Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions, and add the following secret:

```
SERVER_PASSWORD = Boutigak@36666688@38407840
```

### 2. Workflow Triggers

The deployment workflow triggers on:
- Push to `main` or `master` branch
- Manual trigger via GitHub Actions UI

### 3. Deployment Process

The workflow performs the following steps:

#### Testing Phase
1. Sets up PHP 8.1 with required extensions
2. Installs Composer dependencies
3. Runs PHPUnit tests
4. Only proceeds to deployment if tests pass

#### Deployment Phase
1. Builds production assets with npm
2. Creates deployment archive (excludes development files)
3. Deploys to both servers simultaneously using matrix strategy
4. Creates backup of current deployment
5. Uploads and extracts new code
6. Configures environment variables
7. Runs Laravel optimization commands
8. Restarts web services

## 📁 Deployment Structure

### Files Excluded from Deployment
- `.git/`
- `.github/`
- `node_modules/`
- `.env` (created from .env.example)
- `storage/logs/*`
- `storage/framework/cache/*`
- `storage/framework/sessions/*`
- `storage/framework/views/*`
- `tests/`
- `*.md` files

### Laravel Commands Executed
```bash
php artisan key:generate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan migrate --force
php artisan storage:link
php artisan cache:clear
```

## 🔒 Security Features

1. **Backup Creation**: Automatic backup before each deployment
2. **Environment Configuration**: Production-specific settings
3. **Permission Management**: Proper file ownership and permissions
4. **Service Restart**: Automatic web server reload

## 🛠️ Environment Configuration

The deployment automatically configures the following production settings:

```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://boutigak.com
DB_HOST=************
DB_PORT=5432
DB_DATABASE=boutigak_db
DB_USERNAME=postgress
DB_PASSWORD=adminboutigak
```

## 📊 Monitoring Deployment

### View Deployment Status
1. Go to GitHub repository → Actions tab
2. Click on the latest workflow run
3. Monitor progress for both servers

### Deployment Logs
Each deployment step is logged with detailed output for troubleshooting.

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify server credentials in GitHub Secrets
   - Check server accessibility

2. **Permission Errors**
   - Ensure www-data user exists on servers
   - Verify directory permissions

3. **Laravel Commands Failed**
   - Check PHP version compatibility
   - Verify database connectivity
   - Ensure all required extensions are installed

### Manual Deployment

If automatic deployment fails, you can manually deploy using:

```bash
# On each server
cd /var/www/html/boutigak_api  # or /var/www/boutigak_api for server 2
git pull origin main
composer install --no-dev --optimize-autoloader
npm ci && npm run build
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
sudo systemctl reload apache2
```

## 🔄 Rollback Procedure

If deployment fails, automatic backups are created:

```bash
# On server, restore from backup
cd /var/www/html  # or /var/www for server 2
rm -rf boutigak_api
mv boutigak_api_backup_YYYYMMDD_HHMMSS boutigak_api
sudo systemctl reload apache2
```

## 📝 Customization

### Modify Server Configuration
Edit `.github/workflows/deploy.yml` and update the matrix strategy:

```yaml
strategy:
  matrix:
    server:
      - name: "Your Server Name"
        host: "your.server.ip"
        path: "/your/deployment/path"
```

### Add Environment Variables
Update the deployment script section to include additional environment variables:

```bash
sed -i 's/YOUR_VAR=.*/YOUR_VAR=production_value/' .env
```

### Custom Laravel Commands
Add additional commands in the deployment script:

```bash
php artisan your:custom:command
```

## 🎯 Best Practices

1. **Test Locally**: Always test changes locally before pushing
2. **Database Migrations**: Use migration files for database changes
3. **Environment Variables**: Never commit sensitive data to repository
4. **Monitoring**: Monitor deployment logs for any issues
5. **Backup Strategy**: Regular database backups outside of deployment process

## 🎛️ Available Workflows

### 1. Automatic Deployment (`deploy.yml`)
- **Trigger**: Push to main/master branch
- **Features**: Full testing, builds assets, deploys to both servers
- **Use case**: Regular deployments

### 2. Manual Deployment (`manual-deploy.yml`)
- **Trigger**: Manual via GitHub Actions UI
- **Features**: Configurable options, emergency deployment
- **Options**:
  - Target servers (both/server1/server2)
  - Skip tests (for emergencies)
  - Run migrations (optional)

### 3. Emergency Rollback (`rollback.yml`)
- **Trigger**: Manual via GitHub Actions UI
- **Features**: Quick rollback to previous backup
- **Options**:
  - Target servers
  - Specific backup timestamp
  - Confirmation required

### 4. Manual Deployment Script (`deploy.sh`)
- **Trigger**: Local command line
- **Features**: Direct deployment from local machine
- **Usage**: `./deploy.sh [server1|server2|both]`

## 🚀 Quick Start Guide

### Step 1: Initial Setup
```bash
# 1. Add GitHub Secret
# Go to: Repository → Settings → Secrets → Actions
# Add: SERVER_PASSWORD = Boutigak@36666688@38407840

# 2. Make deployment script executable
chmod +x deploy.sh

# 3. Test local deployment (requires sshpass)
./deploy.sh --help
```

### Step 2: First Deployment
```bash
# Option A: Push to main branch (automatic)
git push origin main

# Option B: Manual deployment via GitHub Actions
# Go to: Actions → Manual Deployment → Run workflow

# Option C: Local deployment
./deploy.sh both
```

## 🛠️ Advanced Usage

### Manual Deployment Options
1. **Standard Deployment**
   - Target: Both servers
   - Tests: Enabled
   - Migrations: Enabled

2. **Emergency Deployment**
   - Target: Specific server
   - Tests: Disabled
   - Migrations: Disabled

3. **Migration-only Deployment**
   - Target: Server 1 only
   - Tests: Enabled
   - Migrations: Enabled

### Rollback Scenarios
1. **Latest Backup**: Leave timestamp empty
2. **Specific Backup**: Use format `YYYYMMDD_HHMMSS`
3. **Partial Rollback**: Target specific server

## 📊 Monitoring & Logs

### GitHub Actions Logs
- Real-time deployment progress
- Detailed error messages
- Step-by-step execution

### Server Logs
```bash
# Laravel application logs
tail -f /var/www/html/boutigak_api/storage/logs/laravel.log

# Web server logs
tail -f /var/log/apache2/error.log
tail -f /var/log/nginx/error.log

# PHP-FPM logs
tail -f /var/log/php8.1-fpm.log
```

### Health Checks
```bash
# Check Laravel status
php artisan --version
php artisan route:list
php artisan config:show

# Check database connectivity
php artisan migrate:status

# Check queue status
php artisan queue:work --once
```

## 📞 Support

For deployment issues:
1. Check GitHub Actions logs
2. Verify server accessibility
3. Review Laravel logs on servers: `storage/logs/laravel.log`
4. Use rollback workflow if needed
5. Contact system administrator for critical issues
