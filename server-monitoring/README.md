# Server Monitoring Dashboard

A comprehensive real-time monitoring dashboard for your server infrastructure, designed to monitor:
- 1 Database/Storage Server (************)
- 2 Laravel Application Servers

## Features

### 📊 Real-time Monitoring
- **System Metrics**: CPU, RAM, Disk usage
- **Server Status**: Online/Offline status with health checks
- **Database KPIs**: Active connections, queries/sec, slow queries, database size
- **Laravel Metrics**: Active users, sessions, queue jobs, cache stats
- **Performance**: Response times, error rates, uptime

### 🎯 Key Performance Indicators
- Active concurrent users
- Total daily sessions
- Requests per minute
- Average response time
- Error rate monitoring
- Queue job status
- Cache hit rates

### 📈 Visual Dashboard
- Real-time charts and graphs
- Progress bars for resource usage
- Color-coded status indicators
- Responsive design for mobile/desktop
- Auto-refresh every 30 seconds

## Installation

### Option 1: Docker Installation (Recommended)

#### Prerequisites
- Docker and Docker Compose installed
- Access to PostgreSQL database (************:5432)

#### Quick Start with Docker
```bash
# Navigate to the project directory
cd server-monitoring

# Build and start the containers
docker-compose up -d

# Access the dashboard
# Main Dashboard: http://localhost:8080
# Debug Console: http://localhost:8080/debug.php
```

#### Docker Services
- **monitoring-app**: Main PHP/Apache application (port 8080)
- **redis**: Redis cache server (port 6379)

#### Docker Management
```bash
# View container status
docker-compose ps

# View logs
docker-compose logs -f monitoring-app

# Stop services
docker-compose down

# Rebuild containers
docker-compose build --no-cache
docker-compose up -d
```

### Option 2: Traditional Installation

#### Prerequisites
- PHP 7.4 or higher
- Web server (Apache/Nginx)
- SSH access to all servers
- PostgreSQL access

### Required PHP Extensions
```bash
# Install required extensions
sudo apt-get install php-ssh2 php-pdo php-pgsql

# Or on CentOS/RHEL
sudo yum install php-ssh2 php-pdo php-pgsql
```

### Setup Steps

1. **Clone/Download the monitoring system**
   ```bash
   # Place the server-monitoring folder in your web directory
   cp -r server-monitoring /var/www/html/
   ```

2. **Configure Server Details**
   Edit `config/servers.php` with your actual server information:
   ```php
   'database_server' => [
       'host' => '************', // Your database server IP
       'username' => 'root',
       'password' => 'Boutigak@36666688@38407840',
       'db_name' => 'your_database_name',
       // ... other settings
   ],
   ```

3. **Set Permissions**
   ```bash
   chmod 755 /var/www/html/server-monitoring
   chmod 644 /var/www/html/server-monitoring/config/servers.php
   ```

4. **Configure Web Server**

   **For Nginx:**
   ```nginx
   server {
       listen 80;
       server_name monitoring.yourdomain.com;
       root /var/www/html/server-monitoring;
       index index.html;

       location /api/ {
           try_files $uri $uri/ =404;
           location ~ \.php$ {
               fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
               fastcgi_index index.php;
               include fastcgi_params;
               fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
           }
       }
   }
   ```

5. **Test PostgreSQL Connection**
   ```bash
   # Test the database connection
   php server-monitoring/test-postgres.php
   ```

6. **Test the Installation**
   - Open your browser and navigate to `http://your-server/server-monitoring/`
   - Check that the dashboard loads and shows data
   - Verify API endpoints: `http://your-server/server-monitoring/api/overview.php`

## Configuration

### Server Configuration
Update the following files with your server details:

1. **config/servers.php** - Main configuration file
2. **api/overview.php** - Update database connection details
3. **api/database.php** - Update SSH and database credentials
4. **api/app-server-1.php** - Update App Server 1 details
5. **api/app-server-2.php** - Update App Server 2 details

### Security Considerations

1. **SSH Key Authentication (Recommended)**
   Replace password authentication with SSH keys:
   ```php
   // Instead of password, use key-based auth
   ssh2_auth_pubkey_file($connection, $username, $public_key, $private_key);
   ```

2. **Restrict Access**
   Add IP restrictions or authentication to the monitoring dashboard:
   ```nginx
   # In your Nginx config
   allow ***********/24;  # Your office network
   deny all;
   ```

3. **HTTPS Setup**
   Configure SSL/TLS for secure access:
   ```nginx
   listen 443 ssl;
   ssl_certificate /path/to/certificate.crt;
   ssl_certificate_key /path/to/private.key;
   ```

## API Endpoints

The monitoring system provides the following API endpoints:

- `GET /api/overview.php` - Overall system metrics
- `GET /api/database.php` - Database server metrics
- `GET /api/app-server-1.php` - App Server 1 metrics
- `GET /api/app-server-2.php` - App Server 2 metrics

### Example API Response
```json
{
    "status": "online",
    "cpu": 45.2,
    "ram": 67.8,
    "disk": 34.1,
    "activeUsers": 156,
    "requestsPerMin": 87,
    "timestamp": 1640995200
}
```

## Customization

### Adding New Metrics
1. Update the relevant API file (e.g., `api/database.php`)
2. Add the new metric collection method
3. Update the frontend JavaScript in `js/dashboard.js`
4. Add UI elements in `index.html`

### Changing Refresh Interval
Edit `js/dashboard.js`:
```javascript
// Change from 30 seconds to 60 seconds
refreshInterval = setInterval(refreshData, 60000);
```

### Alert Thresholds
Update `config/servers.php`:
```php
'alert_thresholds' => [
    'cpu_warning' => 70,    // CPU usage warning at 70%
    'cpu_critical' => 85,   // CPU usage critical at 85%
    // ... other thresholds
]
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify SSH credentials in config files
   - Check if SSH service is running on target servers
   - Ensure firewall allows SSH connections

2. **Database Connection Failed**
   - Verify database credentials
   - Check if MySQL service is running
   - Ensure database user has proper permissions

3. **API Returns Mock Data**
   - Check PHP error logs: `tail -f /var/log/php/error.log`
   - Verify SSH2 extension is installed: `php -m | grep ssh2`
   - Test SSH connection manually

4. **Dashboard Not Loading**
   - Check web server error logs
   - Verify file permissions
   - Ensure all required files are present

### Debug Mode
Enable debug mode by adding to API files:
```php
// Add at the top of API files for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Performance Optimization

1. **Caching**: Implement Redis caching for frequently accessed metrics
2. **Database Indexing**: Ensure proper indexes on session and log tables
3. **Log Rotation**: Set up log rotation to prevent disk space issues
4. **Connection Pooling**: Use persistent connections for database queries

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review PHP and web server error logs
3. Verify all configuration settings
4. Test individual API endpoints

## License

This monitoring system is provided as-is for monitoring your server infrastructure.
