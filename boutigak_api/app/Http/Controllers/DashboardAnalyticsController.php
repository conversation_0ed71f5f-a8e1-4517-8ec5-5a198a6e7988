<?php

namespace App\Http\Controllers;

use App\services\AnalyticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DashboardAnalyticsController extends Controller
{
    protected AnalyticsService $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get daily active users data for chart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDailyActiveUsers(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $data = $this->analyticsService->getDailyActiveUsers($days);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get current active users count
     *
     * @return JsonResponse
     */
    public function getCurrentActiveUsers(): JsonResponse
    {
        $count = $this->analyticsService->getCurrentActiveUsers();

        return response()->json([
            'success' => true,
            'active_users' => $count
        ]);
    }

    /**
     * Get active users statistics
     *
     * @return JsonResponse
     */
    public function getActiveUsersStats(): JsonResponse
    {
        $stats = $this->analyticsService->getActiveUsersStats();

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Get user activity summary
     *
     * @return JsonResponse
     */
    public function getUserActivitySummary(): JsonResponse
    {
        $summary = $this->analyticsService->getUserActivitySummary();

        return response()->json([
            'success' => true,
            'summary' => $summary
        ]);
    }

    /**
     * Get hourly active users for today
     *
     * @return JsonResponse
     */
    public function getHourlyActiveUsers(): JsonResponse
    {
        $data = $this->analyticsService->getHourlyActiveUsers();

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get comprehensive dashboard analytics
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDashboardAnalytics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);

        $data = [
            'daily_active_users' => $this->analyticsService->getDailyActiveUsers($days),
            'current_active_users' => $this->analyticsService->getCurrentActiveUsers(),
            'active_users_stats' => $this->analyticsService->getActiveUsersStats(),
            'user_activity_summary' => $this->analyticsService->getUserActivitySummary(),
            'hourly_active_users' => $this->analyticsService->getHourlyActiveUsers(),
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
