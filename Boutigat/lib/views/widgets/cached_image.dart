import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'dart:io';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image/image.dart' as img;




import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:image/image.dart' as img;

class CachedImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  const CachedImageWidget({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
  }) : super(key: key);

  @override
  State<CachedImageWidget> createState() => _CachedImageWidgetState();
}

class _CachedImageWidgetState extends State<CachedImageWidget> with AutomaticKeepAliveClientMixin {
  File? _file;
  bool _loading = true;
  bool _hasError = false;
  bool _hasTransparentBackground = false;

  static final Map<String, File> _fileCache = {};
  static final Map<String, bool> _transparencyCache = {};

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(covariant CachedImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    setState(() {
      _loading = true;
      _hasError = false;
    });

    try {
      File file;

      if (_fileCache.containsKey(widget.imageUrl)) {
        file = _fileCache[widget.imageUrl]!;
      } else {
        file = await DefaultCacheManager().getSingleFile(widget.imageUrl);
        _fileCache[widget.imageUrl] = file;
      }

      bool hasTransparency = false;

      if (_transparencyCache.containsKey(widget.imageUrl)) {
        hasTransparency = _transparencyCache[widget.imageUrl]!;
      } else {
        final bytes = await file.readAsBytes();
        final image = img.decodeImage(bytes);
        if (image != null && image.numChannels == 4) {
          final pixels = image.getBytes();
          for (int i = 3; i < pixels.length; i += 40) { // 💡 encore plus léger
            if (pixels[i] < 255) {
              hasTransparency = true;
              break;
            }
          }
        }
        _transparencyCache[widget.imageUrl] = hasTransparency;
      }

      if (mounted) {
        setState(() {
          _file = file;
          _loading = false;
          _hasTransparentBackground = hasTransparency;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _loading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final double width = widget.width ?? 100;
    final double height = widget.height ?? 100;

    if (_loading) return _buildPlaceholder(width, height);
    if (_hasError || _file == null) return _buildError(width, height);

    final imageWidget = Image.file(
      _file!,
      width: width,
      height: height,
      fit: _hasTransparentBackground ? BoxFit.contain : widget.fit,
    );

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      child: Container(
        width: width,
        height: height,
        color: _hasTransparentBackground ? Colors.grey[200] : null,
        alignment: Alignment.center,
        child: imageWidget,
      ),
    );
  }

  Widget _buildPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      ),
      child: Center(
        child: Image.asset(
          'assets/images/logo.png',
          width: width * 0.4,
          height: height * 0.4,
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildError(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
      ),
      child: const Icon(Icons.error),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

