import 'dart:convert';
import 'package:get/get.dart';
import 'package:boutigak/data/models/categories.dart';

import 'package:boutigak/data/services/webservices.dart';

class CategoryService {
  static Future<List<Category>?> fetchCategories() async {
    var response =  await WebService.get(AvailableServices.categories ,);

    if (response.statusCode == 200) {
      List<dynamic> body = jsonDecode(response.body);


    //     print('Response status code: categories ================= ${response.statusCode}');
    // print('response ${response.body}');

      
      List<Category> categories = body.map((dynamic item) => Category.fromJson(item)).toList();
      return categories;
    } else {
    //  // Get.snakbar("Error", "Failed to load categories");
      return null;
    }
  }
}