<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item', function (Blueprint $table) {
            $table->decimal('promotion_percentage', 5, 2)->nullable()->after('rejection_reason');
            $table->boolean('has_promotion')->default(false)->after('promotion_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item', function (Blueprint $table) {
            $table->dropColumn('promotion_percentage');
            $table->dropColumn('has_promotion');
        });
    }
};
