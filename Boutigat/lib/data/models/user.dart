// Base class for all users
class User {
  int? id;
  String firstName;
  String lastName;
  String phoneNumber;
  String gender;
  String? invitationcode;
  String? password;
  bool? hasStore;
  bool? hasSubscription;
  DateTime? subscriptionsStartDate;
  DateTime? subscriptionsEndDate;
  String? lang;

  User({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.invitationcode,
    required this.phoneNumber,
    required this.gender,
    required this.password,
    this.hasStore,
    this.hasSubscription,
    this.subscriptionsStartDate,
    this.subscriptionsEndDate,
    this.lang
  });

 
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      "firstname": firstName,
      "lastname": lastName,
      "invitationcode": invitationcode,
      "phone": phoneNumber,
      "gender": gender,
      "password": password,
      "has_store": hasStore,
      "has_subscription": hasSubscription,
      "subscriptions_start_date": subscriptionsStartDate?.toIso8601String(),
      "subscriptions_end_date": subscriptionsEndDate?.toIso8601String(),
    };
  }

  
 factory User.fromJson(Map<String, dynamic> json) {


  return User(
    id: json['id'],
    firstName: json['firstname'],
    lastName: json['lastname'],
    invitationcode: json['invitationcode'],
    phoneNumber: json['phone'],
    gender: json['gender'],
    password: null, 
    hasStore: json['has_store'] ?? false,
    hasSubscription: json.containsKey('has_subscription') 
                      ? json['has_subscription'] 
                      : (json['subscriptions'] != null && json['subscriptions'].isNotEmpty),
    subscriptionsStartDate: json['subscriptions'] != null && json['subscriptions'].isNotEmpty
        ? DateTime.tryParse(json['subscriptions'][0]['start_date'])
        : null,
    subscriptionsEndDate: json['subscriptions'] != null && json['subscriptions'].isNotEmpty
        ? DateTime.tryParse(json['subscriptions'][0]['end_date'])
        : null,
    lang: json['lang'] ?? 'en',
  );
}

}
