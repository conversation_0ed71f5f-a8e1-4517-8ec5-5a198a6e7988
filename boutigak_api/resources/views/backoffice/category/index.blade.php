@extends('backoffice.layouts.layout')

@section('title', 'Categories | ' . config('app.name'))

@section('content')
    <div class="page-content">
        <div class="container-fluid">
            @include('backoffice.partials.messages')
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="card-title">Categories</h4>
                            <div class="card-tools">
                                <a href="{{ route('categories.create') }}" class="btn btn-primary mx-1">Add Category</a>
                                <a href="{{ route('import.categories.view') }}" class="btn btn-secondary mx-1">Import Categories</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="categories-table" class="table dt-responsive nowrap w-100">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title (EN)</th>
                                        <th>Title (AR)</th>
                                        <th>Title (FR)</th>
                                        <th>Parent</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            const table = $('#categories-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('categories.index') }}",
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'title_en', name: 'title_en'},
                    {data: 'title_ar', name: 'title_ar'},
                    {data: 'title_fr', name: 'title_fr'},
                    {data: 'parent', name: 'parent'},
                    {data: 'actions', name: 'actions', orderable: false, searchable: false}
                ]
            });

            $(document).on('click', '.delete', function() {
                let id = $(this).data('id');
                Swal.fire({
                    title: 'Êtes-vous sûr ?',
                    text: "Cette action est irréversible !",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Oui, supprimer !'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('categories.destroy', '') }}/" + id,
                            method: 'DELETE',
                            data: {
                                _token: "{{ csrf_token() }}"
                            },
                            success: function(response) {
                                table.ajax.reload();
                                Swal.fire('Supprimé !', 'La catégorie a été supprimée.', 'success');
                            },
                            error: function(xhr) {
                                Swal.fire('Erreur !', 'Une erreur est survenue : ' + xhr.responseText, 'error');
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
