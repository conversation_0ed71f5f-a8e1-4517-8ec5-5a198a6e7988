class Badge {
  final int id;
  final int userId;
  final String module;
  final int count;
  final DateTime? lastReadAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  Badge({
    required this.id,
    required this.userId,
    required this.module,
    required this.count,
    this.lastReadAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Badge.fromJson(Map<String, dynamic> json) {
    return Badge(
      id: json['id'],
      userId: json['user_id'],
      module: json['module'],
      count: json['count'],
      lastReadAt: json['last_read_at'] != null ? DateTime.parse(json['last_read_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }
}

class BadgeCounts {
  final Map<String, int> counts;
  final int total;

  BadgeCounts({
    required this.counts,
    required this.total,
  });

  factory BadgeCounts.fromJson(Map<String, dynamic> json) {
    return BadgeCounts(
      counts: Map<String, int>.from(json['counts']),
      total: json['total'],
    );
  }
}