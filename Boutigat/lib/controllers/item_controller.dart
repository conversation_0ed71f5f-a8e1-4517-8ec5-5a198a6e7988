import 'dart:developer';

import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/brands.dart';
import 'package:boutigak/data/services/brands_service.dart';
import 'package:boutigak/data/services/store_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/services/categories_service.dart';
import 'package:boutigak/data/services/item_service.dart';
import 'package:boutigak/data/models/item.dart';

import '../data/models/item_image.dart';

class ItemController extends GetxController {
  final AuthController authController = Get.find<AuthController>();
  Rx<Item?> selectedItem = Rx<Item?>(null);
  TextEditingController titleController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  final TextEditingController titleArController = TextEditingController();
  final TextEditingController descriptionArController = TextEditingController();
  RxString id;
  
  RxList<String> photos = <String>[].obs;
  RxString title = ''.obs;
  RxString description = ''.obs;
  RxString brand = ''.obs;
  RxString condition = ''.obs;
  RxDouble price = 0.0.obs;  
  RxDouble categoryPri = 0.0.obs;  

  RxBool isSoldOut = false.obs;
  RxInt quantity = 0.obs; 
  RxBool isOnPromotion = false.obs;
  RxInt promotionPercent = 0.obs; 
  RxString categoryID = ''.obs;
  RxList<Category> categories = <Category>[].obs;
  RxList<Brands> brands = <Brands>[].obs;
  RxList<CategoryDetails> selectedCategoryDetails = <CategoryDetails>[].obs;
  RxBool isItemUploaded = false.obs;
  final RxList<Item> items = <Item>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasMoreItems = true.obs;
  final RxInt currentPage = 1.obs;
  final int perPage = 10;
  RxList<Item> likedItems = <Item>[].obs;
  RxList<Item> myItems = <Item>[].obs;
  RxString categoryDetailId = ''.obs;

    final RxBool isLoadingMore = false.obs;

  // Add scroll controller
  final ScrollController scrollController = ScrollController();

  // Add this property to track if we're in edit mode
  final Rx<int?> itemId = Rx<int?>(null);

  // Add these properties to your controller
  final List<int> imageIdsToDelete = <int>[].obs;
  RxList<ItemImage> existingImages = <ItemImage>[].obs;

  // Add a method to update existing images
  void updateExistingImages(List<ItemImage> images) {
    existingImages.clear();
    existingImages.addAll(images);
    log('Updated existing images: ${existingImages.map((img) => '${img.id}:${img.url}').join(', ')}');
  }
  
  // Modify later to required
  ItemController( {
    String initialId = '',
    List<String> initialPhotos = const [],
    String initialTitle = '',
    String initialDescription = '',
    String initialBrand = '',
    String initialCondition = '',
    double? initialPrice,  
    bool initialIsSoldOut = false,
    bool initialIsOnPromotion = false,
    int initialPromotionPercent = 0,
    String categoryIDINIT = ''
  }) : id = (initialId.isNotEmpty ? initialId : '').obs {
    // Make sure AuthController is initialized
    Get.put(AuthController());
    
    // Rest of your constructor code
    categoryID.value = '';
    title.value = initialTitle;
    description.value = initialDescription;
    brand.value = initialBrand;
    condition.value = initialCondition;
    price.value = initialPrice ?? 0.0; 
    promotionPercent = initialPromotionPercent.obs ;
    photos.addAll(initialPhotos);
    
  }


  void clearItemData() {
  // Clear text controllers
  titleController.clear();
  descriptionController.clear();

  // Reset basic item properties
  id = ''.obs;
  photos.clear();
  title.value = '';
  description.value = '';
  brand.value = '';
  condition.value = '';
  price.value = 0.0;
  
  // Reset states
  isSoldOut.value = false;
  quantity.value = 0;
  isOnPromotion.value = false;
  promotionPercent.value = 0;
  
  // Reset category related data
  categoryID.value = '';
  selectedCategoryDetails.clear();
  
  
  // Reset selected item
  selectedItem.value = null;

  // Reset itemId
  itemId.value = null;

  update(); // Notify listeners of changes
}

  void addPhoto(String photoUrl) {
    if (!photos.contains(photoUrl)) {
      photos.add(photoUrl);
    }
  }

  void removePhoto(String photoUrl) {
    photos.remove(photoUrl);
  }

  bool hasPhotos() {
    return photos.isNotEmpty;
  }

  void updateBrand(String newBrand) {
    brand.value = newBrand;
  }

  void updateCondition(String newCondition) {
    condition.value = newCondition;
  }

  void updatePrice(double newPrice) {
    price.value = newPrice;
  }

  void setPromotion(int percent) {
    isOnPromotion.value = percent > 0;
    promotionPercent.value = percent;
  }

  void fetchCategories() async {
    var fetchedCategories = await CategoryService.fetchCategories();


  
    if (fetchedCategories != null) {
      categories.value = fetchedCategories;
    }
  }

  void fetchBrands() async {
    var fetchBrands = await BrandService.fetchBrands(categoryID.value);
    if (fetchBrands != null) {
      
      brands.value = fetchBrands;
    }
  }

  String get selectedCategoryTitle {
    Category? selectedCategory = findCategoryById(categoryID.value);
    return selectedCategory != null ? selectedCategory.getTitle() : 'select_category'.tr;
  }

  

  void updateSelectedCategory(String categoryId) {
    categoryID.value = categoryId;
    var category = findCategoryById(categoryId);

    if (category != null) {
      if (category.details.isNotEmpty) {
        selectedCategoryDetails.value = category.details;



         
        categoryDetailId.value = category.id.toString();


        print('category detail id ${categoryDetailId.value}');
      } else {
        var parentCategory = findParentCategory(categoryId);
        


        if (parentCategory != null && parentCategory.details.isNotEmpty) {
          selectedCategoryDetails.value = parentCategory.details;
          // Add category_detail_id when using parent category details
          categoryDetailId.value = parentCategory.id.toString();
        } else {
          selectedCategoryDetails.clear();
          categoryDetailId.value = '';
        }
      }
    } else {
      selectedCategoryDetails.clear();
      categoryDetailId.value = '';
    }

    update();
  }


Category? findParentCategory(String childId) {
  for (var category in categories) {
    for (var child in category.children) {
      if (child.id.toString() == childId) {
        return category; // Parent found
      }
      var parent = _findParentCategoryRecursive(child, childId);
      if (parent != null) {
        return parent;
      }
    }
  }
  return null;
}

Category? _findParentCategoryRecursive(Category category, String childId) {
  for (var child in category.children) {
    if (child.id.toString() == childId) {
      return category;
    }
    var parent = _findParentCategoryRecursive(child, childId);
    if (parent != null) {
      return parent;
    }
  }
  return null;
}

  Category? findCategoryById(String id) {
    for (var category in categories) {
      if (category.id.toString() == id) {
        return category;
      }
      for (var child in category.children) {
        if (child.id.toString() == id) {
          return child;
        }
        // Check deeper levels if necessary
        var subChild = _findCategoryByIdRecursive(child, id);
        if (subChild != null) {
          return subChild;
        }
      }
    }
    return null;
  }

  Category? _findCategoryByIdRecursive(Category category, String id) {
    for (var child in category.children) {
      if (child.id.toString() == id) {
        return child;
      }
      var subChild = _findCategoryByIdRecursive(child, id);
      if (subChild != null) {
        return subChild;
      }
    }
    return null;
  }

  // Method to select an item and make it reactive
  void selectItem(Item item) {
    selectedItem.value = item;  // Set the current item to be observed
  }

  // Fetch a single item by its ID and update the selectedItem
  Future<void> fetchItemById(int id) async {
    try {
      log('id item $id');
      isLoading.value = true;
      errorMessage.value = '';
      final item = await ItemService.getItemById(id);
      if (item != null) {
        selectedItem.value = item;
        log(' new item id ${selectedItem.value?.title}');
        print('Loaded images: ${item.images}');
      } else {
        errorMessage.value = 'Item not found';
      }
    } catch (e) {
      print('Error fetching item: $e');
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
  



  // Function to fetch all items
  Future<void> fetchItems({bool refresh = false}) async {
    if (isLoading.value) return;
    
    if (refresh) {
      currentPage.value = 1;
      items.clear();
      hasMoreItems.value = true;
    }

    if (!hasMoreItems.value) {
      print('No more items to load');
      return;
    }

    print('Fetching items for page: ${currentPage.value}');
    isLoading.value = true;

    try {
      bool isAuthenticated = authController.token.value != '';
      
      List<Item>? fetchedItems = isAuthenticated 
          ? await ItemService.fetchItems(
              page: currentPage.value,
              perPage: perPage,
            )
          : await ItemService.fetchgeneralItems(
              page: currentPage.value,
              perPage: perPage,
            );

      if (fetchedItems != null) {
        if (fetchedItems.isEmpty) {
          print('No more items received from server');
          hasMoreItems.value = false;
        } else {
          print('Received ${fetchedItems.length} new items');
          for (var newItem in fetchedItems) {
            if (!items.any((existingItem) => existingItem.id == newItem.id)) {
              items.add(newItem);
            }
          }
          currentPage.value++;
        }
      }
    } catch (e) {
      debugPrint('Error fetching items: $e');
      hasMoreItems.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  
  // Function to fetch liked items
 Future<void> fetchLikedItems() async {
  var fetchedLikedItems = await ItemService.fetchLikedItems();
  if (fetchedLikedItems != null) {
    likedItems.value = fetchedLikedItems;
  }
}




     void clearFields() {
    // Clear all text controllers
    titleController.clear();
    descriptionController.clear();
    titleArController.clear();
    descriptionArController.clear();

    // Reset basic item properties
    id = ''.obs;
    photos.clear();
    title.value = '';
    description.value = '';
    brand.value = '';
    condition.value = '';

    isSoldOut.value = false;
    quantity.value = 0;
    isOnPromotion.value = false;
    promotionPercent.value = 0;

    // Reset category related data
    categoryID.value = '';
    categoryDetailId.value = '';
    selectedCategoryDetails.clear();






    // Clear image deletion tracking
    imageIdsToDelete.clear();
    existingImages.clear();

    // Clear brands list (will be refetched when category is selected)
    brands.clear();

    // Clear PhotoActionsController if it exists
    try {
      final photoController = Get.find<PhotoActionsController>();
      photoController.clearPhotoActionData();
    } catch (e) {
      // PhotoActionsController might not be initialized yet, which is fine
    }

    update(); // Notify listeners of changes
  }

 Future<void> deleteItem(int itemId) async {
    try {
      // Appelle le service pour supprimer l'item
      bool success = await ItemService.deleteItem(itemId);

      if (success) {
        // Mise à jour de la liste des items après suppression
        items.removeWhere((item) => item.id == itemId);
      //  // Get.snakbar("Success", "Item successfully deleted!");
      } else {
      //  // Get.snakbar("Error", "Failed to delete item");
      }
    } catch (e) {
    //  // Get.snakbar("Error", "An error occurred while deleting the item: ${e.toString()}");
    }
  }
Future<void> deleteStoreItem(int itemId) async {
  try {
    // Call the service to delete the item
    bool success = await StoreService.deleteStoreItem(itemId);
    
    if (success) {
      // Update the local list of items after deletion
      items.removeWhere((item) => item.id == itemId);
      
      // Refresh store items
      final storeController = Get.find<StoreController>();
      await storeController.fetchMyStoreItems();
      
      Get.snackbar("Success", "Item successfully deleted!");
    } else {
      // Get.snackbar("Error", "Failed to delete item");
    }
  } catch (e) {
    // Get.snackbar("Error", "An error occurred while deleting the item: ${e.toString()}");
  }
}

Future<bool> updateItem(int itemId) async {
  // Validate required fields



  log('title: ${titleController.text}');
  log('description: ${descriptionController.text}');
  log('price: ${price.value}');
  log('condition: ${condition.value}');
  log('brand: ${brand.value}');
  log('category: ${categoryID.value}');


  if (titleController.text.isEmpty ||
      descriptionController.text.isEmpty ||
      price.value <= 0 ||
      condition.value.isEmpty ||
      brand.value.isEmpty ||
      categoryID.value.isEmpty) {
    return false;
  }


  log('here in update');



  // Get brand and category IDs
  int? parsedBrandId = _findBrandIdByName(brand.value);
  int? parsedCategoryId = int.tryParse(categoryID.value);

  log('parsedCategoryId: $parsedCategoryId');

  log('parsedBrandId: $parsedBrandId');

  if (parsedCategoryId == null) {
    return false;
  }

  if (parsedBrandId == null) {
    parsedBrandId = -1;  // Custom brand indicator
  }

  // Create updated item object
  Item updatedItem = Item(
    id: itemId,
    title: titleController.text,
    titleAr: titleArController.text,
    description: descriptionController.text,
    descriptionAr: descriptionArController.text,
    price: price.value,
    condition: condition.value,
    quantity: quantity.value,
    brandId: parsedBrandId,
    categoryId: parsedCategoryId,
    categoryDetailId: categoryDetailId.value.isEmpty 
        ? parsedCategoryId 
        : int.parse(categoryDetailId.value),
    categoryItemDetails: selectedCategoryDetails
        .map((detail) => CategoryItemDetail(
            id: detail.id, 
            value:  detail.value ?? '' ,
            labelEn: detail.labelEn,
            labelAr: detail.labelAr,
            labelFr: detail.labelFr
            ))
        .toList(),
    images: photos,
  );

  try {
    // Call service to update the item
    bool success = await ItemService.putItem(updatedItem , imageIdsToDelete: imageIdsToDelete);
    
    log('response update item $success');

    if (success) {
      isItemUploaded.value = true;
      clearItemData();  // Reset all fields after successful update
      
      // Refresh items list to show updated item
      await fetchItems(refresh: true);
      return true;
    } else {
      return false;
    }
  } catch (e) {
    print('Error updating item: $e');
    return false;
  }
}

Future<void> postStoreItemWithProgress(Function(double) onUploadProgress) async {
  if (titleController.text.isEmpty ||
      descriptionController.text.isEmpty ||
      price.value <= 0 ||
      condition.value.isEmpty ||
      brand.value.isEmpty ||
      categoryID.value.isEmpty) {
    isItemUploaded.value = false;
    return;
  }

  int? parsedBrandId = _findBrandIdByName(brand.value);
  int? parsedCategoryId = int.tryParse(categoryID.value);

  if (parsedCategoryId == null) {
    return;
  }

  if (parsedBrandId == null) {
    parsedBrandId = -1;
  }

  Item item = Item(
    title: titleController.text,
    description: descriptionController.text,
    price: price.value,
    condition: condition.value,
    quantity: 1,
    brandId: parsedBrandId,
    categoryId: parsedCategoryId,
    categoryItemDetails: selectedCategoryDetails
        .map((detail) => CategoryItemDetail(id: detail.id, value: detail.value ?? '' , labelEn: detail.labelEn  , labelAr: detail.labelAr , labelFr: detail.labelFr ))
        .toList(),
    images: photos,
    categoryDetailId: categoryDetailId.value.isEmpty
        ? int.parse(categoryID.value)
        : int.parse(categoryDetailId.value),
  );

  try {
    final response = await StoreService.postStoreItemWithProgress(item, onUploadProgress);

    if (response != null) {
      categoryPri.value = response['categoryPrice'];
      isItemUploaded.value = true;
      clearFields();
    } else {
      isItemUploaded.value = false;
    }
  } catch (e) {
    print("Error while posting item with progress: $e");
    isItemUploaded.value = false;
  } finally {
    onUploadProgress(0.0);
  }
}


  int? _findBrandIdByName(String brandName) {
    log('brandName: $brandName');
    log('brands: $brands');

    if (brands.isEmpty && categoryID.value.isNotEmpty) {
      fetchBrands();
      return null; 
    }    


    try {
      return brands.firstWhere((brand) => brand.name == brandName).id;
    } catch (e) {
      return null;
    }
  }
   void reorderPhotos(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final String photo = photos.removeAt(oldIndex);
    photos.insert(newIndex, photo);

    // You can print the new order for testing purposes
    print("Updated photo order: $photos");
  }

  // Add this method to mark an image for deletion


void markImageForDeletion(int imageId) {
  if (!imageIdsToDelete.contains(imageId)) {
    imageIdsToDelete.add(imageId);
    log('Added image ID $imageId to deletion list: $imageIdsToDelete');
  }
}
  Future<int?> postItem(Function(double) onUploadProgress) async {
    // Vérification que tous les champs sont remplis

    // print verifications 

    print('titleController.text: ${titleController.text}');
    print('descriptionController.text: ${descriptionController.text}');
    print('price.value: ${price.value}');
    print('condition.value: ${condition.value}');
    print('brand.value: ${brand.value}');
    print('categoryID.value: ${categoryID.value}');
    print('categoryDetailId.value: ${categoryDetailId.value}');
    if (titleController.text.isEmpty ||
        descriptionController.text.isEmpty ||
        price.value <= 0 ||
        condition.value.isEmpty ||
        brand.value.isEmpty ||
        categoryID.value.isEmpty) {
    //  // Get.snakbar("Error", "Please fill in all required fields.");
      isItemUploaded.value = true;


      print('here ...');
      return null;
    }

    // Traitement de la marque et de la catégorie
    int? parsedBrandId = _findBrandIdByName(brand.value);
    int? parsedCategoryId = int.tryParse(categoryID.value);

    if (parsedCategoryId == null) {
    //  // Get.snakbar("Error", "Invalid category ID");
      return null;
    }

    // Si pas de marque valide, utiliser une valeur par défaut
    if (parsedBrandId == null) {
    //  print("Utilisation d'une marque personnalisée : ${brand.value}");
      parsedBrandId = -1;  // Indicateur pour une marque personnalisée
    }

    // Création de l'objet Item avec les valeurs des TextEditingController
    Item item = Item(
      title: titleController.text,
      description: descriptionController.text,
      price: price.value,
      condition: condition.value,
      quantity: 1,
      brandId: parsedBrandId,
      categoryId: parsedCategoryId,
      categoryItemDetails: selectedCategoryDetails
          .map((detail) => CategoryItemDetail(id: detail.id, value: detail.value ?? '' , labelEn: detail.labelEn  , labelAr: detail.labelAr , labelFr: detail.labelFr ))
          .toList(),
      images: photos,
      categoryDetailId: categoryDetailId.value.isEmpty ? int.parse(categoryID.value)  : int.parse(categoryDetailId.value) ,



      titleAr: titleArController.text,
      descriptionAr: descriptionArController.text,
    );

    // Log de l'item à envoyer
    print('Sending item: ${item.toJson()}');

    try {
      // Envoi de l'item au serveur avec suivi de la progression
      dynamic res = await ItemService.postItem(item, (double progress) {
        onUploadProgress(progress); // Update progress in the callback
      });

      var categoryPrice = res['categoryPrice'];
      var itemId = res['item']['id']; // Assuming the response contains the item ID

    //  print('Sending item price : ${categoryPrice}');

      if (categoryPrice != null) {
     //   print('categoryPrice not null item price : ${categoryPrice}');
        categoryPri.value = categoryPrice; 
        isItemUploaded.value = true;
      //  // Get.snakbar("Success", "Item successfully uploaded!");
        clearFields();  // Vider les champs après succès
        return itemId; // Return the item ID
      } else {
     //   // Get.snakbar("Error", "Failed to upload item");
        clearFields();  // Vider les champs en cas d'échec également
        return null;
      }
    } catch (e) {
      // Gestion des erreurs lors de l'envoi
    //  // Get.snakbar("Error", "An error occurred while posting the item: $e");
      clearFields();  // Vider les champs en cas d'erreur
      return null;
    } finally {
      // Réinitialiser la progression après l'envoi
      onUploadProgress(0.0);
    }
  }


  Future<void> loadItemDetails(int itemId) async {
    try {
      // Appelle le service pour récupérer les détails de l'élément
      Item? fetchedItem = await ItemService.getItemById(itemId);

      if (fetchedItem != null) {
        // Mets à jour les valeurs dans le contrôleur avec les détails de l'élément
        titleController.text = fetchedItem.title;
        descriptionController.text = fetchedItem.description;
        price.value = fetchedItem.price;
        condition.value = fetchedItem.condition;
        brand.value = fetchedItem.brandId.toString(); // Assumes the brand is represented as an ID
        categoryID.value = fetchedItem.categoryId.toString();
        
        // Sort images by order if available
        if (fetchedItem.images.isNotEmpty) {
          // Images should already be sorted by order from the backend
          photos.value = fetchedItem.images;
          
          // First image is considered the main image
          // You can add a mainImage property if needed
          // mainImage.value = fetchedItem.images[0];
        }

        // Mets à jour l'élément sélectionné
        selectedItem.value = fetchedItem;
      } else {
        // Handle error
      }
    } catch (e) {
      print('Error loading item details: $e');
    }
  }


  Future<void> loadItemForEdit(int id) async {
    itemId.value = id;
    imageIdsToDelete.clear();
    existingImages.clear();
    
    try {
      Item? item = await ItemService.getItemById(id);
      
      if (item != null) {
        // Set category ID first so we can fetch brands
        categoryID.value = item.categoryId.toString();
        
        // Fetch brands for this category
        fetchBrands();
        
        // Populate text controllers
        titleController.text = item.title;
        descriptionController.text = item.description;



        log('title ar ${item.titleAr}');

        titleArController.text = item.titleAr ?? '';
        descriptionArController.text = item.descriptionAr ?? '';
        
        // Populate observable values
        price.value = item.price;
        condition.value = item.condition;
        brand.value = item.brandName ?? '';
        categoryDetailId.value = item.categoryDetailId?.toString() ?? '';
        

        
        log('category details ${item.categoryItemDetails}');

        
        selectedCategoryDetails.clear();
        selectedCategoryDetails.addAll(
          item.categoryItemDetails.map((detail) => CategoryDetails(
            id: detail.id,
            labelEn: detail.labelEn,
            labelAr: detail.labelAr,
            labelFr: detail.labelFr,
            value: detail.value,
            categoryId: item.categoryId,
            createdDate: DateTime.now(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ))
        );



        log('selectedCategoryDetails: ${selectedCategoryDetails.map((detail) => 
        
        'id : ${detail.id} label en :${detail.labelEn}: label fr:${detail.labelFr} label ar:${detail.labelAr} value :${detail.value}').join(', ') } ');
        
        // Clear and update photos in both controllers
        photos.clear();
        final updatedPhotos = item.images;
        photos.value = updatedPhotos;

        // Update PhotoActionsController
        final photoActionsController = Get.find<PhotoActionsController>();
        photoActionsController.photos.clear();
        photoActionsController.photos.value = updatedPhotos;
        
        print('photos after loading: ${photos.value}');
        print('PhotoActionsController photos: ${photoActionsController.photos.value}');
        
        update();
        photoActionsController.update();
      }
    } catch (e) {
      print('Error loading item for edit: $e');
    }
  }


  @override
  void onInit() {
    super.onInit();
    _setupScrollController();
    fetchItems(); // Initial load
  }

  void _setupScrollController() {
    scrollController.addListener(() {
      double maxScroll = scrollController.position.maxScrollExtent;
      double currentScroll = scrollController.position.pixels;
      double threshold = maxScroll * 0.7; // Load more at 70% of scroll

      if (currentScroll > threshold && !isLoading.value && hasMoreItems.value) {
        print('Loading more items...');
        print('Current page: ${currentPage.value}');
        print('Current items count: ${items.length}');
        fetchItems();
      }
    });
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
