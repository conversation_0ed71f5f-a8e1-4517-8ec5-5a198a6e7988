<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StorePaymentProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'provider_id',
        'payment_code',
        'phone_number',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function provider()
    {
        return $this->belongsTo(EPaymentProvider::class, 'provider_id');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}