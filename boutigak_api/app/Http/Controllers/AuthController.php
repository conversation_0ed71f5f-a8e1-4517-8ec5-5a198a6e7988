<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Models\User;
use App\Models\UserScore;
use App\services\OtpService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;


/**
 * @OA\Info(title="Boutigak API", version="1.0")
 */
class AuthController extends Controller
{
    private $OtpService;

    public function __construct(OtpService $OtpService)
    {
        $this->OtpService = $OtpService;
    }

    /**
     * @OA\Post(
     *     path="/api/auth/register",
     *     tags={"Auth"},
     *     summary="Register a new user",
     *     description="Register a new user with the provided information.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="firstname", type="string", example="John"),
     *             @OA\Property(property="lastname", type="string", example="Doe"),
     *             @OA\Property(property="phone", type="string", example="**********"),
     *             @OA\Property(property="password", type="string", example="password123"),
     *             @OA\Property(property="invitationcode", type="string", example="ABC123"),
     *             @OA\Property(property="gender", type="string", example="male")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Successful registration"),
     *     @OA\Response(response=400, description="Validation error")
     * )
     */
    // Register a new user
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => 'required|string',
            'lastname' => 'required|string',
            'phone' => 'required|string:unique:users|min:8',
            'password' => 'required|string|min:6',
            'invitationcode' => 'nullable|string',
            'gender' => 'required|string',
        ]);

        // check if phone already exists, if so return 409
        $user = User::where('phone', $request->phone)->first();
        if ($user) {
            return response()->json(['error' => 'User already exists'], 409);
        }
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $data = $validator->validated();
        $data['password'] = Hash::make($data['password']);
        $user = User::create($data);

        $token = $user->createToken('Boutigak')->plainTextToken;

        UserScore::create([
            'user_id' => $user->id,
            'score' => 0
        ]);

        return response()->json([
            'id' => $user->id,
            'firstname' => $user->firstname,
            'lastname' => $user->lastname,
            'phone' => $user->phone,
            'invitationcode' => $user->invitationcode,
            'gender' => $user->gender,
            'access_token' => $token,
            'roles' => $user->getRoleNames()
        ]);
    }


    /**
     * @OA\Post(
     *     path="/api/auth/login",
     *     tags={"Auth"},
     *     summary="User login",
     *     description="Authenticate the user and return an access token.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="phone", type="string", example="**********"),
     *             @OA\Property(property="password", type="string", example="password123")
     *         )
     *     ),
     *     @OA\Response(response=200, description="Successful login"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=400, description="Validation error")
     * )
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $credentials = $validator->validated();

        if (!Auth::attempt($credentials)) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();

        // Check if account is marked for deletion
        if ($user->is_deleted || $user->delete_reason) {
            return response()->json([
                'error' => 'Account has been marked for deletion. Please recover your account to continue.'
            ], 405);
        }

        if (!$user->is_verified) {
            // Generate and send OTP
            $this->OtpService->generateOtp($user->phone);

            return response()->json([
                'message' => 'OTP sent to your phone number. Please verify to continue.',
                'user_id' => $user->id
            ], 403);
        }

        $subscriptions = $user->subscriptions;
        $hasSubscription = $subscriptions->isNotEmpty();
        $token = $user->createToken('Boutigak')->plainTextToken;

        return response()->json([
            'user' => $user,
            'access_token' => $token,
            'has_subscription' => $hasSubscription,
        ]);
    }


    /**
     * @OA\Post(
     *     path="/api/auth/logout",
     *     tags={"Auth"},
     *     summary="User logout",
     *     description="Invalidate the user's token and log them out.",
     *     @OA\Response(response=200, description="Successful logout")
     * )
     */
    public function logout(): \Illuminate\Http\JsonResponse
    {
        try {
            $user = auth()->user();

            if ($user) {
                DeviceToken::query()->where('user_id', $user->id)->delete();
                Log::info('Device token deleted during logout', ['user_id' => $user->id]);

                $user->update(['last_activity' => now()]);
            }

            auth()->logout();
            Log::info('User logged out', ['user_id' => $user?->id]);

            return response()->json(['message' => 'Logout successful']);
        } catch (\Throwable $e) {
            Log::error('Error during logout', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'An error occurred during logout'], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/change-password",
     *     summary="Change the authenticated user's password",
     *     description="Allows the authenticated user to change their password by providing the current password and a new one.",
     *     operationId="changePassword",
     *     tags={"User"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"current_password", "new_password", "new_password_confirmation"},
     *             @OA\Property(property="current_password", type="string", example="oldPassword123"),
     *             @OA\Property(property="new_password", type="string", format="password", minLength=8, example="newPassword123"),
     *             @OA\Property(property="new_password_confirmation", type="string", format="password", example="newPassword123"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Password changed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Password changed successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized - Current password is incorrect",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Current password is incorrect")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="errors", type="object",
     *                 @OA\Property(property="current_password", type="array", @OA\Items(type="string")),
     *                 @OA\Property(property="new_password", type="array", @OA\Items(type="string"))
     *             )
     *         )
     *     )
     * )
     */
    public function changePassword(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
            'new_password_confirmation' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Get the authenticated user
        $user = Auth::user();

        // Check if the provided current password matches the stored password
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json(['message' => 'Current password is incorrect'], 401);
        }

        // Update the password
        $user->password = Hash::make($request->new_password);
        $user->save();

        return response()->json(['message' => 'Password changed successfully'], 200);
    }

    /**
     * @OA\Post(
     *     path="/api/password/request-reset",
     *     tags={"Auth"},
     *     summary="Request password reset",
     *     description="Send OTP to user's phone number for password reset",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="phone", type="string", example="**********")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="OTP sent successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="OTP sent to your phone")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="User not found")
     *         )
     *     )
     * )
     */
    public function requestPasswordReset(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = User::where('phone', $request->phone)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        // Generate and send OTP
        $this->OtpService->generateOtp($user->phone);

        return response()->json(['message' => 'OTP sent to your phone']);
    }

    /**
     * @OA\Post(
     *     path="/api/password/reset",
     *     tags={"Auth"},
     *     summary="Reset password with OTP",
     *     description="Verify OTP and set new password",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="phone", type="string", example="**********"),
     *             @OA\Property(property="otp", type="string", example="123456"),
     *             @OA\Property(property="password", type="string", example="new_password123"),
     *             @OA\Property(property="password_confirmation", type="string", example="new_password123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Password reset successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Password reset successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid OTP or validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="User not found")
     *         )
     *     )
     * )
     */
    public function resetPassword(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'otp' => 'required|string',
            'password' => 'required|string|min:6|confirmed',
            'password_confirmation' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = User::where('phone', $request->phone)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        // Verify OTP
        if (!$this->OtpService->verifyOtp($user->phone, $request->otp)) {
            return response()->json(['error' => 'Invalid OTP'], 400);
        }

        // Update password
        $user->password = Hash::make($request->password);
        $user->save();

        return response()->json(['message' => 'Password reset successfully']);
    }

    /************ API ***********
     * @throws Exception
     */
    public function requestDeleteAccount(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'User not connected'], 401);
        }

        $this->OtpService->generateOtp($user->phone);

        return response()->json(['message' => 'OTP sent to your phone']);
    }

    /**
     * @OA\Delete(
     *     path="/api/auth/delete-account",
     *     tags={"Auth"},
     *     summary="Delete user account",
     *     description="Delete user account and add delay of 30 days to recover it.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="password", type="string", example="current_password"),
     *             @OA\Property(property="delete_reason", type="string", example="No longer needed")
     *         )
     *     ),
     *     @OA\Response(response=200, description="User account deleted successfully"),
     *     @OA\Response(response=401, description="Invalid password"),
     *     @OA\Response(response=404, description="User not found"),
     *     @OA\Response(response=500, description="Server error")
     * )
     */
    public function deleteAccount(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'delete_reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = $request->user();

        // Verify the password
        if (!Hash::check($request->password, $user->password)) {
            return response()->json(['error' => 'Invalid password'], 401);
        }

        $user->update([
            'delete_reason' => $request->delete_reason,
            'is_deleted' => true,
        ]);

        return response()->json(['message' => 'User account deleted successfully']);
    }

    /**
     * @OA\Post(
     *     path="/api/auth/recover-account",
     *     tags={"Auth"},
     *     summary="Recover user account",
     *     description="Recover user account before 30 days.",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="phone", type="string", example="**********"),
     *             @OA\Property(property="password", type="string", example="new_password123"),
     *             @OA\Property(property="password_confirm", type="string", example="new_password123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User account recovered successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="User account recovered successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="User not found")
     *         )
     *     )
     * )
     */
    public function recoverAccount(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string|min:6',
            'password_confirm' => 'required|string|same:password',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        $user = User::query()->where('phone', $request->phone)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $user->update([
            'deleted_at' => null,
            'password' => Hash::make($request->password)
        ]);

        return response()->json(['message' => 'User account recovered successfully']);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Validation\Validator
     */
    public function validateUserPassword(Request $request): \Illuminate\Validation\Validator
    {
        return Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string|min:6',
            'password_confirm' => 'required|string|same:password',
        ]);
    }


    /**
     * @OA\Put(
     *     path="/api/user/update-language",
     *     tags={"Auth"},
     *     summary="Update user language preference",
     *     description="Update the language preference for the authenticated user.",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="lang",
     *                 type="string",
     *                 example="fr",
     *                 description="Language code (e.g., 'en', 'fr', 'ar')"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Language updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="message",
     *                 type="string",
     *                 example="Language updated successfully"
     *             ),
     *             @OA\Property(
     *                 property="user",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="firstname", type="string", example="John"),
     *                 @OA\Property(property="lastname", type="string", example="Doe"),
     *                 @OA\Property(property="lang", type="string", example="fr")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     )
     * )
     */
    public function updateLanguage(Request $request): \Illuminate\Http\JsonResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'lang' => 'required|string|in:en,fr,ar' // Add any other supported languages
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        try {
            $user = Auth::user();

            // Update the user's language preference
            $user->update([
                'lang' => $request->lang
            ]);

            // Log the language update
            Log::info('User language updated', [
                'user_id' => $user->id,
                'language' => $request->lang
            ]);

            return response()->json([
                'message' => 'Language updated successfully',
                'user' => [
                    'id' => $user->id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'lang' => $user->lang
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Error updating user language: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while updating the language'
            ], 500);
        }
    }

    public function verifyOtpAndSetVerified(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'otp' => 'required|string',
        ]);

        $user = User::where('phone', $request->phone)->first();


        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $isValid = $this->OtpService->verifyOtp($request->phone, $request->otp);

        if ($isValid) {
            $user->is_verified = true;
            $user->save();

            $token = $user->createToken('Boutigak')->plainTextToken;

            return response()->json([
                'message' => 'OTP verified successfully',
                'user' => $user,
                'access_token' => $token
            ]);
        } else {
            return response()->json(['error' => 'Invalid or expired OTP'], 400);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/auth/user",
     *     tags={"Auth"},
     *     summary="Get authenticated user information",
     *     description="Returns the current authenticated user's details",
     *     security={{"bearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="firstname", type="string"),
     *                 @OA\Property(property="lastname", type="string"),
     *                 @OA\Property(property="phone", type="string"),
     *                 @OA\Property(property="gender", type="string"),
     *                 @OA\Property(property="invitationcode", type="string"),
     *                 @OA\Property(property="is_verified", type="boolean")
     *             ),
     *             @OA\Property(property="has_subscription", type="boolean")
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function getUser()
    {
        $user = Auth::user();
        $subscriptions = $user->subscriptions;
        $hasSubscription = $subscriptions->isNotEmpty();

        if ($user->is_deleted || $user->delete_reason) {
            Log::warning('User account is marked for deletion', ['user_id' => $user->id]);
            return response()->json(['error' => 'Account has been marked for deletion. Please recover your account to continue.'], 403);
        }

        return response()->json([
            'user' => $user,
            'has_subscription' => $hasSubscription
        ]);
    }
}
