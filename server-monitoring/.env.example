# Server Monitoring Environment Configuration

# Application Environment
ENVIRONMENT=development
APP_DEBUG=true
APP_URL=http://localhost:8080

# Database Configuration (PostgreSQL)
DB_HOST=************
DB_PORT=5432
DB_NAME=boutigak_db
DB_USERNAME=postgress
DB_PASSWORD=adminboutigak

# SSH Configuration for Remote Server Monitoring
SSH_HOST=************
SSH_PORT=22
SSH_USERNAME=your_ssh_username
SSH_PASSWORD=your_ssh_password

# Alternative SSH Key Authentication (recommended for production)
# SSH_PRIVATE_KEY_PATH=/path/to/private/key
# SSH_PUBLIC_KEY_PATH=/path/to/public/key

# Redis Configuration (for caching)
REDIS_HOST=redis-cache
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Monitoring Configuration
MONITORING_REFRESH_INTERVAL=30
MONITORING_DATA_RETENTION=24
MONITORING_ENABLE_ALERTS=false

# Alert Thresholds
ALERT_CPU_WARNING=70
ALERT_CPU_CRITICAL=85
ALERT_RAM_WARNING=80
ALERT_RAM_CRITICAL=90
ALERT_DISK_WARNING=80
ALERT_DISK_CRITICAL=90
ALERT_RESPONSE_TIME_WARNING=1000
ALERT_RESPONSE_TIME_CRITICAL=2000

# Email Alerts (optional)
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_TO=<EMAIL>
ALERT_EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# Logging
LOG_LEVEL=debug
LOG_FILE=/var/log/monitoring/app.log

# Security
ENABLE_DEBUG_LOGS=true
ENABLE_API_RATE_LIMITING=false
API_RATE_LIMIT=100

# Docker Development Settings
DOCKER_APP_PORT=8080
DOCKER_NGINX_PORT=80
DOCKER_NGINX_SSL_PORT=443
DOCKER_REDIS_PORT=6379
DOCKER_ADMINER_PORT=8081
