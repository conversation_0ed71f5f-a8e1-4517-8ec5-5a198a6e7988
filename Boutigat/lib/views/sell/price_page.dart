import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/item_controller.dart';
import '/constants/app_colors.dart';


class PricePage extends StatelessWidget {
  final ItemController itemController = Get.find<ItemController>();
  final TextEditingController priceController = TextEditingController();

  PricePage({Key? key}) : super(key: key) {
    
    if (itemController.price.value != 0.0) { 
      priceController.text = itemController.price.value.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Price'),
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TextField(
              controller: priceController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              decoration: const InputDecoration(
                labelText: 'Enter the item price',
                hintText: '00.00 UM',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                  borderSide: BorderSide(color: AppColors.primary, width: 2.0), 
                ),
              ),
              onChanged: (value) {
                double? newPrice = double.tryParse(value);
                if (newPrice != null) {
                  itemController.price.value = newPrice;
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}