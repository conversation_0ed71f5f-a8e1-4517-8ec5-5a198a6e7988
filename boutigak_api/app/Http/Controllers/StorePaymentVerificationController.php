<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PaymentProof;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="Store Payment Verification",
 *     description="API Endpoints for store payment verification management"
 * )
 */
class StorePaymentVerificationController extends Controller
{
    /**
     * Get store's pending payment proofs
     * 
     * @OA\Get(
     *     path="/api/store/payments",
     *     operationId="getStorePaymentProofs",
     *     tags={"Store Payment Verification"},
     *     summary="Get store's payment proofs",
     *     description="Retrieve all payment proofs for the authenticated user's store with pagination",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Payment proofs retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="current_page",
     *                     type="integer",
     *                     example=1
     *                 ),
     *                 @OA\Property(
     *                     property="data",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="store_id", type="integer", example=1),
     *                         @OA\Property(property="user_id", type="integer", example=1),
     *                         @OA\Property(property="provider_id", type="integer", example=1),
     *                         @OA\Property(property="order_id", type="integer", example=1),
     *                         @OA\Property(property="amount", type="number", format="float", example=100.50),
     *                         @OA\Property(property="screenshot", type="string", example="path/to/screenshot.jpg"),
     *                         @OA\Property(property="status", type="string", example="PENDING"),
     *                         @OA\Property(property="reference_number", type="string", example="REF123"),
     *                         @OA\Property(property="to_store", type="boolean", example=true),
     *                         @OA\Property(
     *                             property="user",
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=1),
     *                             @OA\Property(property="firstname", type="string", example="John"),
     *                             @OA\Property(property="lastname", type="string", example="Doe")
     *                         ),
     *                         @OA\Property(
     *                             property="order",
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=1),
     *                             @OA\Property(property="status", type="string", example="pending")
     *                         ),
     *                         @OA\Property(
     *                             property="provider",
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=1),
     *                             @OA\Property(property="name", type="string", example="PayPal")
     *                         ),
     *                         @OA\Property(property="created_at", type="string", format="date-time"),
     *                         @OA\Property(property="updated_at", type="string", format="date-time")
     *                     )
     *                 ),
     *                 @OA\Property(property="first_page_url", type="string"),
     *                 @OA\Property(property="from", type="integer"),
     *                 @OA\Property(property="last_page", type="integer"),
     *                 @OA\Property(property="last_page_url", type="string"),
     *                 @OA\Property(property="per_page", type="integer"),
     *                 @OA\Property(property="to", type="integer"),
     *                 @OA\Property(property="total", type="integer")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store not found")
     *         )
     *     )
     * )
     */
    public function index()
    {
        $store = auth()->user()->store;
        
        if (!$store) {
            return response()->json(['message' => 'Store not found'], 404);
        }

        $paymentProofs = PaymentProof::where('store_id', $store->id)
            ->where('to_store', true)
            ->with(['user', 'order', 'provider'])
            ->latest()
            ->paginate(20);

        return response()->json(['data' => $paymentProofs]);
    }

    /**
     * Verify payment proof
     * 
     * @OA\Post(
     *     path="/api/store/payments/{id}/verify",
     *     operationId="verifyStorePayment",
     *     tags={"Store Payment Verification"},
     *     summary="Verify a payment proof",
     *     description="Approve or reject a payment proof for the authenticated user's store",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Payment proof ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"status"},
     *             @OA\Property(
     *                 property="status",
     *                 type="string",
     *                 enum={"APPROVED", "REJECTED"},
     *                 description="Payment verification status"
     *             ),
     *             @OA\Property(
     *                 property="rejection_reason",
     *                 type="string",
     *                 description="Required when status is REJECTED"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Payment verification completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Payment verification completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="status", type="string", example="APPROVED"),
     *                 @OA\Property(property="rejection_reason", type="string", nullable=true),
     *                 @OA\Property(property="verified_by", type="integer", example=1),
     *                 @OA\Property(property="verified_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Store or payment proof not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Store not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Failed to verify payment")
     *         )
     *     )
     * )
     */
    public function verify(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:APPROVED,REJECTED',
            'rejection_reason' => 'required_if:status,REJECTED'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $store = auth()->user()->store;
            
            if (!$store) {
                return response()->json(['message' => 'Store not found'], 404);
            }

            $paymentProof = PaymentProof::where('store_id', $store->id)
                ->where('to_store', true)
                ->findOrFail($id);

            $paymentProof->update([
                'status' => $request->status,
                'rejection_reason' => $request->rejection_reason,
                'verified_by' => auth()->id(),
                'verified_at' => now()
            ]);

            // If approved, update order status
            if ($request->status === 'APPROVED' && $paymentProof->order) {
                $paymentProof->order->update([
                    'payment_status' => 'PAID'
                ]);
            }

            return response()->json([
                'message' => 'Payment verification completed',
                'data' => $paymentProof
            ]);

        } catch (\Exception $e) {
            Log::error('Payment verification failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to verify payment'
            ], 500);
        }
    }
}