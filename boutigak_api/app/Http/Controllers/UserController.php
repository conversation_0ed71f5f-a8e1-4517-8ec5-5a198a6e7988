<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Yajra\DataTables\Facades\DataTables;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view user', ['only' => ['index']]);
        $this->middleware('permission:create user', ['only' => ['create', 'store']]);
        $this->middleware('permission:update user', ['only' => ['update', 'edit']]);
        $this->middleware('permission:delete user', ['only' => ['destroy']]);
    }

    /**
     * @throws Exception
     */
    public function index(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|JsonResponse|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        if ($request->ajax()) {
            $users = User::with('roles')->get();
            return DataTables::of($users)
                ->addIndexColumn()
                ->addColumn('action', function ($user) {
                    $showUrl = route('users.show', $user->id);
                    $editUrl = url('users/' . $user->id . '/edit');
                    $deleteUrl = url('users/' . $user->id . '/delete');

                    $btn = '<a href="' . $showUrl . '" class="btn btn-info">Show</a>';
                    $btn .= ' <a href="' . $editUrl . '" class="btn btn-success mx-2">Edit</a>';
                    $btn .= ' <a href="' . $deleteUrl . '" class="btn btn-danger" data-can-delete="true">Delete</a>';

                    return $btn;
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('backoffice.role-permission.user.index');
    }

    public function create(): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $roles = Role::all();
        return view('backoffice.role-permission.user.create', ['roles' => $roles]);
    }

    public function show($id): \Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application|\Illuminate\Contracts\View\View|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application|\Laravel\Lumen\Application
    {
        $user = User::with(['store', 'items'])->findOrFail($id);
        return view('backoffice.role-permission.user.show', compact('user'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'invitationcode' => 'required|string|max:255',
            'gender' => 'required|string|in:male,female',
            'password' => 'required|string|min:8|max:20|confirmed',
            'roles' => 'required|array|exists:roles,id'
        ]);

        DB::beginTransaction();

        try {
            $user = User::query()->create([
                'firstname' => $request->firstname,
                'lastname' => $request->lastname,
                'phone' => $request->phone,
                'invitationcode' => $request->invitationcode,
                'gender' => $request->gender,
                'password' => Hash::make($request->password),
            ]);

            $roles = Role::query()->whereIn('id', $request->roles)->pluck('id')->toArray();

            $roleData = [];
            foreach ($roles as $roleId) {
                $roleData[] = [
                    'role_id' => $roleId,
                    'model_type' => User::class,
                    'model_id' => $user->id,
                ];
            }

            DB::table('model_has_roles')->insert($roleData);

            DB::commit();

            return redirect('/users')->with('success', 'User created successfully with roles');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect('/users')->with('error', 'Failed to create user: ' . $e->getMessage());
        }
    }

    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();
        return view('backoffice.role-permission.user.edit', [
            'user' => $user,
            'roles' => $roles,
            'userRoles' => $userRoles
        ]);
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:users,phone,' . $user->id,
            'invitationcode' => 'required|string|max:255|unique:users,invitationcode,' . $user->id,
            'gender' => 'required|string|in:male,female',
            'password' => 'nullable|string|min:8|max:20|confirmed',
            'roles' => 'required|array|exists:roles,id'
        ]);

        // Start transaction
        DB::beginTransaction();

        try {
            $data = [
                'firstname' => $request->firstname,
                'lastname' => $request->lastname,
                'phone' => $request->phone,
                'invitationcode' => $request->invitationcode,
                'gender' => $request->gender,
            ];

            if (!empty($request->password)) {
                $data['password'] = Hash::make($request->password);
            }

            $user->update($data);

            $roles = Role::query()->whereIn('id', $request->roles)->pluck('id')->toArray();

            $roleData = [];
            foreach ($roles as $roleId) {
                $roleData[] = [
                    'role_id' => $roleId,
                    'model_type' => User::class,
                    'model_id' => $user->id,
                ];
            }

            DB::table('model_has_roles')->where('model_id', $user->id)->delete();
            DB::table('model_has_roles')->insert($roleData); // Insert the new roles

            DB::commit();

            return redirect('/users')->with('info', 'User updated successfully with roles');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect('/users')->with('error', 'Failed to update user: ' . $e->getMessage());
        }
    }


    public function destroy($userId): \Illuminate\Foundation\Application|\Laravel\Lumen\Http\Redirector|\Illuminate\Routing\Redirector|\Illuminate\Contracts\Foundation\Application|\Illuminate\Http\RedirectResponse
    {
        $user = User::query()->findOrFail($userId);
        $user->delete();

        return redirect('/users')->with('danger', 'User Delete Successfully');
    }

    /**
     * @OA\Post(
     *     path="/api/users/location",
     *     tags={"User"},
     *     summary="Store authenticated user location",
     *     description="Store a new location for the authenticated user.",
     *     operationId="storeAuthenticatedUserLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"address", "latitude", "longitude", "name"},
     *             @OA\Property(property="address", type="string", example="123 Main St"),
     *             @OA\Property(property="latitude", type="number", format="float", example=40.7128),
     *             @OA\Property(property="longitude", type="number", format="float", example=-74.0060),
     *             @OA\Property(property="name", type="string", example="Home")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Location stored successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="location_id", type="integer"))
     *     ),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function storeUserLocation(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();

        // Create new location
        $location = Location::create($request->all());

        // Attach location to user
        $user->locations()->attach($location->id);

        return response()->json(['location_id' => $location->id], 201);
    }


    /**
     * @OA\Get(
     *     path="/api/users/location",
     *     tags={"User"},
     *     summary="Get authenticated user location",
     *     description="Retrieve the location of the authenticated user.",
     *     operationId="getAuthenticatedUserLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="address", type="string"),
     *             @OA\Property(property="latitude", type="number", format="float"),
     *             @OA\Property(property="longitude", type="number", format="float"),
     *             @OA\Property(property="name", type="string"),
     *             @OA\Property(property="created_at", type="string", format="date-time"),
     *             @OA\Property(property="updated_at", type="string", format="date-time")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Location not found")
     * )
     */
    public function getUserLocation(): \Illuminate\Http\JsonResponse
    {
        $user = Auth::user();


        $locations = $user->locations;

        return response()->json($locations, 200);
    }

    // update user location with open api documentation

    /**
     * @OA\Put(
     *     path="/api/users/location",
     *     tags={"User"},
     *     summary="Update authenticated user location",
     *     description="Update the location of the authenticated user.",
     *     operationId="updateAuthenticatedUserLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"address", "latitude", "longitude", "name"},
     *             @OA\Property(property="address", type="string", example="123 Main St"),
     *             @OA\Property(property="latitude", type="number", format="float", example=40.7128),
     *             @OA\Property(property="longitude", type="number", format="float", example=-74.0060),
     *             @OA\Property(property="name", type="string", example="Home")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Location updated successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="location_id", type="integer"))
     *     ),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function updateUserLocation(Request $request, $locationId): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = Auth::user();
        $location = $user->locations()->findOrFail($locationId);

        $location->update($request->all());

        return response()->json(['location_id' => $location->id], 200);
    }

    /**
     * @OA\Delete(
     *     path="/api/users/locations/{locationId}",
     *     tags={"User"},
     *     summary="Delete user location",
     *     description="Delete a location associated with the authenticated user",
     *     operationId="deleteUserLocation",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="locationId",
     *         in="path",
     *         required=true,
     *         description="ID of the location to delete",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Location deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Location deleted successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Location not found"),
     *     @OA\Response(response=403, description="Unauthorized access to location"),
     *     @OA\Response(response=401, description="Unauthenticated")
     * )
     */
    public function deleteUserLocation($locationId): \Illuminate\Http\JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if location exists and belongs to user
            $location = $user->locations()->findOrFail($locationId);

            // Detach location from user
            $user->locations()->detach($locationId);

            // Delete location if no other users are using it
            if ($location->users()->count() === 0) {
                $location->delete();
            }

            return response()->json(['message' => 'Location deleted successfully'], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Location not found or does not belong to user'], 404);
        } catch (Exception $e) {
            return response()->json(['error' => 'An error occurred while deleting the location'], 500);
        }
    }
}
