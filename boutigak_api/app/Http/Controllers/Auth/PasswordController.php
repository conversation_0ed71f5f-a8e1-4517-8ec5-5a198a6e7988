<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class PasswordController extends Controller
{
    /**
     * Update the user's password.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', Password::min(8)->mixedCase()->numbers()->uncompromised()],
            'password_confirmation' => ['required', 'string', 'same:password'],
        ]);

        if (!Hash::check($validated['current_password'], $request->user()->password)) {
            return back()->with('error', 'The provided password does not match your current password.');
        }

        $request->user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return back()->with('success', 'Password updated successfully.');
    }

    /**
     * Display the form to change the user's password.
     */
    public function edit(): \Illuminate\View\View
    {
        return view('profile.partials.update-password-form');
    }
}
