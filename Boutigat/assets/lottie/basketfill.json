{"v": "5.8.1", "fr": 60, "ip": 400, "op": 490, "w": 430, "h": 430, "nm": "139-basket-outline", "ddd": 0, "assets": [{"id": "comp_0", "nm": "in-oscillate", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.145], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [39]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [-9]}, {"t": 63, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.145, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [207.804, 43.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.509, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [225.804, 90.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 56, "s": [215.804, 80.506, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.083], [9.761, 0], [0, 9.083], [-9.761, 0]], "o": [[0, 9.083], [-9.761, 0], [0, -9.083], [9.761, 0]], "v": [[17.673, 0], [0, 16.446], [-17.673, 0], [0, -16.446]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.804, 243.653], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.325, 67.762], [3.345, 67.762]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.335, 67.782], [-3.345, 67.782]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [0.999, 48.119]], "c": false}]}, {"t": 26, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [1.535, -71.984]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.774, 164.897], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.414], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [54]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 69, "s": [9]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [-11.443, 0.236], [0, 0], [0.022, 13.463], [0, 0]], "o": [[0, 0], [0.041, 12.787], [0, 0], [11.073, 0], [0, 0], [0, 0]], "v": [[-20.325, -86.495], [-19.688, -15.55], [0.693, 8.131], [1.005, 8.398], [20.432, -17.197], [20.1, -87.213]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, -7.12], [0, 0], [7.119, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.119, 0]], "v": [[32.4, -8.5], [32.4, 8.7], [19.5, 21.6], [-19.5, 21.5], [-32.4, 8.6], [-32.4, -8.6], [-19.5, -21.5], [19.5, -21.4]], "c": true}]}, {"t": 39, "s": [{"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.041, -34.447], [-0.032, -26.009]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-53.898, -34.447], [-42.822, 53.991]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.083, -54.739], [-0.064, -25.149]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-110, -54.739], [-83.874, 54.851]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -24.076], [0, -25.265]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.35, -24.076], [-0.049, 54.735]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.041, -36.206], [0.033, -25.265]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[54.1, -36.206], [43.178, 54.735]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 1, "k": [{"i": {"x": 0.269, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.083, -54.851], [0.064, -25.304]], "c": false}]}, {"t": 39, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110, -54.851], [84.127, 54.696]], "c": false}]}], "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [252.083, 315.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "hover-oscillate-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.334], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.452], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [4]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 38, "s": [-8]}, {"t": 65, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.145, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [215.804, 80.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.509, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [225.804, 90.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 58, "s": [215.804, 80.506, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.083], [9.761, 0], [0, 9.083], [-9.761, 0]], "o": [[0, 9.083], [-9.761, 0], [0, -9.083], [9.761, 0]], "v": [[17.673, 0], [0, 16.446], [-17.673, 0], [0, -16.446]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.804, 243.653], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.325, 67.762], [3.345, 67.762]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.335, 67.782], [-3.345, 67.782]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [1.535, -71.984]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.774, 164.897], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.449], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.198], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [5]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 71, "s": [9]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-53.898, -34.447], [-42.822, 53.991]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-110, -54.739], [-83.874, 54.851]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.35, -24.076], [-0.049, 54.735]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[54.1, -36.206], [43.178, 54.735]], "c": false}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110, -54.851], [84.127, 54.696]], "c": false}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [252.083, 315.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "hover-oscillate-full", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.334], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.452], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [4]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [-9]}, {"t": 54, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.145, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [215.804, 80.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.509, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [225.804, 90.506, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 47, "s": [215.804, 80.506, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.083], [9.761, 0], [0, 9.083], [-9.761, 0]], "o": [[0, 9.083], [-9.761, 0], [0, -9.083], [9.761, 0]], "v": [[17.673, 0], [0, 16.446], [-17.673, 0], [0, -16.446]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.804, 243.653], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.325, 67.762], [3.345, 67.762]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.335, 67.782], [-3.345, 67.782]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [1.535, -71.984]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.774, 164.897], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.449], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.198], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [5]}, {"i": {"x": [0.509], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 60, "s": [9]}, {"t": 79, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-53.898, -34.447], [-42.822, 53.991]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-110, -54.739], [-83.874, 54.851]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.35, -24.076], [-0.049, 54.735]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[54.1, -36.206], [43.178, 54.735]], "c": false}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110, -54.851], [84.127, 54.696]], "c": false}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [252.083, 315.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Elements", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[61.717, -92.75], [108.093, -122.73], [142.437, -62.853]], "c": false}]}, {"i": {"x": 0.42, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[78.717, -62.25], [125.093, -92.23], [142.437, -62.853]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.58, "y": 0}, "t": 40, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[53.217, -104.25], [99.593, -134.23], [142.437, -62.853]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[78.717, -62.25], [125.093, -92.23], [142.437, -62.853]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 78, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[56.602, -95.006], [102.979, -124.986], [142.437, -62.853]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[61.717, -92.75], [108.093, -122.73], [142.437, -62.853]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-17.977, -115.714], [28.986, -144.218], [78.058, -61.615]], "c": false}]}, {"i": {"x": 0.42, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-0.977, -85.214], [45.986, -113.718], [78.058, -61.615]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.58, "y": 0}, "t": 36, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-26.477, -127.214], [20.486, -155.718], [78.058, -61.615]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-0.977, -85.214], [45.986, -113.718], [78.058, -61.615]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 72, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.091, -117.97], [23.871, -146.473], [78.058, -61.615]], "c": false}]}, {"t": 86, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-17.977, -115.714], [28.986, -144.218], [78.058, -61.615]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-146.2, -94.183], [-45.687, -158.764], [13.059, -61.649]], "c": false}]}, {"i": {"x": 0.42, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-129.2, -63.683], [-28.687, -128.264], [13.059, -61.649]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.58, "y": 0}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-154.7, -105.683], [-54.187, -170.264], [13.059, -61.649]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 51, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-129.2, -63.683], [-28.687, -128.264], [13.059, -61.649]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-151.315, -96.439], [-50.802, -161.02], [13.059, -61.649]], "c": false}]}, {"t": 80, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-127.641, -62.879], [-146.2, -94.183], [-45.687, -158.764], [13.059, -61.649]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_3", "nm": "loop-oscillate-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0.308]}, "t": 0, "s": [10.509]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [-10]}, {"i": {"x": [0.667], "y": [0.365]}, "o": {"x": [0.333], "y": [0]}, "t": 84, "s": [12]}, {"t": 90, "s": [10.509]}], "ix": 10}, "p": {"a": 0, "k": [215.804, 80.506, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.083], [9.761, 0], [0, 9.083], [-9.761, 0]], "o": [[0, 9.083], [-9.761, 0], [0, -9.083], [9.761, 0]], "v": [[17.673, 0], [0, 16.446], [-17.673, 0], [0, -16.446]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.804, 243.653], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.325, 67.762], [3.345, 67.762]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.335, 67.782], [-3.345, 67.782]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [1.535, -71.984]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.774, 164.897], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [21]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [-21]}, {"t": 90, "s": [21]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-53.898, -34.447], [-42.822, 53.991]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-110, -54.739], [-83.874, 54.851]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.35, -24.076], [-0.049, 54.735]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[54.1, -36.206], [43.178, 54.735]], "c": false}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110, -54.851], [84.127, 54.696]], "c": false}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [252.083, 315.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_4", "nm": "morph-fill", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Handle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-1]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [215.804, 80.506, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.083], [9.761, 0], [0, 9.083], [-9.761, 0]], "o": [[0, 9.083], [-9.761, 0], [0, -9.083], [9.761, 0]], "v": [[17.673, 0], [0, 16.446], [-17.673, 0], [0, -16.446]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.804, 243.653], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.325, 67.762], [3.345, 67.762]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.335, 67.782], [-3.345, 67.782]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.208, 71.984], [1.535, -71.984]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250.774, 164.897], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Main 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-3]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-53.898, -34.447], [-42.822, 53.991]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-110, -54.739], [-83.874, 54.851]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.35, -24.076], [-0.049, 54.735]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[54.1, -36.206], [43.178, 54.735]], "c": false}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[110, -54.851], [84.127, 54.696]], "c": false}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [252.083, 315.94], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-3]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-11.866, 0], [0, 0], [-3.285, 11.402], [0, 0]], "o": [[0, 0], [3.398, 11.369], [0, 0], [11.866, 0], [0, 0], [0, 0]], "v": [[-169.85, -87.243], [-123.534, 67.718], [-95.877, 88.318], [97.455, 88.318], [124.908, 67.658], [169.85, -88.318]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [250, 320.447], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.12], [0, 0], [7.12, 0], [0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0]], "o": [[0, 0], [0, 7.12], [0, 0], [-7.12, 0], [0, 0], [0, -7.12], [0, 0], [7.12, 0]], "v": [[184.9, -8.6], [184.9, 8.6], [172, 21.5], [-172, 21.5], [-184.9, 8.6], [-184.9, -8.6], [-172, -21.5], [172, -21.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [250, 210.629], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "elements", "tt": 2, "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 74, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Elements", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -2.592, "ix": 10}, "p": {"a": 0, "k": [251.737, 250.022, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[62.077, -92.731], [118.535, -118.786], [146.635, -54.496]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[61.717, -92.75], [108.093, -122.73], [142.437, -62.853]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-13.47, -114.928], [38.921, -141.511], [79.591, -56.049]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-17.977, -115.714], [28.986, -144.218], [78.058, -61.615]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-126.343, -71.253], [-143.565, -101.689], [-39.595, -160.848], [13.501, -60.745]], "c": false}]}, {"t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-130.434, -67.51], [-146.2, -94.183], [-45.687, -158.764], [13.059, -61.649]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 73, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_5", "nm": "elements", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-1]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [215.804, 80.506, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 115.506, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "Main", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [-9]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [-3]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [250.804, 243.653, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.804, 243.653, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "element-2", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-37]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [-27]}, {"t": 79, "s": [-33]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [184.397, -99.312, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [184.397, 221.688, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [184.397, 157.188, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 68, "s": [184.397, 171.188, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-58.103, -109.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.025, -53.369]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "mask-1", "parent": 2, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-37]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 54, "s": [-27]}, {"t": 79, "s": [-33]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [184.397, -99.312, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [184.397, 221.688, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [184.397, 157.188, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 68, "s": [184.397, 171.188, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-58.103, -109.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.025, -53.369]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "element-3", "parent": 2, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [-37]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [-27]}, {"t": 86, "s": [-33]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [260.397, -84.312, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [260.397, 236.688, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [260.397, 172.188, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 75, "s": [260.397, 186.188, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-58.103, -109.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.025, -53.369]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask-2", "parent": 2, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 33, "s": [-37]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [-27]}, {"t": 86, "s": [-33]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [260.397, -84.312, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 27, "s": [260.397, 236.688, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [260.397, 172.188, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 75, "s": [260.397, 186.188, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-58.103, -109.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.025, -53.369]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "element-1", "parent": 2, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [-37]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 67, "s": [-27]}, {"t": 89, "s": [-33]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [338.397, -63.312, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 33, "s": [338.397, 257.688, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 56, "s": [338.397, 193.188, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 81, "s": [338.397, 207.188, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-58.103, -109.312, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.025, -53.369]], "c": true}]}, {"t": 35, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.713, -166.125], [-117.722, -166.049], [-117.919, -52.933], [-1.907, -66.793]], "c": true}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('139-basket-outline').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12.642, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('139-basket-outline').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 89, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@QvH7u/yISyyA/1/aXyYgBQ", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@QvH7u/yISyyA/1/aXyYgBQ-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.008, 0.467, 0.51], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0], "ix": 1}}]}], "ip": 0, "op": 491, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "in-oscillate", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-oscillate-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 100, "op": 200, "st": 100, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "hover-oscillate-full", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 200, "op": 300, "st": 200, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "loop-oscillate-empty", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 300, "op": 400, "st": 300, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "morph-fill", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 400, "op": 500, "st": 400, "bm": 0}], "markers": [{"tm": 0, "cm": "in-oscillate", "dr": 90}, {"tm": 100, "cm": "hover-oscillate-empty", "dr": 90}, {"tm": 200, "cm": "hover-oscillate-full", "dr": 90}, {"tm": 300, "cm": "loop-oscillate-empty", "dr": 90}, {"tm": 400, "cm": "default:morph-fill", "dr": 90}]}