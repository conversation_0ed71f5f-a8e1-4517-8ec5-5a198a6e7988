{"v": "5.8.1", "fr": 60, "ip": 0, "op": 90, "w": 430, "h": 430, "nm": "wired-outline-298-coins", "ddd": 0, "assets": [{"id": "comp_1", "nm": "Coin-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Coin-Main 4", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 30, "s": [0], "h": 1}, {"t": 120, "s": [100], "h": 1}, {"t": 330, "s": [0], "h": 1}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"t": 135, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 327, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 333, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"t": 360, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [0]}, {"t": 131, "s": [0], "h": 1}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 338, "s": [0]}, {"t": 360, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [54]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [50]}, {"t": 131, "s": [54], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [54]}, {"t": 360, "s": [50]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [270]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [270]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [278]}, {"t": 131, "s": [270], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [278]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [270]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [270]}, {"t": 360, "s": [278]}], "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Coin-Main 6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 30, "s": [0], "h": 1}, {"t": 32, "s": [100], "h": 1}, {"t": 120, "s": [100], "h": 1}, {"t": 135, "s": [0], "h": 1}, {"t": 300, "s": [100], "h": 1}, {"t": 330, "s": [0], "h": 1}, {"t": 332, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 300, "s": [90], "h": 1}], "ix": 10}, "p": {"a": 0, "k": [136, 136, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, -23.787], [84.2, -27.117], [0, -30.447], [-84.2, -27.117]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, 31.213], [84.2, 27.883], [0, 24.553], [-84.2, 27.883]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"t": 135, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 329, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, -23.787], [84.2, -27.117], [0, -30.447], [-84.2, -27.117]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-46.502, 0], [0, 0.138], [46.502, 0], [0, -0.138]], "o": [[46.502, 0], [0, -0.138], [-46.502, 0], [0, 0.138]], "v": [[0, -27.9], [84.2, -28.15], [0, -28.4], [-84.2, -28.15]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 331, "s": [{"i": [[-46.502, 0], [0, 1.839], [46.502, 0], [0, -1.839]], "o": [[46.502, 0], [0, -1.839], [-46.502, 0], [0, 1.839]], "v": [[0, 31.213], [84.2, 27.883], [0, 24.553], [-84.2, 27.883]], "c": true}]}, {"t": 360, "s": [{"i": [[-46.502, 0], [0, 46.502], [46.502, 0], [0, -46.502]], "o": [[46.502, 0], [0, -46.502], [-46.502, 0], [0, 46.502]], "v": [[0, 84.2], [84.2, 0], [0, -84.2], [-84.2, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.008, 0.467, 0.51, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 555, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Coin-Main 10", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 360, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 773, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Coin-Main 7", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"t": 135, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 327, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[-0.146, -27.915], [92.221, -27.424], [117.65, -0.043], [92.383, -27.363], [-0.092, -27.897], [-91.912, -27.736], [-119.4, 0], [-92.423, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 333, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[-0.131, -13.184], [93.131, -13.709], [117.825, -0.039], [93.155, -30.557], [-0.083, -37.048], [-94.025, -30.538], [-119.4, 0], [-94.049, -13.617]], "c": true}]}, {"t": 360, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [0]}, {"t": 131, "s": [0], "h": 1}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 338, "s": [0]}, {"t": 360, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [50]}, {"t": 131, "s": [54], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [54]}, {"t": 360, "s": [50]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [90]}, {"t": 131, "s": [82], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [82]}, {"t": 360, "s": [90]}], "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Coin-Main 9", "parent": 2, "sr": 1, "ks": {"o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 120, "s": [0], "h": 1}, {"t": 135, "s": [100], "h": 1}, {"t": 300, "s": [0], "h": 1}, {"t": 330, "s": [100], "h": 1}], "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"t": 135, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 327, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 333, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"t": 360, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 338, "s": [0]}, {"t": 360, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [54]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [50]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [54]}, {"t": 360, "s": [50]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [245]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [253]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [245]}, {"t": 360, "s": [253]}], "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 30, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Coin-Main 5", "parent": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 60, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 120, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"t": 135, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 300, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 327, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 330, "s": [{"i": [[-47.636, 0], [-17.496, 0], [0, -14.378], [14.467, 0.294], [47.135, 0], [15.037, 0], [0, -13.844], [-17.924, 0.063]], "o": [[46.465, 0], [13.879, 0], [0, -14.132], [-17.979, -0.365], [-48.094, 0], [-18.488, 0], [0, -13.919], [17.798, -0.062]], "v": [[1.604, -27.915], [93.971, -27.424], [119.4, -0.043], [94.133, -27.363], [-0.092, -27.897], [-90.162, -27.736], [-117.65, 0], [-90.673, -27.713]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 333, "s": [{"i": [[-45.712, 0], [-18.061, -0.92], [0, -9.464], [18.049, 5.248], [45.712, 0], [18.401, -5.258], [0, -16.018], [-18.389, 0.954]], "o": [[45.712, 0], [18.061, 0.92], [0, -16.018], [-18.049, -5.248], [-45.712, 0], [-18.401, 5.258], [0, -9.367], [18.389, -0.954]], "v": [[0.869, -13.184], [94.131, -13.709], [118.825, -0.039], [94.155, -30.557], [0.917, -37.048], [-93.025, -30.538], [-118.4, 0], [-93.049, -13.617]], "c": true}]}, {"t": 360, "s": [{"i": [[-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607], [32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607]], "o": [[32.971, 0], [21.607, -21.607], [0, -32.971], [-21.607, -21.607], [-32.971, 0], [-21.607, 21.607], [0, 32.971], [21.607, 21.607]], "v": [[0, 119.4], [84.429, 84.429], [119.4, 0], [84.429, -84.429], [0, -119.4], [-84.429, -84.429], [-119.4, 0], [-84.429, 84.429]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 38, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [0]}, {"t": 131, "s": [0], "h": 1}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 338, "s": [0]}, {"t": 360, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [50]}, {"t": 131, "s": [54], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [54]}, {"t": 360, "s": [50]}], "ix": 2}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 60, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [90]}, {"t": 131, "s": [82], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 300, "s": [90]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 322, "s": [82]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 338, "s": [82]}, {"t": 360, "s": [90]}], "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 361, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "Source-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.074]}, "t": 50, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 60.436, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74.783, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 89, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 107.285, "s": [-3]}, {"t": 113, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [210.5, 493, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.942}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [208.5, 48, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 89, "s": [211.5, 99, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [208.5, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 107.285, "s": [206.5, 105, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 113, "s": [208.5, 110, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 50, "op": 114, "st": -70, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "source-3-empty", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 64, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask 3", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.5, 136.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[-65.943, 0], [0, 38], [65.943, 0], [0, -40]], "o": [[65.943, 0], [0, -41.364], [-65.943, 0], [0, 36.5]], "v": [[0, 37.736], [119.4, 0], [0, -37.357], [-119.4, 0]], "c": true}]}, {"t": 65, "s": [{"i": [[-65.943, 0], [0, 40], [65.943, 0], [0, -42]], "o": [[65.943, 0], [0, -38], [-65.943, 0], [0, 40]], "v": [[0, 29.4], [119.4, 0], [0, -28.4], [-119.4, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 50, "op": 64, "st": -70, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "source-3-empty", "tt": 2, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 50, "op": 64, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "source-3-empty", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Rectangle 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Rectangle 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.5, 221, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.5, 165, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 89, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Rectangle 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206.5, 109, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 113, "op": 900, "st": 0, "bm": 0}]}, {"id": "comp_3", "nm": "source-3-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [0.82]}, "o": {"x": [0.239], "y": [0.07]}, "t": 31, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [-0.067]}, "t": 40.082, "s": [-20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52.568, "s": [15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57.107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 65.055, "s": [-7]}, {"t": 73, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [155.5, 549, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 48.027, "s": [153.5, 124, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 57.107, "s": [153.5, 166, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.484}, "o": {"x": 0.333, "y": 0}, "t": 65.055, "s": [151.5, 160, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 73, "s": [153.5, 166, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 31, "op": 90, "st": -89, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "source-2-empty", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 45, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask 2", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [136.5, 136.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 31, "s": [{"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[-65.943, 0], [0, 38], [65.943, 0], [0, -40]], "o": [[65.943, 0], [0, -41.364], [-65.943, 0], [0, 36.5]], "v": [[0, 37.736], [119.4, 0], [0, -37.357], [-119.4, 0]], "c": true}]}, {"t": 46, "s": [{"i": [[-65.943, 0], [0, 40], [65.943, 0], [0, -42]], "o": [[65.943, 0], [0, -38], [-65.943, 0], [0, 40]], "v": [[0, 29.4], [119.4, 0], [0, -28.4], [-119.4, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 31, "op": 45, "st": -89, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "source-2-empty", "tt": 2, "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 31, "op": 45, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "source-2-empty", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 31, "st": 0, "bm": 0}]}, {"id": "comp_4", "nm": "source-2-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 14, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 22, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [-5]}, {"t": 48, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [208.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.911}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [206.5, 180, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [206.5, 222, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 43, "s": [203.5, 213, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 48, "s": [206.5, 222, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 14, "op": 90, "st": -106, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 28, "op": 90, "st": -120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [9.515]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [9.515]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [22.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [16.515]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [14.015]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [9.515]}, {"t": 27, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [207.606, 395.639, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.567, "y": 0.63}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [207.606, 395.639, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.633, "y": 0.668}, "o": {"x": 0.281, "y": 0.354}, "t": 18, "s": [207.633, 342.881, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.663}, "o": {"x": 0.318, "y": 0.52}, "t": 20, "s": [207.681, 271.293, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.675, "y": 0.731}, "o": {"x": 0.34, "y": 0.635}, "t": 23, "s": [205.947, 212.108, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.683, "y": 1}, "o": {"x": 0.348, "y": 0.396}, "t": 24, "s": [205.416, 201.405, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [204.606, 193.639, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 27, "s": [204.106, 182.139, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [{"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [{"i": [[-65.943, 0], [0, 38], [65.943, 0], [0, -40]], "o": [[65.943, 0], [0, -41.364], [-65.943, 0], [0, 36.5]], "v": [[0, 37.736], [119.4, 0], [0, -37.357], [-119.4, 0]], "c": true}]}, {"t": 29, "s": [{"i": [[-65.943, 0], [0, 40], [65.943, 0], [0, -42]], "o": [[65.943, 0], [0, -38], [-65.943, 0], [0, 40]], "v": [[0, 29.4], [119.4, 0], [0, -28.4], [-119.4, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 17, "op": 28, "st": -106, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Coin-empty", "tt": 2, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 17, "op": 28, "st": -120, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.496], "y": [1.187]}, "o": {"x": [0.239], "y": [-0.073]}, "t": 0, "s": [0]}, {"i": {"x": [0.558], "y": [1]}, "o": {"x": [0.24], "y": [0.071]}, "t": 8, "s": [19.181]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19, "s": [-14]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [9]}, {"t": 33, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.187, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [183.5, 570, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.929}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [181.5, 236, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [181.5, 278, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 28, "s": [188.5, 263, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 33, "s": [181.5, 278, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 0, "op": 17, "st": -120, "bm": 0}]}, {"id": "comp_6", "nm": "hover-spending", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [-180]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [-164.386]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [-111.021]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [-11]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.29, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [215, 592, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.71, "y": 0}, "t": 39, "s": [219, 78.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.497, "y": 0.866}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [234.5, 254, 0], "to": [0, 0, 0], "ti": [-14.016, -0.461, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.606, "y": 0.141}, "t": 70, "s": [252.167, 185.5, 0], "to": [10.238, 0.337, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [265.5, 250, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [275.5, 250, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 18, "op": 138, "st": 18, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Coin-empty", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 30, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [274.4, 249.4, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 30, "s": [573.4, 249.4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [136.5, 136.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 273, "h": 273, "ip": 0, "op": 540, "st": -360, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "mask-empty", "td": 1, "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "stack-empty", "tt": 2, "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 900, "st": 0, "bm": 0}]}, {"id": "comp_7", "nm": "mask-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [274.4, 249.4, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 30, "s": [573.4, 249.4, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [-180]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [-164.386]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [-111.021]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [-11]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.29, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [215, 592, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.71, "y": 0}, "t": 39, "s": [219, 78.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.497, "y": 0.866}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [234.5, 254, 0], "to": [0, 0, 0], "ti": [-14.016, -0.461, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.606, "y": 0.141}, "t": 70, "s": [252.167, 185.5, 0], "to": [10.238, 0.337, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [265.5, 250, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [275.5, 250, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 18, "s": [{"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[-65.943, 0], [0, 38], [65.943, 0], [0, -40]], "o": [[65.943, 0], [0, -41.364], [-65.943, 0], [0, 36.5]], "v": [[0, 37.736], [119.4, 0], [0, -37.357], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [{"i": [[-65.943, 0], [0, 40], [65.943, 0], [0, -42]], "o": [[65.943, 0], [0, -38], [-65.943, 0], [0, 40]], "v": [[0, 29.4], [119.4, 0], [0, -28.4], [-119.4, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [{"i": [[-65.943, 0], [0, 38.5], [65.943, 0], [0, -41.5]], "o": [[65.943, 0], [0, -41.364], [-65.943, 0], [0, 38]], "v": [[0, 40.236], [119.4, 0], [0, -37.857], [-119.4, 0]], "c": true}]}, {"t": 78, "s": [{"i": [[-65.943, 0], [0, 65.943], [65.943, 0], [0, -65.943]], "o": [[65.943, 0], [0, -65.943], [-65.943, 0], [0, 65.943]], "v": [[0, 119.4], [119.4, 0], [0, -119.4], [-119.4, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.070588238537, 0.074509806931, 0.192156866193, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 18, "op": 330, "st": 18, "bm": 0}]}, {"id": "comp_8", "nm": "stack-empty", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Rectangle 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [180.5, 277, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 4", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.5, 221, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Rectangle 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [152.5, 165, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Rectangle 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [206.5, 109, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [237, 56], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 28, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-outline-298-coins').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-outline-298-coins').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@Fot544wnTSSwjCPjYfhjQA", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@Fot544wnTSSwjCPjYfhjQA-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0, 0, 0], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.008, 0.467, 0.51], "ix": 1}}]}], "ip": 0, "op": 421, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "hover-spending", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-spending", "dr": 90}]}