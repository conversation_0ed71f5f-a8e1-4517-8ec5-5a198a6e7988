@extends('backoffice.layouts.layout')
@section('title', 'User Details')
@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h2 class="mb-0 font-weight-bold text-primary">User Details</h2>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Dashboard</a></li>
                                <li class="breadcrumb-item active">User Details</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <!-- Back Button -->
            <div class="row mb-4">
                <div class="col-12">
                    <a href="{{ route('users.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Users
                    </a>
                </div>
            </div>
            <!-- end back button -->

            <!-- User Details -->
            <div class="row">
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h4 class="card-title text-primary mb-4">{{ $user->firstname }} {{ $user->lastname }}</h4>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold">Phone number</span>
                                    <span>{{ $user->phone }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <span class="font-weight-bold"> Subscribed At</span>
                                    <span>{{ $user->created_at->format('Y-m-d H:i') }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    @if($user->has_store)
                        <div class="card border-0 shadow-sm mt-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h4 class="card-title text-primary mb-4">Store Details</h4>
                                    <a href="{{ route('stores.show', $user->store->id) }}" class="btn btn-primary btn-sm">Show</a>
                                </div>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span class="font-weight-bold">Store Name</span>
                                        <span>{{ $user->store->name }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span class="font-weight-bold">Store Type</span>
                                        <span>{{ $user->store->type->name_en }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span class="font-weight-bold">Opening Time</span>
                                        <span>{{ $user->store->opening_time }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span class="font-weight-bold">Closing Time</span>
                                        <span>{{ $user->store->closing_time }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                        <span class="font-weight-bold">Store Address</span>
                                        <span>{{ $user->store->address ?: 'To be defined' }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h4 class="card-title text-primary mb-4">User Posts</h4>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th>First image</th>
                                        <th>Title</th>
                                        <th>Price</th>
                                        <th>Qty</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($user->items as $item)
                                        <tr>
                                            <td>
                                                <img src="{{ $item->images->first() ? $item->images->first()->url : null }}"
                                                     alt="First Image" class="img-fluid" style="max-width: 80px;">
                                            </td>
                                            <td>{{ $item->title }}</td>
                                            <td>{{ $item->price }}</td>
                                            <td>{{ $item->quantity }}</td>
                                            <td>
                                                <a href="{{ route('items.show', $item->id) }}" class="btn btn-primary btn-sm">Show</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End User Details -->
        </div>
    </div>
@endsection
