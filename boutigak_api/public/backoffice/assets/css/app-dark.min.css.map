{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_topbar.scss", "app-dark.css", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_color-picker.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_round-slider.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_toastr.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_datatable.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_x-editable.scss", "custom/pages/_email.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss"], "names": [], "mappings": "AAKQ,2FAER,WACI,YAAA,MACA,WAAA,OACA,YAAA,IACA,aAAA,KACA,IAAA,uCAAA,eAAA,CAAA,sCAAA,eAIJ,WACI,YAAA,MACA,WAAA,OACA,YAAA,IACA,aAAA,KACA,IAAA,yCAAA,eAAA,CAAA,wCAAA,eAIJ,WACI,YAAA,MACA,WAAA,OACA,YAAA,IACA,aAAA,KACA,IAAA,wCAAA,eAAA,CAAA,uCAAA,eAIJ,WACI,YAAA,MACA,WAAA,OACA,YAAA,IACA,aAAA,KACA,IAAA,sCAAA,eAAA,CAAA,oCAAA,eCnCJ,aACI,SAAA,MACA,IAAA,EACA,MAAA,EACA,KAAA,EACA,QAAA,KACA,iBAAA,QACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBAGJ,eACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,QACA,iBAAA,QAAA,gBAAA,cACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,OAAA,EAAA,KACA,OAAA,KACA,QAAA,EAAA,eAAA,EAAA,EAGI,2CACI,iBAAA,QAKZ,kBACI,QAAA,EAAA,OACA,WAAA,OACA,MAAA,MAGJ,MACI,YAAA,KAEA,eACI,QAAA,KAIR,WACI,QAAA,KAGJ,YACI,QAAA,MAKJ,YACI,QAAA,eAAA,EAEA,0BACI,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,KACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAEJ,iBACI,SAAA,SACA,QAAA,GACA,UAAA,KACA,YAAA,KACA,KAAA,KACA,IAAA,EACA,MAAA,QAOJ,kBACI,SAAA,SACA,QAAA,IAAA,EACA,oBACI,MAAA,QAKZ,yBACI,kBACI,MAAA,KAMA,mBACI,QAAA,KAGJ,mBACI,QAAA,cAKZ,cACI,QAAA,kBAAA,eAAA,KAAA,eAGJ,aACI,OAAA,KACA,mBAAA,eAAA,WAAA,eACA,MAAA,QACA,OAAA,EACA,cAAA,EAEA,mBACI,MAAA,QAMR,qBACI,OAAA,KACA,MAAA,KACA,iBAAA,QACA,QAAA,IAKI,gCACI,QAAA,aAMR,aACI,UAAA,KACA,MAAA,QAGJ,qBACI,SAAA,SACA,QAAA,aACA,OAAA,IACA,MAAA,IACA,iBAAA,QACA,cAAA,IACA,IAAA,KACA,MAAA,KAKJ,2BACI,QAAA,OAAA,KAEA,iCACI,iBAAA,QAMZ,oBACI,QAAA,MACA,cAAA,IACA,YAAA,KACA,WAAA,OACA,QAAA,KAAA,EAAA,IACA,QAAA,MACA,OAAA,IAAA,MAAA,YACA,MAAA,QAEA,wBACI,OAAA,KAGJ,yBACI,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OAGJ,0BACI,aAAA,QAOA,uEACI,QAAA,QAMR,oCACI,iBAAA,QAII,kEACI,iBAAA,sBAIR,kEACI,WAAA,qBAIR,oCACI,MAAA,QAEA,0CACI,MAAA,QAIR,4CACI,iBAAA,sBAIA,oCACI,MAAA,QAIR,kCACI,QAAA,KAGJ,mCACI,QAAA,MAKA,iDACI,iBAAA,mBACA,MAAA,KCZZ,iFDcQ,wCAEI,MAAA,qBAOR,0CACI,WAAA,QAGJ,mCACI,QAAA,KAGJ,oCACI,QAAA,MAIR,yBAEQ,yBACI,SAAA,OAEA,wCACI,KAAA,eACA,MAAA,gBAMhB,yBACI,kBACI,QAAA,MAKJ,+CACI,MAAA,KAEJ,2CACI,WAAA,KACA,QAAA,kBAAA,eAAA,KAAA,eAIR,yBAEQ,2CACI,WAAA,MAQR,uCACI,iBAAA,QAII,qEACI,iBAAA,sBAIR,qEACI,WAAA,qBAIR,uCACI,MAAA,QAEA,6CACI,MAAA,QAIR,+CACI,iBAAA,sBAIA,uCACI,MAAA,QAIR,qCACI,QAAA,KAGJ,sCACI,QAAA,MAKA,oDACI,iBAAA,mBACA,MAAA,KChDZ,oFDkDQ,2CAEI,MAAA,qBEvWZ,gBACI,eAAA,KAEA,4BACI,iBAAA,YACA,QAAA,EAGJ,mBACI,UAAA,KACA,eAAA,UACA,YAAA,ICXR,QACI,OAAA,EACA,QAAA,KAAA,eACA,SAAA,SACA,MAAA,EACA,MAAA,QACA,KAAA,MACA,OAAA,KACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBACA,iBAAA,QAGJ,yBACI,QACI,KAAA,GAMJ,2BACI,KAAA,KAKJ,qCACI,KAAA,YC3BR,WACI,iBAAA,QACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAEA,6BACI,iBAAA,KACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,QACA,WAAA,OACA,cAAA,IAEA,mCACI,iBAAA,KAMZ,kBACI,iBAAA,sBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAIA,8BACI,MAAA,EAEJ,qCACI,QAAA,MC0BJ,4BDrBA,WACI,SAAA,KACA,4BACI,OAAA,gBEtDZ,WACI,OAAA,EAEA,cACI,QAAA,MACA,MAAA,KAGJ,wBACI,QAAA,KAEA,sCACI,QAAA,KAGJ,gCACI,QAAA,MAIR,0BACI,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAKR,eACI,MAAA,MACA,QAAA,KACA,WAAA,QACA,OAAA,EACA,WAAA,EACA,SAAA,MACA,IAAA,KACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBAGJ,cACI,YAAA,MACA,SAAA,OAEA,uBACI,QAAA,EAAA,KAAA,KAAA,KACA,WAAA,KAKR,cACI,QAAA,KAAA,EAAA,KAAA,EAIQ,0CACI,kBAAA,gBAAA,UAAA,gBAMR,+BACI,QAAA,SACA,YAAA,wBACA,QAAA,MACA,MAAA,MACA,mBAAA,kBAAA,IAAA,WAAA,kBAAA,IAAA,WAAA,UAAA,IAAA,WAAA,UAAA,GAAA,CAAA,kBAAA,IACA,UAAA,KAMA,sBACI,QAAA,MACA,QAAA,QAAA,OACA,MAAA,QACA,SAAA,SACA,UAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,YAAA,KAAA,CAAA,WACA,YAAA,IAEA,wBACI,QAAA,aACA,UAAA,OACA,eAAA,OACA,UAAA,OACA,YAAA,WACA,eAAA,OACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,QAAA,IAGJ,4BACI,MAAA,QAEA,8BACI,MAAA,QAKZ,2BACI,WAAA,IAGJ,gCACI,QAAA,EAII,qCACI,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,KACA,MAAA,QAGJ,+CACI,QAAA,EAGI,oDACI,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,OAWhC,YACI,QAAA,KAAA,eACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,KACA,eAAA,UACA,MAAA,QACA,YAAA,IACA,YAAA,KAAA,CAAA,WACA,QAAA,GAIJ,WACI,MAAA,kBACA,aACI,MAAA,kBACA,eACI,MAAA,kBAGR,aACI,MAAA,kBAEJ,mBACI,MAAA,kBAEA,qBACI,MAAA,kBAKZ,yBACI,eACI,QAAA,KAGJ,cACI,YAAA,YAIA,mCACI,QAAA,OAQR,iCACI,YAAA,KAGJ,qCACI,MAAA,eAIA,sCACI,QAAA,KAGJ,sCACI,QAAA,MAKR,kCACI,SAAA,SACA,MAAA,eACA,QAAA,ELiXR,6DK/WQ,kDAEI,SAAA,kBAGJ,uDACI,QAAA,eAGJ,oDACI,OAAA,YL+WZ,uDACA,6DK1WY,4DAGI,QAAA,eAGJ,8DACI,OAAA,kBAIA,iEACI,QAAA,KAKJ,sDACI,SAAA,SACA,YAAA,OAEA,wDACI,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KAEA,+DAAA,8DAAA,8DAGI,MAAA,QAGJ,0DACI,UAAA,KACA,YAAA,IAGJ,6DACI,QAAA,KACA,aAAA,KAKJ,8DACI,SAAA,SACA,MAAA,mBACA,MAAA,QACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KAEA,gEACI,MAAA,QAGJ,mEACI,QAAA,OAIR,+DACI,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,KAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,KAAA,kBAEA,kEACI,mBAAA,IAAA,IAAA,KAAA,KAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,KAAA,kBAGJ,iEACI,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EACA,MAAA,QAEA,uEACI,MAAA,QAOpB,sDACI,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,QAIQ,kEACI,QAAA,MACA,KAAA,MACA,OAAA,eACA,WAAA,MACA,SAAA,SACA,MAAA,MAKJ,2EACI,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eAMR,kEACI,MAAA,QAcpB,yCACI,MAAA,KAEJ,2CACI,MAAA,kBAGR,uCACI,WAAA,QAOQ,8CACI,MAAA,QAEA,gDACI,MAAA,QAGJ,oDACI,MAAA,KAEA,sDACI,MAAA,KAQJ,6DACI,MAAA,QAEA,mEACI,MAAA,KAS5B,0CACI,WAAA,OAYoB,qFACI,WAAA,QACA,MAAA,KACA,uFACI,MAAA,KAKJ,wFACI,MAAA,QACA,8FACI,MAAA,QAOpB,6EACI,iBAAA,QASI,yFACI,MAAA,eAQD,yFAAA,4FACK,MAAA,kBAKJ,yFAAA,4FACI,MAAA,kBAgBhC,mCACI,MAAA,eACA,qCACI,MAAA,eACA,uCACI,MAAA,eAGR,qCACI,MAAA,eAEJ,2CACI,MAAA,eAEA,6CACI,MAAA,eAKZ,oCACI,MAAA,QAMJ,2CACI,YAAA,YAOJ,gDACI,MAAA,MAEA,yBAHJ,gDAIQ,MAAA,MAGR,6CACI,MAAA,MACA,WAAA,OL6PR,oDK3PQ,8DAEI,QAAA,eAGR,4CACI,YAAA,MAEJ,sCACI,KAAA,MACA,yBAFJ,sCAGQ,KAAA,GAMA,6DACI,iBAAA,QAGA,sDACI,QAAA,MAKA,mEACI,aAAA,OAKI,kFACI,aAAA,OASxB,8DACI,YAAA,KAGA,6EACI,WAAA,KAIY,uFACI,QAAA,aAOxB,wDACI,KAAA,KASJ,4CACI,MAAA,KAEJ,8CACI,MAAA,kBAIR,0CACI,WAAA,QAEJ,6CACI,iBAAA,QACA,wDACI,QAAA,KAEJ,yDACI,QAAA,MAOI,0DACI,MAAA,qBAGJ,iDACI,MAAA,qBACA,mDACI,MAAA,qBAGA,4EACE,WAAA,qBAOF,gEACI,MAAA,qBAcJ,wFACI,iBAAA,QACA,MAAA,KACA,0FACI,MAAA,KAWJ,4FACI,MAAA,eAQD,4FAAA,+FACK,MAAA,kBAKJ,4FAAA,+FACI,MAAA,kBAchC,sCACI,MAAA,eACA,wCACI,MAAA,eACA,0CACI,MAAA,eAGR,wCACI,MAAA,eAEJ,8CACI,MAAA,eAEA,gDACI,MAAA,eAKZ,uCACI,MAAA,eAKJ,iCACI,QAAA,KC3tBR,QACI,WAAA,QACA,QAAA,EAAA,eACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBACA,WAAA,KACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,QAAA,IAEA,qBACI,OAAA,EACA,QAAA,EAKA,8BACI,UAAA,KACA,SAAA,SACA,QAAA,KAAA,OACA,MAAA,QACA,YAAA,KAAA,CAAA,WAEA,gCACI,UAAA,KACA,eAAA,OACA,QAAA,aAEJ,oCAAA,oCACI,MAAA,QACA,iBAAA,YAIR,mCACI,MAAA,QACA,0CAAA,yCACI,MAAA,QAKJ,+CACI,MAAA,QAMF,uCACM,MAAA,QACA,iBAAA,YFOhB,0BEGI,8CNk3BR,4CMh3BY,UAAA,KFLR,yBEegB,sDACI,aAAA,EAMhB,uBACI,QAAA,MAAA,OACA,UAAA,MAMI,oDACI,KAAA,EACA,MAAA,KAGR,iCACI,WAAA,EACA,cAAA,EAAA,EAAA,OAAA,OAGI,oDACI,MAAA,KACA,kBAAA,gBAAA,iBAAA,UAAA,gBAAA,iBACA,SAAA,SAKJ,0DACI,SAAA,SACA,IAAA,YACA,KAAA,KACA,QAAA,KAMR,uCACI,QAAA,MAKZ,sEACI,QAAA,MAIR,eACI,QAAA,MAIR,YACI,QAAA,aAEA,kBACI,aAAA,QACA,aAAA,MACA,aAAA,EAAA,EAAA,IAAA,IACA,QAAA,GACA,OAAA,KACA,QAAA,aACA,MAAA,IACA,IAAA,IACA,YAAA,KACA,kBAAA,eAAA,iBAAA,UAAA,eAAA,iBACA,yBAAA,IAAA,iBAAA,IACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,KF7EJ,6BEwFoB,kEACI,MAAA,KACA,KAAA,MF1FxB,4BEsGI,6BACI,QAAA,KACA,0CACI,QAAA,KAIR,8BACI,QAAA,MAIR,QACI,WAAA,MACA,WAAA,KACA,QAAA,EAEI,8BACI,QAAA,OAAA,OAKJ,iCACI,iBAAA,YACA,OAAA,KACA,mBAAA,KAAA,WAAA,KACA,aAAA,KACA,uDACI,MAAA,KAEA,4DACI,OAAA,EAKZ,iCACI,SAAA,SACA,iBAAA,YAEA,wCAAA,wCAEI,MAAA,QAMR,2BACI,MAAA,KACA,SAAA,UFtKZ,yBEiLQ,6EACI,QAAA,KAGJ,8EACI,QAAA,MAIR,wDACI,iBAAA,QAGI,8EACI,MAAA,qBAEA,oFAAA,oFACI,MAAA,qBAMJ,uFACQ,MAAA,gCCpQ5B,6BACI,iBAAA,QACA,6CACI,iBAAA,QACA,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBAGJ,0CACI,UAAA,OACA,OAAA,EAAA,KAGJ,qCACI,OAAA,EAAA,KACA,UAAA,qBAIA,uDACI,UAAA,oBASR,qEAAA,kEAAA,6DACI,UAAA,KAEJ,sEAAA,oEACI,UAAA,OCrCR;;;;;;AAOC,cACG,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cRklCF,cQhlCI,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cRklCF,oBAFA,oBACA,sBQ7kCI,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MAIA,wCACI,iBAAA,qBAKJ,0CACI,iBAAA,oBAIJ,0CACI,iBAAA,qBAIJ,uCACI,iBAAA,mBAIJ,0CACI,iBAAA,oBAIJ,yCACI,iBAAA,mBCjKR,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,QACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,YAAA,IACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAKF,cACE,aAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,iCACE,YAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,uCACE,SAAA,SACA,kBAAA,iBAAA,UAAA,iBC9CE,8BACI,WAAA,MAOI,uDACI,QAAA,SAMhB,+BACI,cAAA,IAKJ,8BACI,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KAEJ,qCACI,aAAA,KACA,SAAA,SAEA,uDACI,SAAA,SACA,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,UAAA,KACA,iBAAA,QACA,MAAA,KACA,cAAA,IACA,WAAA,OACA,KAAA,KACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBAOI,8DACI,QAAA,SCpDpB,uBACI,YAAA,KAAA,CAAA,WAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAOJ,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QACA,WAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,wBACI,MAAA,QACA,iBAAA,QAKR,MACI,UAAA,KAGJ,MACI,UAAA,KAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAKJ,YACI,SAAA,SACA,OAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,QAAA,GACA,iBAAA,KAKJ,QACI,iBAAA,EAAA,SAAA,EAAA,KAAA,EAQA,8BACI,UAAA,KACA,QAAA,QAAA,QACA,WAAA,YAAA,0TAAA,MAAA,CAAA,IAAA,KAAA,UC3HR,WACI,SAAA,MACA,IAAA,EACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,iBAAA,QACA,QAAA,KAGJ,QACI,SAAA,SACA,KAAA,IACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,OAAA,MAAA,EAAA,EAAA,MAIA,oBACI,UAAA,KACA,MAAA,QACA,SAAA,SACA,QAAA,aACA,kBAAA,KAAA,KAAA,SAAA,OAAA,UAAA,KAAA,KAAA,SAAA,OAIR,wBACI,GACE,kBAAA,UAAA,UAAA,UAEF,KACE,kBAAA,eAAA,UAAA,gBALN,gBACI,GACE,kBAAA,UAAA,UAAA,UAEF,KACE,kBAAA,eAAA,UAAA,gBC9BN,kBACE,aAAA,EACA,QAAA,aACA,cAAA,MACA,oCACE,MAAA,MACA,YAAA,EACA,aAAA,OAEF,oCACE,QAAA,MAIJ,YACE,SAAA,SACA,WAAA,KAIF,kBACE,OAAA,QACA,cAAA,ECrBF,cACI,WAAA,IACA,YAAA,KAEA,6BACI,SAAA,SACA,QAAA,EAAA,EAAA,KAAA,KAEA,oCACI,QAAA,GACA,YAAA,IAAA,OAAA,qBACA,SAAA,SACA,KAAA,EACA,OAAA,EACA,IAAA,KAEJ,4CACI,SAAA,SACA,KAAA,MACA,IAAA,EACA,QAAA,EAGJ,wCACI,eAAA,EC3BZ,cACI,YAAA,KACA,cAAA,MAEA,mBACI,cAAA,KACA,YAAA,IAMR,gBACI,UAAA,OAKJ,kBACI,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,KACA,KAAA,KACA,QAAA,EACA,QAAA,MAKJ,mBACE,MAAA,QAEA,qBACE,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,UAAA,KACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,WAAA,OACA,aAAA,KACA,eAAA,OAGF,6BACE,WAAA,KAGE,qCACE,MAAA,KACA,iBAAA,QACA,aAAA,QAUJ,gCACI,iBAAA,QACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KAOR,YACE,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,OACA,QAAA,KACA,SAAA,OACA,cAAA,SACA,YAAA,OACA,QAAA,MAEA,kBACE,OAAA,QAIJ,kBACE,QAAA,MAIF,kBACE,QAAA,KACA,sCACE,aAAA,kBAKF,wBACI,WAAA,KACA,+BACI,MAAA,KACA,OAAA,KACA,YAAA,eACA,UAAA,eACA,cAAA,cACA,iBAAA,+BACA,MAAA,kBACA,OAAA,IAAA,cCnHV,ahBkpDA,QADA,eADA,gBADA,WgB9oDI,eAKI,QAAA,eAEJ,WhB+oDJ,cAEA,cADA,WAEA,KgB7oDQ,QAAA,EACA,OAAA,EAGJ,MACI,OAAA,GCvBR,iBACE,SAAA,SACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACA,mBAAA,MAAA,cAAA,WACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,mBACE,SAAA,OACA,MAAA,QACA,OAAA,QACA,UAAA,QACA,WAAA,QAGF,gBACE,UAAA,QACA,SAAA,SACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,eACA,OAAA,eACA,QAAA,EAGF,kBACE,UAAA,kBACA,mBAAA,kBAAA,WAAA,kBACA,OAAA,eACA,SAAA,SACA,IAAA,EACA,KAAA,YACA,OAAA,EACA,MAAA,YACA,QAAA,EACA,OAAA,EACA,2BAAA,MAGF,2BACE,UAAA,QACA,mBAAA,qBAAA,WAAA,qBACA,SAAA,SACA,QAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,SAAA,KACA,UAAA,KACA,WAAA,KACA,gBAAA,KACA,QAAA,YAGF,8CjBwrDA,6CiBtrDE,QAAA,KjB2rDF,yBiBxrDA,0BAEE,QAAA,IACA,QAAA,MAGF,uBACE,WAAA,KACA,UAAA,KACA,MAAA,KACA,eAAA,KAGF,wCACE,mBAAA,kBAAA,WAAA,kBACA,OAAA,KACA,MAAA,KACA,UAAA,IACA,SAAA,SACA,MAAA,KACA,WAAA,IACA,SAAA,OACA,QAAA,GACA,QAAA,EACA,OAAA,EACA,eAAA,KACA,iBAAA,QAAA,kBAAA,QAAA,UAAA,QACA,kBAAA,EAAA,YAAA,EACA,wBAAA,EAAA,WAAA,EAGF,gCACE,mBAAA,QAAA,WAAA,QACA,QAAA,MACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,KAAA,EACA,OAAA,MACA,MAAA,MACA,WAAA,IACA,UAAA,IACA,SAAA,OACA,eAAA,KACA,QAAA,GAGF,iBACE,QAAA,EACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,eAAA,KACA,SAAA,OAGF,uDACE,eAAA,KACA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,oBAAA,KAGF,qDACE,eAAA,IAGF,qBACE,SAAA,SACA,MAAA,IACA,MAAA,IACA,WAAA,KAGF,4BACE,SAAA,SACA,QAAA,GACA,WAAA,QACA,cAAA,IACA,KAAA,EACA,MAAA,EACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAGF,8CAEE,QAAA,GACA,mBAAA,QAAA,GAAA,OAAA,WAAA,QAAA,GAAA,OAGF,oCACE,IAAA,EACA,MAAA,KAGF,gEACE,IAAA,IACA,OAAA,IAGF,sCACE,KAAA,EACA,OAAA,KAGF,kEACE,OAAA,KACA,KAAA,IACA,MAAA,IAGF,2DACE,MAAA,KACA,KAAA,EACA,IAAA,IACA,OAAA,IACA,WAAA,EACA,UAAA,KACA,MAAA,KAIF,mEACE,MAAA,KACA,KAAA,EAGF,yBACE,UAAA,IACA,SAAA,MACA,QAAA,EACA,WAAA,OACA,OAAA,MACA,MAAA,MACA,WAAA,OACA,WAAA,OAGF,0BACE,SAAA,MACA,KAAA,EACA,WAAA,OACA,WAAA,OACA,gBAAA,KAGF,eACE,OAAA,KChNA,eACI,UAAA,KACA,YAAA,KACA,eAAA,UAKJ,wBACI,WAAA,QACA,UAAA,KACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UACA,YAAA,IAKJ,yBlBi5DF,yBACA,iCACA,2BACA,yBACA,qBACA,mBACA,gBACA,gBACA,mBkB/4DM,aAAA,QAEJ,yBACI,WAAA,QAIN,WACE,WAAA,QACA,aAAA,QACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,eACA,OAAA,elBo5DF,iBACA,mBkBl5DA,eAGE,iBAAA,QACA,MAAA,KACA,YAAA,KAGF,UACE,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OAGF,iCACE,WAAA,eACA,QAAA,IAAA,KAGF,UAAA,cACE,iBAAA,QAGF,sBACE,MAAA,KAKE,uBAAA,uBACE,aAAA,QAKF,4BADF,gBAEI,QAAA,OAGA,mBACI,UAAA,KACA,YAAA,KACA,eAAA,UAGJ,4BlB84DN,2BkB54DU,yBlB24DV,0BkBx4Dc,MAAA,KACA,QAAA,MACA,WAAA,OACA,MAAA,KACA,OAAA,KAAA,EAGJ,oBACI,MAAA,KAGJ,iCACI,QAAA,MAIR,qBACI,eAAA,WAKV,mCACE,iBAAA,QAGF,+DACE,iBAAA,eAIF,6BACE,UAAA,cAGF,2CACE,YAAA,MCxIF,cACE,iBAAA,QACA,qBACE,QAAA,OAAA,MACE,UAAA,UACA,cAAA,MACA,YAAA,IACA,MAAA,QAEA,uCACE,iBAAA,QAGF,+BACE,iBAAA,QACA,YAAA,IACA,aAAA,EAKR,sBACE,aAAA,IAAA,MAAA,QAGF,UACE,iBAAA,QACA,aAAA,kBACA,MAAA,QACA,gBACE,QAAA,EAOF,oBACE,UAAA,IAIA,kDACE,wBAAA,YACA,2BAAA,YACA,uBAAA,cACA,0BAAA,cAIJ,qCACE,OAAA,IAAA,MAAA,QACA,YAAA,EACA,uBAAA,EACA,0BAAA,EACA,wBAAA,OACA,2BAAA,OCxDA,+BACI,QAAA,KAGJ,0CACI,MAAA,QACA,YAAA,IAGJ,qCACI,iBAAA,KACA,MAAA,QACA,mBAAA,KAAA,WAAA,KCZR,YACE,OAAA,EAAA,KAGF,eACE,iBAAA,QAGF,aACE,iBAAA,QAGF,WACE,aAAA,YAGF,WACE,iBAAA,QAIA,6BACE,OAAA,IAAA,MAAA,QAIJ,aACE,QAAA,EAMA,yBACE,aAAA,EACA,kCACE,OAAA,KAAA,MAAA,QAGJ,yBACE,YAAA,YAEF,6BACE,iBAAA,YAOF,sCACE,OAAA,KAAA,OAEF,6BACE,YAAA,YAOI,kCACE,iBAAA,QAGF,iCACE,iBAAA,QACA,aAAA,QACA,uCACE,iBAAA,QAKF,8CACE,iBAAA,QAKF,uDACE,aAAA,QApBJ,oCACE,iBAAA,QAGF,mCACE,iBAAA,QACA,aAAA,QACA,yCACE,iBAAA,QAKF,gDACE,iBAAA,QAKF,yDACE,aAAA,QApBJ,kCACE,iBAAA,QAGF,iCACE,iBAAA,QACA,aAAA,QACA,uCACE,iBAAA,QAKF,8CACE,iBAAA,QAKF,uDACE,aAAA,QApBJ,+BACE,iBAAA,QAGF,8BACE,iBAAA,QACA,aAAA,QACA,oCACE,iBAAA,QAKF,2CACE,iBAAA,QAKF,oDACE,aAAA,QApBJ,kCACE,iBAAA,KAGF,iCACE,iBAAA,QACA,aAAA,KACA,uCACE,iBAAA,KAKF,8CACE,iBAAA,KAKF,uDACE,aAAA,QApBJ,iCACE,iBAAA,QAGF,gCACE,iBAAA,QACA,aAAA,QACA,sCACE,iBAAA,QAKF,6CACE,iBAAA,QAKF,sDACE,aAAA,QApBJ,+BACE,iBAAA,QAGF,8BACE,iBAAA,QACA,aAAA,QACA,oCACE,iBAAA,QAKF,2CACE,iBAAA,QAKF,oDACE,aAAA,QApBJ,gCACE,iBAAA,QAGF,+BACE,iBAAA,QACA,aAAA,QACA,qCACE,iBAAA,QAKF,4CACE,iBAAA,QAKF,qDACE,aAAA,QApBJ,+BACE,iBAAA,QAGF,8BACE,iBAAA,KACA,aAAA,QACA,oCACE,iBAAA,QAKF,2CACE,iBAAA,QAKF,oDACE,aAAA,KASR,4BACE,iBAAA,YACA,OAAA,IAAA,MAAA,YACA,mBAAA,QACA,OAAA,KAAA,EAAA,EAAA,eACA,aAAA,IAAA,MAAA,IAAA,IACA,mCACE,QAAA,MACA,QAAA,IACA,SAAA,SACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,MAAA,MACA,OAAA,MACA,cAAA,MAON,iCACE,iBAAA,YACA,OAAA,IAAA,MAAA,YACA,mBAAA,QACA,OAAA,KAAA,EAAA,EAAA,eACA,wCACE,QAAA,MACA,QAAA,IACA,SAAA,SACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,MAAA,KACA,OAAA,KACA,cAAA,KAEJ,uCACE,QAAA,MACA,QAAA,IACA,MAAA,KACA,SAAA,SACA,IAAA,KACA,MAAA,EACA,WAAA,IAAA,OAAA,QCxIJ,KACI,YAAA,0BAKA,qBtBu0EJ,sBACA,wBAFA,oBsBl0EQ,WAAA,kBACA,UAAA,KtBw0ER,6BACA,+BsBn0EQ,2BACI,QAAA,KAIR,sBACI,WAAA,QACA,aAAA,QAGJ,2BACI,UAAA,KACA,MAAA,QtBm0ER,qBsBh0EI,qBAEI,MAAA,QACA,WAAA,QACA,UAAA,KAGJ,wBACI,OAAA,IAAA,MAAA,QACA,MAAA,KACA,OAAA,KACA,IAAA,KACA,iBAAA,kBC1CN,8BACE,UAAA,KACA,YAAA,IAIJ,eACE,UAAA,KAIA,2BACE,aAAA,QACA,MAAA,QAGA,sDACE,iBAAA,QAGF,8CACE,aAAA,qBAGJ,0BACE,aAAA,KACA,MAAA,KAKF,oBACE,mBAAA,KAAA,WAAA,KAKF,2CACE,WAAA,QACA,sEACE,WAAA,QACA,2FAAA,gGACE,WAAA,oBAKN,gDACE,WAAA,QAIJ,cACE,aAAA,QAAA,YAAA,QAAA,YCtDF,QACE,aAAA,QAGF,0BAAA,0BACE,UAAA,KAGF,0BACE,IAAA,EAIA,kBACE,QAAA,aACA,eAAA,OAEA,wBACE,YAAA,ICbF,qBACI,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBACA,QAAA,EACA,2BACI,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBACA,QAAA,GAKJ,6CAAA,0CACE,UAAA,IACA,OAAA,IAAA,KAQN,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,iBACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,6BAFJ,eACI,OAAA,IAAA,MAAA,eACA,iBAAA,8BAFJ,cACI,OAAA,IAAA,MAAA,kBACA,iBAAA,6BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,aACI,OAAA,IAAA,MAAA,kBACA,iBAAA,4BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAOR,aACI,iBAAA,mBACA,OAAA,IAAA,MAAA,QAGJ,gBACI,QAAA,KACA,iBAAA,QACA,cAAA,EACA,OAAA,IAAA,MAAA,QC3CJ,OACE,MAAA,QAGF,eACE,aAAA,QAGF,qBACE,QAAA,KACA,OAAA,EACA,QAAA,EACA,4BACE,QAAA,MAEF,wBACE,UAAA,KACA,WAAA,KACA,MAAA,QACA,WAAA,ICnBJ,mBACE,QAAA,MACA,8CACE,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,OAAA,KACA,oDACE,QAAA,EAGF,2EACE,YAAA,KACA,aAAA,KACA,MAAA,QAGF,wEACE,OAAA,KACA,MAAA,KACA,MAAA,IAEA,0EACE,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAIJ,8EACE,MAAA,QAUA,gFACE,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAON,sDACI,QAAA,KACA,iBAAA,QACA,6EACI,OAAA,IAAA,MAAA,QACA,iBAAA,QACA,MAAA,QACA,QAAA,EAGR,iFACI,iBAAA,QAEJ,yEACI,iBAAA,QACA,MAAA,QACA,+EACI,iBAAA,QACA,MAAA,KAKV,yBACE,QAAA,IAAA,KAGF,kBACE,OAAA,IAAA,MAAA,QACA,iBAAA,QACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBAIA,sBACE,OAAA,IAAA,MAAA,QAKF,gDACE,WAAA,KACA,iBAAA,QACA,OAAA,IAAA,MAAA,kBAEA,6EACE,QAAA,IAAA,KAEF,uEACE,OAAA,EACA,MAAA,QACA,kGACI,MAAA,QADJ,yFACI,MAAA,QADJ,6FACI,MAAA,QADJ,8FACI,MAAA,QADJ,oFACI,MAAA,QAGN,2EACE,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,QAAA,EAAA,IAOF,kFACE,aAAA,QAIJ,oDACE,YAAA,IAMJ,mCACI,MAAA,KACA,MAAA,KACA,aAAA,KACF,uCACE,MAAA,KACA,OAAA,KACA,cAAA,IAIJ,uCACE,WAAA,IAGF,kC3BogFA,uCACA,qC2BlgFE,QAAA,aACA,UAAA,KACA,aAAA,IACA,MAAA,QAEA,sC3BogFF,2CACA,yC2BpgFI,aAAA,IAGE,uD3BqgFN,4DACA,0D2BrgFQ,QAAA,QACA,YAAA,sBAON,wE3BmgFF,6EACA,2E2BjgFE,MAAA,qBAIF,iCACE,SAAA,OAMF,UACE,aAAA,IACA,OAAA,KACA,MAAA,KClLF,cACE,QAAA,KACA,oBACE,UAAA,IACA,YAAA,EACA,MAAA,KACA,OAAA,KACA,iBAAA,QACA,iBAAA,KACA,cAAA,KACA,QAAA,UACA,OAAA,QACA,QAAA,aACA,WAAA,OACA,SAAA,SACA,YAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YACA,2BACE,MAAA,QACA,QAAA,qBACA,QAAA,MACA,YAAA,QACA,YAAA,IACA,UAAA,KACA,YAAA,KACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,IAAA,KACA,WAAA,OACA,UAAA,WACA,SAAA,OACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAGF,0BACE,QAAA,GACA,SAAA,SACA,KAAA,IACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KACA,OAAA,KACA,MAAA,KACA,IAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAIJ,4BACE,iBAAA,QAIJ,4BACE,iBAAA,QACA,mCACE,MAAA,KACA,QAAA,oBACA,MAAA,KACA,KAAA,IAGF,kCACE,KAAA,KACA,iBAAA,QAIJ,yBACE,iBAAA,QAEF,gCAAA,wC5BurFA,2C4BrrFE,MAAA,KAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,KAGF,mCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,eACE,aAAA,IACA,mCAAA,yCACE,cAAA,IChHJ,YACE,OAAA,IAAA,MAAA,QACA,QAAA,IACA,QAAA,cAGI,wBACE,YAAA,IAGA,yCAAA,+BAAA,8CAAA,qCAAA,iCAAA,0CAAA,gDAAA,uCAAA,8BAAA,uCAAA,6CAAA,oCAGE,iBAAA,kBACA,iBAAA,KACA,mBAAA,KAAA,WAAA,KACA,MAAA,e7B0yFV,qCACA,mC6BxyFQ,oCAAA,kCAII,WAAA,Q7BwyFZ,iCACA,iC6BtyFQ,4BAAA,4BAII,MAAA,QACA,QAAA,GAGJ,8BAAA,uCAAA,6CAAA,oCACI,iBAAA,QAQV,6BAAA,6BACE,QAAA,ICxCI,2DAAA,wEACA,wBAAA,EACA,2BAAA,EAOA,0DAAA,uEACE,uBAAA,EACA,0BAAA,ECfR,oCACE,QAAA,EAKF,6CACE,WAAA,MAEA,yBAHF,6CAII,WAAA,QAIF,mDACE,YAAA,KACA,aAAA,EAOF,oBACE,QAAA,EAMJ,iFACE,aAAA,EAGF,gFACE,cAAA,EAKJ,gBACE,gBAAA,mBACA,cAAA,eAUI,sC/Bk0FN,0CAEA,mDADA,2CAEA,oD+Bp0FQ,KAAA,KACA,MAAA,MACA,QAAA,SACA,YAAA,wBACA,UAAA,KACA,IAAA,IAKF,qC/Bm0FN,yCAEA,kDADA,0CAEA,mD+Br0FQ,KAAA,KACA,MAAA,KACA,QAAA,SACA,YAAA,wBACA,IAAA,KACA,UAAA,K/B20FR,oCAFA,wCACA,yC+Bj0FQ,oCAAA,wCAAA,yCAGE,aAAA,KACA,cAAA,KASN,kCAAA,mCACI,iBAAA,oBAEA,qCAAA,sCACI,aAAA,oBACA,MAAA,QAIJ,+BACI,QAAA,YAIR,+BAAA,+BACI,QAAA,IAAA,MAAA,kBACA,eAAA,KACA,iBAAA,qBAKR,iBACE,YAAA,IAYQ,8E/B2yFV,8E+B1yFY,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,iBAAA,QACA,OAAA,KAWF,2E/BoyFV,2E+BnyFY,iBAAA,QAUZ,mBACE,iBAAA,QACA,OAAA,KACA,MAAA,KACA,mBAAA,KAAA,WAAA,KACA,cAAA,IACA,WAAA,OACA,QAAA,GAEA,sBACE,cAAA,KACA,iBAAA,qBACA,MAAA,K3B5FA,4BJ+3FJ,wB+B7xFE,4BAEE,QAAA,aACA,UAAA,OAGF,mBACE,QAAA,KAIA,wBACE,WAAA,OACA,QAAA,MACA,OAAA,KAAA,EAAA,YAIJ,eACE,QAAA,aACA,cAAA,MAMF,4BACE,iBAAA,QASJ,gBACE,OAAA,IAAA,MAAA,QAQE,kD/B6wFJ,iD+B5wFM,SAAA,SAEA,8D/B8wFN,6D+B7wFQ,aAAA,KAEA,qE/B+wFR,oE+B9wFU,IAAA,IACA,KAAA,IACA,OAAA,KACA,MAAA,KACA,WAAA,MACA,QAAA,MACA,SAAA,SACA,MAAA,KACA,OAAA,IAAA,MAAA,KACA,cAAA,KACA,mBAAA,YAAA,WAAA,YACA,WAAA,OACA,YAAA,YACA,YAAA,KACA,QAAA,IACA,iBAAA,QC3OV,aACI,OAAA,IAAA,MAAA,kBAIA,oBACI,WAAA,IAAA,MAAA,kBhCggGR,4BgC7/FI,kBhC8/FJ,oBgC3/FQ,iBAAA,kBACA,WAAA,cAGJ,eACI,MAAA,kBAEA,2DACI,iBAAA,kBAKJ,qBACI,iBAAA,kBAIR,2BACI,aAAA,kBAGJ,kBhCs/FJ,4BACA,2BgCp/FQ,WAAA,kBAGJ,eACI,MAAA,kBAEA,mBACI,KAAA,kBAIR,4BACI,iBAAA,kBAGJ,sBhCk/FJ,+BACA,+BgCh/FQ,MAAA,kBAGJ,2DACI,aAAA,IAAA,MAAA,kBAGR,iBACI,QAAA,eC/DJ,UACE,WAAA,MACA,OAAA,IAAA,OAAA,QACA,WAAA,QACA,cAAA,IAEA,sBACE,UAAA,KACA,MAAA,KCNA,0CACI,SAAA,SAEA,iDACI,QAAA,GACA,MAAA,KACA,OAAA,IACA,iBAAA,QACA,SAAA,SACA,KAAA,EACA,IAAA,KAGJ,uDACI,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,IAAA,MAAA,QACA,MAAA,QACA,WAAA,OACA,cAAA,IACA,SAAA,SACA,iBAAA,QAEA,4BAZJ,uDAaQ,QAAA,MACA,OAAA,EAAA,KAAA,eAKJ,gEACI,QAAA,MACA,WAAA,IACA,YAAA,IAEA,4BALJ,gEAMQ,QAAA,MAIR,2DACI,iBAAA,YACA,MAAA,QAEA,wEACI,iBAAA,QACA,MAAA,KAMhB,iDACI,YAAA,KACA,aAAA,EACA,WAAA,KACA,cAAA,EAEA,oDACI,QAAA,aAEA,sDACI,QAAA,aACA,QAAA,OAAA,OACA,iBAAA,QACA,MAAA,KACA,cAAA,OAIA,+DACI,OAAA,YACA,iBAAA,QAIR,yDACI,MAAA,MAMhB,+BACI,YAAA,KACA,WAAA,MCzFF,+BACE,QAAA,MAEF,oCACE,OAAA,eAGA,0CACE,iBAAA,QACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,sDACI,iBAAA,QACA,aAAA,QACA,MAAA,KAGN,wCACE,MAAA,MACA,uDACE,MAAA,EACA,kBAAA,eAAA,UAAA,eACA,IAAA,eAKJ,2BACE,UAAA,KACA,YAAA,IAIJ,gCACE,aAAA,KACA,MAAA,kBAEA,sCACE,iBAAA,kBAGF,sCACE,QAAA,aACA,aAAA,IACA,SAAA,SACA,8CACE,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YAEF,6CACE,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KAGJ,qDACE,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAEA,oEACE,QAAA,IAIF,yEACE,eAAA,KACA,QAAA,EAIF,0EACE,QAAA,QACA,YAAA,sBACA,YAAA,IAIF,4EACE,iBAAA,QACA,OAAA,YAIF,2EACE,iBAAA,QACA,aAAA,QAEF,0EACE,MAAA,KAMJ,uDACE,IAAA,eACA,iBAAA,QACA,6DACE,MAAA,KAON,yBAEI,kEACE,IAAA,iBClIN,mBAAA,oBACE,OAAA,0BACA,QAAA,OAAA,MACA,OAAA,IAAA,MAAA,QACA,iBAAA,QACA,MAAA,QACA,cAAA,OACA,yBAAA,0BACE,QAAA,EACA,aAAA,QCXN,aACI,WAAA,eACA,kBACI,YAAA,oCACA,KAAA,QAEJ,gCACI,OAAA,EAAA,KrCgxGR,yBqC5wGA,0BAEI,YAAA,oCAGJ,0BACI,YAAA,IAGJ,qBACI,eAAA,KACA,OAAA,QAGJ,wBACI,MAAA,kBACA,YAAA,oCACA,UAAA,eAGJ,sBACI,KAAA,erC+wGJ,uBqC1wGI,uBACI,YAAA,oCACA,KAAA,QCxCR,oBACE,OAAA,MAGF,SACE,QAAA,IAAA,KACA,iBAAA,qBACA,QAAA,IACA,MAAA,QACA,mBAAA,EAAA,IAAA,IAAA,gBAAA,WAAA,EAAA,IAAA,IAAA,gBACA,cAAA,IAGF,aACE,MAAA,QCbF,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,kBACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,kBAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,oCACA,YAAA,cCfF,OAAA,gBACE,OAAA,MACA,WAAA,QACA,cAAA,IAGF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAGF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SACA,2BACE,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAEF,2BACE,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QClCJ,kBACI,OAAA,KACA,WAAA,QACA,MAAA,QACA,YAAA,0BACA,UAAA,MACA,QAAA,IAAA,ICLA,8BACE,QAAA,aAIJ,kBACE,YAAA,IACA,mCACE,YAAA,ICVN,eACE,MAAA,MACA,MAAA,KACA,QAAA,KACA,cAAA,IAGF,gBACE,YAAA,MAIA,4BACE,MAAA,QACA,YAAA,IAEF,iBACE,UAAA,KAIJ,yBACE,eACE,MAAA,KACA,MAAA,KAEF,gBACE,OAAA,GAMF,aACE,QAAA,MACA,MAAA,QACA,YAAA,KACA,QAAA,IAAA,IACA,oBACE,MAAA,QACA,YAAA,IAKN,cACE,QAAA,MACA,aAAA,EAEA,iBACE,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IAEA,mBACE,MAAA,QAGF,uBACE,WAAA,QACA,4BAAA,KAAA,oBAAA,KAGF,2BACE,MAAA,KACA,SAAA,SAGF,6BACE,MAAA,M3Cs5GN,oDACA,kC2Cr5GM,0CAGE,QAAA,MACA,MAAA,KAGF,kCACE,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EAGF,oDACE,OAAA,KAAA,KAAA,EAAA,KAGF,0CACE,WAAA,KACA,YAAA,IAGF,oCACE,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OACA,cAAA,EAIJ,6BACE,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,E3Cg5GN,mC2C94GM,sCAEE,SAAA,SACA,IAAA,EAGF,sCACE,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OAGF,mCACE,MAAA,EACA,MAAA,MACA,aAAA,KAIJ,wBAAA,8BAEE,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAIJ,wBACE,iBAAA,QACA,YAAA,IACA,MAAA,QACE,0BACE,MAAA,QACA,YAAA,IAMN,qCACE,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QACA,cAAA,IAEA,2CACE,QAAA,EACA,OAAA,QAEF,yDACE,QAAA,EAGF,2CACE,SAAA,SACA,OAAA,KACA,MAAA,KACA,KAAA,EACA,OAAA,QACA,QAAA,EACA,cAAA,EACA,4BAAA,KAAA,oBAAA,KACA,IAAA,EACA,kDACE,QAAA,SACA,YAAA,wBACA,IAAA,EACA,OAAA,KACA,MAAA,QACA,MAAA,KACA,SAAA,SACA,WAAA,MACA,KAAA,IACA,UAAA,KAMR,4BACE,6BACI,MAAA,OC1MN,cACI,MAAA,IACA,UAAA,OACA,OAAA,EAAA,KAEF,qBACE,QAAA,GACA,QAAA,MACA,MAAA,KAEF,aACE,cAAA,IACA,WAAA,IACA,QAAA,IAAA,EACA,SAAA,SACA,qBACE,YAAA,IAAA,MAAA,QACA,QAAA,GACA,OAAA,KACA,KAAA,KACA,SAAA,SACA,IAAA,EACA,MAAA,IAGJ,0CACE,aACE,cAAA,IACA,WAAA,IACA,qBACE,KAAA,IACA,YAAA,MAKN,mBACE,OAAA,IAAA,EACA,SAAA,SACA,yBACE,MAAA,KACA,QAAA,GACA,QAAA,MAGJ,+BACE,WAAA,EAEF,8BACE,cAAA,EAEF,0CACE,mBACE,OAAA,IAAA,EAEF,+BACE,WAAA,EAEF,8BACE,cAAA,GAGJ,iBACE,SAAA,SACA,IAAA,KACA,KAAA,EACA,MAAA,KACA,OAAA,KACA,cAAA,IACA,WAAA,OACA,YAAA,KACA,UAAA,KACA,MAAA,KACA,iBAAA,QACA,OAAA,IAAA,MAAA,KAEA,mBACE,YAAA,IAGJ,0CACE,iBACE,MAAA,KACA,OAAA,KACA,YAAA,KACA,KAAA,IACA,YAAA,MAEF,0CACE,WAAA,OAEF,0CACE,WAAA,QACA,kBAAA,YAAA,IAEA,UAAA,YAAA,KAIJ,qBACE,cAAA,IACA,OAAA,IAAA,MAAA,QACA,YAAA,KACA,QAAA,IACA,SAAA,SAEA,2BACE,MAAA,KACA,QAAA,GACA,QAAA,MAEF,wBACE,WAAA,EAEF,mCACE,WAAA,QACA,cAAA,MACA,MAAA,KACA,QAAA,aACA,MAAA,MACA,UAAA,KACA,QAAA,KAAA,IAEF,8BACE,QAAA,aACA,UAAA,KAEF,wBACE,UAAA,KACA,OAAA,EAAA,EAAA,KAAA,EAIJ,mDACE,iBAAA,QAEF,8BACE,MAAA,KACA,QAAA,KAAA,EACA,QAAA,GAEF,6BACE,QAAA,GACA,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,EACA,MAAA,EACA,OAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,QAEF,0CACE,qBACE,YAAA,EACA,QAAA,MACA,MAAA,IAEF,6BACE,IAAA,KACA,KAAA,KACA,aAAA,YACA,kBAAA,QAEF,mCACE,MAAA,KAEF,8BACE,SAAA,SACA,MAAA,KACA,KAAA,KACA,IAAA,KAEF,wDACE,MAAA,MAEF,gEACE,IAAA,KACA,KAAA,KACA,MAAA,KACA,aAAA,YACA,mBAAA,QAEF,sEACE,MAAA,MAEF,iEACE,KAAA,KACA,MAAA,KACA,WAAA,MAEF,8CACE,WAAA,OAEF,8CACE,WAAA,QACA,kBAAA,YAAA,IAEA,UAAA,YAAA,KAIJ,0CACE,iFACE,kBAAA,oBAAA,IAEA,UAAA,oBAAA,KCxMA,mBACE,WAAA,QACA,cAAA,IACA,MAAA,KACA,QAAA,aACA,OAAA,KACA,YAAA,KACA,WAAA,OACA,MAAA,KAMN,cACE,iBAAA,2BACA,kBAAA,UACA,gBAAA,MACA,oBAAA,OACA,0BACE,WAAA,mBACA,MAAA,KACA,OAAA,KACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EAMJ,cACE,OAAA,KAAA,KACA,UAAA,MACA,SAAA,SAMA,yBACE,UAAA,KACA,YAAA,KACA,oCACE,QAAA,MAEF,mCACE,QAAA,KAOJ,oBACE,UAAA,KACA,YAAA,IACA,YAAA,MACA,YAAA,kBAAA,IAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,CAAA,kBAAA,IAAA", "file": "app-dark.min.css", "sourcesContent": ["//\r\n// Fonts\r\n//\r\n\r\n// Nunito - Google Font\r\n@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap');\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 300;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 400;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 500;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\r\n}\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-style:  normal;\r\n    font-weight: 700;\r\n    font-display: swap;\r\n    src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"),\r\n         url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\r\n}", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1002;\r\n    background-color: $header-bg;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n\r\n    .dropdown {\r\n        .show.header-item {\r\n            background-color: darken($header-bg, 2%);\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-dark {\r\n    display: $display-block;\r\n}\r\n\r\n.logo-light {\r\n    display: $display-none;\r\n}\r\n\r\n/* Search */\r\n\r\n.app-search {\r\n    padding: calc(#{$header-height - 38px} / 2) 0;\r\n\r\n    .form-control {\r\n        border: none;\r\n        height: 38px;\r\n        padding-left: 40px;\r\n        padding-right: 20px;\r\n        background-color: $topbar-search-bg;\r\n        box-shadow: none;\r\n        border-radius: 30px;\r\n    }\r\n    span {\r\n        position: absolute;\r\n        z-index: 10;\r\n        font-size: 16px;\r\n        line-height: 38px;\r\n        left: 13px;\r\n        top: 0;\r\n        color: $gray-600;\r\n    }\r\n}\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height} + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: $header-item-color;\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: $header-item-color;\r\n    }\r\n\r\n\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: $gray-300;\r\n    padding: 3px;\r\n}\r\n\r\n.user-dropdown{\r\n    .dropdown-item{\r\n        i{\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 22px;\r\n        color: $header-item-color;\r\n    }\r\n\r\n    .noti-dot{\r\n        position: absolute;\r\n        display: inline-block;\r\n        height: 6px;\r\n        width: 6px;    \r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        top: 20px;\r\n        right: 14px;   \r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .d-flex {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: $gray-300;\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: $border-color;\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .ri-fullscreen-line:before {\r\n            content: \"\\ed73\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: $header-dark-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(55px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 15px;\r\n        }    \r\n    }\r\n}\r\n\r\n\r\n\r\nbody[data-topbar=\"colored\"] {\r\n    #page-topbar { \r\n        background-color: $header-colored-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}", "/*\nTemplate Name: Upcube -  Admin & Dashboard Template\nAuthor: Themesdesign\nVersion: 2.0.0\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600&display=swap\");\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 300;\n  font-display: swap;\n  src: url(\"../fonts/inter-light.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-light.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(\"../fonts/inter-regular.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-regular.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(\"../fonts/inter-medium.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/inter-medium.woff?v=3.13\") format(\"woff\");\n}\n@font-face {\n  font-family: \"Inter\";\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(\"../fonts/inter-bold.woff2?v=3.13\") format(\"woff2\"), url(\"../fonts/iter-bold.woff?v=3.13\") format(\"woff\");\n}\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1002;\n  background-color: #272d3e;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px / 2) 0 0;\n}\n.navbar-header .dropdown .show.header-item {\n  background-color: #232838;\n}\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 240px;\n}\n\n.logo {\n  line-height: 70px;\n}\n.logo .logo-sm {\n  display: none;\n}\n\n.logo-dark {\n  display: none;\n}\n\n.logo-light {\n  display: block;\n}\n\n/* Search */\n.app-search {\n  padding: calc(32px / 2) 0;\n}\n.app-search .form-control {\n  border: none;\n  height: 38px;\n  padding-left: 40px;\n  padding-right: 20px;\n  background-color: #2b3244;\n  box-shadow: none;\n  border-radius: 30px;\n}\n.app-search span {\n  position: absolute;\n  z-index: 10;\n  font-size: 16px;\n  line-height: 38px;\n  left: 13px;\n  top: 0;\n  color: #919bae;\n}\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px;\n}\n.megamenu-list li a {\n  color: #79859c;\n}\n\n@media (max-width: 991px) {\n  .navbar-brand-box {\n    width: auto;\n  }\n\n  .logo span.logo-lg {\n    display: none;\n  }\n  .logo span.logo-sm {\n    display: inline-block;\n  }\n}\n.page-content {\n  padding: calc(70px + 24px) calc(24px / 2) 60px calc(24px / 2);\n}\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: #919bae;\n  border: 0;\n  border-radius: 0px;\n}\n.header-item:hover {\n  color: #919bae;\n}\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: #2d3448;\n  padding: 3px;\n}\n\n.user-dropdown .dropdown-item i {\n  display: inline-block;\n}\n\n.noti-icon i {\n  font-size: 22px;\n  color: #919bae;\n}\n.noti-icon .noti-dot {\n  position: absolute;\n  display: inline-block;\n  height: 6px;\n  width: 6px;\n  background-color: #f32f53;\n  border-radius: 50%;\n  top: 20px;\n  right: 14px;\n}\n\n.notification-item .d-flex {\n  padding: 0.75rem 1rem;\n}\n.notification-item .d-flex:hover {\n  background-color: #2d3448;\n}\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #919bae;\n}\n.dropdown-icon-item img {\n  height: 24px;\n}\n.dropdown-icon-item span {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.dropdown-icon-item:hover {\n  border-color: #2d3448;\n}\n\n.fullscreen-enable [data-toggle=fullscreen] .ri-fullscreen-line:before {\n  content: \"\\ed73\";\n}\n\nbody[data-topbar=dark] #page-topbar {\n  background-color: #252b3b;\n}\nbody[data-topbar=dark] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=dark] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=dark] .header-item {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .header-item:hover {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=dark] .noti-icon i {\n  color: #e9ecef;\n}\nbody[data-topbar=dark] .logo-dark {\n  display: none;\n}\nbody[data-topbar=dark] .logo-light {\n  display: block;\n}\nbody[data-topbar=dark] .app-search .form-control {\n  background-color: rgba(43, 50, 68, 0.07);\n  color: #fff;\n}\nbody[data-topbar=dark] .app-search span,\nbody[data-topbar=dark] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\nbody[data-sidebar=dark] .navbar-brand-box {\n  background: #252b3b;\n}\nbody[data-sidebar=dark] .logo-dark {\n  display: none;\n}\nbody[data-sidebar=dark] .logo-light {\n  display: block;\n}\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static;\n  }\n  .navbar-header .dropdown .dropdown-menu {\n    left: 10px !important;\n    right: 10px !important;\n  }\n}\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none;\n  }\n}\nbody[data-layout=horizontal] .navbar-brand-box {\n  width: auto;\n}\nbody[data-layout=horizontal] .page-content {\n  margin-top: 70px;\n  padding: calc(55px + 24px) calc(24px / 2) 60px calc(24px / 2);\n}\n\n@media (max-width: 992px) {\n  body[data-layout=horizontal] .page-content {\n    margin-top: 15px;\n  }\n}\nbody[data-topbar=colored] #page-topbar {\n  background-color: #0f9cf3;\n}\nbody[data-topbar=colored] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=colored] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=colored] .header-item {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .header-item:hover {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=colored] .noti-icon i {\n  color: #e9ecef;\n}\nbody[data-topbar=colored] .logo-dark {\n  display: none;\n}\nbody[data-topbar=colored] .logo-light {\n  display: block;\n}\nbody[data-topbar=colored] .app-search .form-control {\n  background-color: rgba(43, 50, 68, 0.07);\n  color: #fff;\n}\nbody[data-topbar=colored] .app-search span,\nbody[data-topbar=colored] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.page-title-box {\n  padding-bottom: 24px;\n}\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0;\n}\n.page-title-box h4 {\n  font-size: 15px;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px / 2);\n  position: absolute;\n  right: 0;\n  color: #919bae;\n  left: 240px;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  background-color: #212734;\n}\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0;\n  }\n}\n.vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-layout=horizontal] .footer {\n  left: 0 !important;\n}\n\n.right-bar {\n  background-color: #252b3b;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0;\n}\n.right-bar .right-bar-toggle {\n  background-color: white;\n  height: 24px;\n  width: 24px;\n  line-height: 24px;\n  color: #252b3b;\n  text-align: center;\n  border-radius: 50%;\n}\n.right-bar .right-bar-toggle:hover {\n  background-color: white;\n}\n\n.rightbar-overlay {\n  background-color: rgba(239, 242, 247, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all 0.2s ease-out;\n}\n\n.right-bar-enabled .right-bar {\n  right: 0;\n}\n.right-bar-enabled .rightbar-overlay {\n  display: block;\n}\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto;\n  }\n  .right-bar .slimscroll-menu {\n    height: auto !important;\n  }\n}\n.metismenu {\n  margin: 0;\n}\n.metismenu li {\n  display: block;\n  width: 100%;\n}\n.metismenu .mm-collapse {\n  display: none;\n}\n.metismenu .mm-collapse:not(.mm-show) {\n  display: none;\n}\n.metismenu .mm-collapse.mm-show {\n  display: block;\n}\n.metismenu .mm-collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: 0.35s;\n  transition-property: height, visibility;\n}\n\n.vertical-menu {\n  width: 240px;\n  z-index: 1001;\n  background: #252b3b;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.main-content {\n  margin-left: 240px;\n  overflow: hidden;\n}\n.main-content .content {\n  padding: 0 15px 10px 15px;\n  margin-top: 70px;\n}\n\n#sidebar-menu {\n  padding: 10px 0 30px 0;\n}\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(-180deg);\n}\n#sidebar-menu .has-arrow:after {\n  content: \"\\f0140\";\n  font-family: \"Material Design Icons\";\n  display: block;\n  float: right;\n  transition: transform 0.2s;\n  font-size: 1rem;\n}\n#sidebar-menu ul li a {\n  display: block;\n  padding: 0.625rem 1.5rem;\n  color: #8590a5;\n  position: relative;\n  font-size: 13.3px;\n  transition: all 0.4s;\n  font-family: \"Inter\", sans-serif;\n  font-weight: 500;\n}\n#sidebar-menu ul li a i {\n  display: inline-block;\n  min-width: 1.5rem;\n  padding-bottom: 0.125em;\n  font-size: 1.1rem;\n  line-height: 1.40625rem;\n  vertical-align: middle;\n  color: #8590a5;\n  transition: all 0.4s;\n  opacity: 0.75;\n}\n#sidebar-menu ul li a:hover {\n  color: #d7e4ec;\n}\n#sidebar-menu ul li a:hover i {\n  color: #d7e4ec;\n}\n#sidebar-menu ul li .badge {\n  margin-top: 4px;\n}\n#sidebar-menu ul li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 3.2rem;\n  font-size: 13px;\n  color: #8590a5;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 4.2rem;\n  font-size: 13.5px;\n}\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: 0.05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #8590a5;\n  font-weight: 600;\n  font-family: \"Inter\", sans-serif;\n  opacity: 0.5;\n}\n\n.mm-active {\n  color: #d7e4ec !important;\n}\n.mm-active > a {\n  color: #d7e4ec !important;\n}\n.mm-active > a i {\n  color: #d7e4ec !important;\n}\n.mm-active > i {\n  color: #d7e4ec !important;\n}\n.mm-active .active {\n  color: #d7e4ec !important;\n}\n.mm-active .active i {\n  color: #d7e4ec !important;\n}\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none;\n  }\n\n  .main-content {\n    margin-left: 0 !important;\n  }\n\n  body.sidebar-enable .vertical-menu {\n    display: block;\n  }\n}\n.vertical-collpsed .main-content {\n  margin-left: 70px;\n}\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important;\n}\n.vertical-collpsed .logo span.logo-lg {\n  display: none;\n}\n.vertical-collpsed .logo span.logo-sm {\n  display: block;\n}\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5;\n}\n.vertical-collpsed .vertical-menu .simplebar-mask,\n.vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n  overflow: visible !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-scrollbar {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-offset {\n  bottom: 0 !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n.vertical-collpsed .vertical-menu #sidebar-menu .badge,\n.vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n  height: inherit !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n  display: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n  position: relative;\n  white-space: nowrap;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n  padding: 15px 20px;\n  min-height: 55px;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n  color: #d7e4ec;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  font-size: 20px;\n  margin-left: 4px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n  display: none;\n  padding-left: 25px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  position: relative;\n  width: calc(190px + 70px);\n  color: #0f9cf3;\n  background-color: #1d222e;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #0f9cf3;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n  display: inline;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n  display: block;\n  left: 70px;\n  position: absolute;\n  width: 190px;\n  height: auto !important;\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n  box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  box-shadow: none;\n  padding: 8px 20px;\n  position: relative;\n  width: 190px;\n  z-index: 6;\n  color: #8590a5;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #d7e4ec;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  padding: 5px 0;\n  z-index: 9999;\n  display: none;\n  background-color: #252b3b;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n  display: block;\n  left: 190px;\n  height: auto !important;\n  margin-top: -36px;\n  position: absolute;\n  width: 190px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n  position: absolute;\n  right: 20px;\n  top: 12px;\n  transform: rotate(270deg);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n  color: #212529;\n}\n\nbody[data-sidebar=dark] .user-profile h4 {\n  color: #fff;\n}\nbody[data-sidebar=dark] .user-profile span {\n  color: #79859c !important;\n}\nbody[data-sidebar=dark] .vertical-menu {\n  background: #252b3b;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a i {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed {\n  min-height: 1400px;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background: #293041;\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  color: #8590a5;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #d7e4ec;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  background-color: #272d3e;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=dark] .mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .menu-title {\n  color: #8590a5;\n}\n\nbody[data-layout=horizontal] .main-content {\n  margin-left: 0 !important;\n}\n\nbody[data-sidebar-size=small] .navbar-brand-box {\n  width: 160px;\n}\n@media (max-width: 992px) {\n  body[data-sidebar-size=small] .navbar-brand-box {\n    width: auto;\n  }\n}\nbody[data-sidebar-size=small] .vertical-menu {\n  width: 160px;\n  text-align: center;\n}\nbody[data-sidebar-size=small] .vertical-menu .has-arrow:after,\nbody[data-sidebar-size=small] .vertical-menu .badge {\n  display: none !important;\n}\nbody[data-sidebar-size=small] .main-content {\n  margin-left: 160px;\n}\nbody[data-sidebar-size=small] .footer {\n  left: 160px;\n}\n@media (max-width: 991px) {\n  body[data-sidebar-size=small] .footer {\n    left: 0;\n  }\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li.menu-title {\n  background-color: #293041;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li a i {\n  display: block;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small].vertical-collpsed .main-content {\n  margin-left: 70px;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  display: inline-block;\n}\nbody[data-sidebar-size=small].vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-sidebar=colored] .user-profile h4 {\n  color: #fff;\n}\nbody[data-sidebar=colored] .user-profile span {\n  color: #79859c !important;\n}\nbody[data-sidebar=colored] .vertical-menu {\n  background: #0f9cf3;\n}\nbody[data-sidebar=colored] .navbar-brand-box {\n  background-color: #0f9cf3;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-dark {\n  display: none;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-light {\n  display: block;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li.menu-title {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a i {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a.waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.1);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li ul.sub-menu li a {\n  color: rgba(255, 255, 255, 0.5);\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #19a0f3;\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n  color: #d7e4ec !important;\n}\nbody[data-sidebar=colored] .mm-active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active .active i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .menu-title {\n  color: #fff !important;\n}\n\n.vertical-collpsed .user-profile {\n  display: none;\n}\n\n.topnav {\n  background: #282e3f;\n  padding: 0 calc(24px / 2);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100;\n}\n.topnav .topnav-menu {\n  margin: 0;\n  padding: 0;\n}\n.topnav .navbar-nav .nav-link {\n  font-size: 15px;\n  position: relative;\n  padding: 1rem 1.3rem;\n  color: #919bae;\n  font-family: \"Inter\", sans-serif;\n}\n.topnav .navbar-nav .nav-link i {\n  font-size: 15px;\n  vertical-align: middle;\n  display: inline-block;\n}\n.topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n  color: #0f9cf3;\n  background-color: transparent;\n}\n.topnav .navbar-nav .dropdown-item {\n  color: #919bae;\n}\n.topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n  color: #0f9cf3;\n}\n.topnav .navbar-nav .nav-item .nav-link.active {\n  color: #0f9cf3;\n}\n.topnav .navbar-nav .dropdown.active > a {\n  color: #0f9cf3;\n  background-color: transparent;\n}\n\n@media (min-width: 1200px) {\n  body[data-layout=horizontal] .container-fluid,\nbody[data-layout=horizontal] .navbar-header {\n    max-width: 85%;\n  }\n}\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0;\n  }\n  .topnav .dropdown-item {\n    padding: 0.5rem 1.5rem;\n    min-width: 180px;\n  }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto;\n  }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 0.25rem 0.25rem;\n  }\n  .topnav .dropdown .dropdown-menu .arrow-down::after {\n    right: 15px;\n    transform: rotate(-135deg) translateY(-50%);\n    position: absolute;\n  }\n  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n    position: absolute;\n    top: 0 !important;\n    left: 100%;\n    display: none;\n  }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n\n  .navbar-toggle {\n    display: none;\n  }\n}\n.arrow-down {\n  display: inline-block;\n}\n.arrow-down:after {\n  border-color: initial;\n  border-style: solid;\n  border-width: 0 0 1px 1px;\n  content: \"\";\n  height: 0.4em;\n  display: inline-block;\n  right: 5px;\n  top: 50%;\n  margin-left: 10px;\n  transform: rotate(-45deg) translateY(-50%);\n  transform-origin: top;\n  transition: all 0.3s ease-out;\n  width: 0.4em;\n}\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: none;\n  }\n  .navbar-brand-box .logo-dark span.logo-sm {\n    display: none;\n  }\n  .navbar-brand-box .logo-light {\n    display: block;\n  }\n\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0;\n  }\n  .topnav .navbar-nav .nav-link {\n    padding: 0.75rem 1.1rem;\n  }\n  .topnav .dropdown .dropdown-menu {\n    background-color: transparent;\n    border: none;\n    box-shadow: none;\n    padding-left: 15px;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n    width: auto;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n    margin: 0px;\n  }\n  .topnav .dropdown .dropdown-item {\n    position: relative;\n    background-color: transparent;\n  }\n  .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n    color: #0f9cf3;\n  }\n  .topnav .arrow-down::after {\n    right: 15px;\n    position: absolute;\n  }\n}\n@media (min-width: 992px) {\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-dark {\n    display: none;\n  }\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-light {\n    display: block;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav {\n    background-color: #252b3b;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:focus, body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav > .dropdown.active > a {\n    color: rgba(255, 255, 255, 0.9) !important;\n  }\n}\nbody[data-layout-size=boxed] {\n  background-color: #1d222e;\n}\nbody[data-layout-size=boxed] #layout-wrapper {\n  background-color: #1d222e;\n  max-width: 1300px;\n  margin: 0 auto;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\nbody[data-layout-size=boxed] #page-topbar {\n  max-width: 1300px;\n  margin: 0 auto;\n}\nbody[data-layout-size=boxed] .footer {\n  margin: 0 auto;\n  max-width: calc(1300px - 240px);\n}\nbody[data-layout-size=boxed].vertical-collpsed .footer {\n  max-width: calc(1300px - 70px);\n}\n\nbody[data-layout=horizontal][data-layout-size=boxed] #page-topbar, body[data-layout=horizontal][data-layout-size=boxed] #layout-wrapper, body[data-layout=horizontal][data-layout-size=boxed] .footer {\n  max-width: 100%;\n}\nbody[data-layout=horizontal][data-layout-size=boxed] .container-fluid, body[data-layout=horizontal][data-layout-size=boxed] .navbar-header {\n  max-width: 1300px;\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(15, 156, 243, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(111, 208, 136, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(0, 151, 167, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(255, 187, 68, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(243, 47, 83, 0.4);\n}\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: #0f9cf3;\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -12px;\n  border: 2px solid #252b3b;\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.custom-accordion .card + .card {\n  margin-top: 0.5rem;\n}\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\f0415\";\n}\n.custom-accordion .card-header {\n  border-radius: 7px;\n}\n\n.custom-accordion-arrow .card {\n  border: 1px solid #2d3448;\n  box-shadow: none;\n}\n.custom-accordion-arrow .card-header {\n  padding-left: 45px;\n  position: relative;\n}\n.custom-accordion-arrow .card-header .accor-arrow-icon {\n  position: absolute;\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  line-height: 24px;\n  font-size: 16px;\n  background-color: #0f9cf3;\n  color: #fff;\n  border-radius: 50%;\n  text-align: center;\n  left: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {\n  content: \"\\f0142\";\n}\n\n.font-family-secondary {\n  font-family: \"Inter\", sans-serif;\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 1px solid #8590a5;\n  border-radius: 50%;\n  color: #8590a5;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: #919bae;\n  background-color: #252b3b;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000;\n}\n\n.flex-1 {\n  flex: 1;\n}\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n  background: transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat;\n}\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #252b3b;\n  z-index: 9999;\n}\n\n#status {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translateY(-50%);\n  margin: -20px 0 0 -20px;\n}\n\n.spinner .spin-icon {\n  font-size: 56px;\n  color: #0f9cf3;\n  position: relative;\n  display: inline-block;\n  animation: spin 1.6s infinite linear;\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(359deg);\n  }\n}\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em;\n}\n.form-check-right .form-check-input {\n  float: right;\n  margin-left: 0;\n  margin-right: -1.5em;\n}\n.form-check-right .form-check-label {\n  display: block;\n}\n\n.form-check {\n  position: relative;\n  text-align: left;\n}\n\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.activity-wid {\n  margin-top: 8px;\n  margin-left: 16px;\n}\n.activity-wid .activity-list {\n  position: relative;\n  padding: 0 0 40px 30px;\n}\n.activity-wid .activity-list:before {\n  content: \"\";\n  border-left: 2px dashed rgba(15, 156, 243, 0.25);\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  top: 32px;\n}\n.activity-wid .activity-list .activity-icon {\n  position: absolute;\n  left: -15px;\n  top: 0;\n  z-index: 9;\n}\n.activity-wid .activity-list:last-child {\n  padding-bottom: 0px;\n}\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px;\n}\n.button-items .btn {\n  margin-bottom: 12px;\n  margin-left: 8px;\n}\n\n.mfp-popup-form {\n  max-width: 1140px;\n}\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n\n.icon-demo-content {\n  color: #8590a5;\n}\n.icon-demo-content i {\n  display: inline-block;\n  width: 40px;\n  height: 40px;\n  line-height: 36px;\n  font-size: 22px;\n  color: #919bae;\n  border: 2px solid #2d3448;\n  border-radius: 4px;\n  transition: all 0.4s;\n  text-align: center;\n  margin-right: 16px;\n  vertical-align: middle;\n}\n.icon-demo-content .col-lg-4 {\n  margin-top: 24px;\n}\n.icon-demo-content .col-lg-4:hover i {\n  color: #fff;\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n}\n\n.grid-structure .grid-container {\n  background-color: #212529;\n  margin-top: 10px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  padding: 10px 20px;\n}\n\n.card-radio {\n  background-color: #252b3b;\n  border: 2px solid #2d3448;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  display: block;\n}\n.card-radio:hover {\n  cursor: pointer;\n}\n\n.card-radio-label {\n  display: block;\n}\n\n.card-radio-input {\n  display: none;\n}\n.card-radio-input:checked + .card-radio {\n  border-color: #0f9cf3 !important;\n}\n\n.navs-carousel .owl-nav {\n  margin-top: 16px;\n}\n.navs-carousel .owl-nav button {\n  width: 30px;\n  height: 30px;\n  line-height: 28px !important;\n  font-size: 20px !important;\n  border-radius: 50% !important;\n  background-color: rgba(15, 156, 243, 0.25) !important;\n  color: #0f9cf3 !important;\n  margin: 4px 8px !important;\n}\n\n@media print {\n  .vertical-menu,\n.right-bar,\n.page-title-box,\n.navbar-header,\n.footer {\n    display: none !important;\n  }\n\n  .card-body,\n.main-content,\n.right-bar,\n.page-content,\nbody {\n    padding: 0;\n    margin: 0;\n  }\n\n  .card {\n    border: 0;\n  }\n}\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n.fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n\n.fc th.fc-widget-header {\n  background: #2d3448;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: #2d3448;\n}\n.fc-unthemed td.fc-today {\n  background: #2d3448;\n}\n\n.fc-button {\n  background: #252b3b;\n  border-color: #2d3448;\n  color: #f6f6f6;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important;\n}\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #0f9cf3;\n  color: #fff;\n  text-shadow: none;\n}\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center;\n}\n\n#external-events .external-event {\n  text-align: left !important;\n  padding: 8px 16px;\n}\n\n.fc-event, .fc-event-dot {\n  background-color: #0f9cf3;\n}\n\n.fc-event .fc-content {\n  color: #fff;\n}\n\n.fc .table-bordered td, .fc .table-bordered th {\n  border-color: #2d3448;\n}\n@media (max-width: 575.98px) {\n  .fc .fc-toolbar {\n    display: block;\n  }\n}\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n.fc .fc-toolbar .fc-right,\n.fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0;\n  }\n  .fc .fc-toolbar > * > * {\n    float: none;\n  }\n  .fc .fc-toolbar .fc-today-button {\n    display: none;\n  }\n}\n.fc .fc-toolbar .btn {\n  text-transform: capitalize;\n}\n\n.fc-bootstrap .fc-today.alert-info {\n  background-color: #2d3448;\n}\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\n  background-color: #000 !important;\n}\n\n[dir=rtl] .fc-header-toolbar {\n  direction: ltr !important;\n}\n\n[dir=rtl] .fc-toolbar > * > :not(:first-child) {\n  margin-left: 0.75em;\n}\n\n.sp-container {\n  background-color: #252b3b;\n}\n.sp-container button {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.71094rem;\n  border-radius: 0.2rem;\n  font-weight: 400;\n  color: #eff2f7;\n}\n.sp-container button.sp-palette-toggle {\n  background-color: #2d3448;\n}\n.sp-container button.sp-choose {\n  background-color: #6fd088;\n  margin-left: 5px;\n  margin-right: 0;\n}\n\n.sp-palette-container {\n  border-right: 1px solid #2d3448;\n}\n\n.sp-input {\n  background-color: #292f3f;\n  border-color: #2d3448 !important;\n  color: #8590a5;\n}\n.sp-input:focus {\n  outline: none;\n}\n\n[dir=rtl] .sp-alpha {\n  direction: rtl;\n}\n[dir=rtl] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important;\n}\n[dir=rtl] input.spectrum.with-add-on {\n  border: 1px solid #2d3448;\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\n\n#session-timeout-dialog .close {\n  display: none;\n}\n#session-timeout-dialog .countdown-holder {\n  color: #f32f53;\n  font-weight: 500;\n}\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f32f53;\n  box-shadow: none;\n}\n\n.rs-control {\n  margin: 0px auto;\n}\n\n.rs-path-color {\n  background-color: #2d3448;\n}\n\n.rs-bg-color {\n  background-color: #252b3b;\n}\n\n.rs-border {\n  border-color: transparent;\n}\n\n.rs-handle {\n  background-color: #f6f6f6;\n}\n\n.rs-circle-border .rs-border {\n  border: 8px solid #2d3448;\n}\n\n.rs-disabled {\n  opacity: 1;\n}\n\n.outer-border .rs-border {\n  border-width: 0px;\n}\n.outer-border .rs-border.rs-outer {\n  border: 14px solid #2d3448;\n}\n.outer-border .rs-handle {\n  margin-left: 0 !important;\n}\n.outer-border .rs-path-color {\n  background-color: transparent;\n}\n\n.outer-border-dot .rs-border.rs-outer {\n  border: 16px dotted;\n}\n.outer-border-dot .rs-handle {\n  margin-left: 0 !important;\n}\n\n.rs-range-primary .rs-range-color {\n  background-color: #0f9cf3;\n}\n.rs-range-primary .rs-handle-dot {\n  background-color: #84ccf9;\n  border-color: #0f9cf3;\n}\n.rs-range-primary .rs-handle-dot:after {\n  background-color: #0f9cf3;\n}\n.rs-range-primary.rs-circle-border .rs-handle {\n  background-color: #0f9cf3;\n}\n.rs-range-primary.outer-border-dot .rs-border.rs-outer {\n  border-color: #84ccf9;\n}\n\n.rs-range-secondary .rs-range-color {\n  background-color: #919bae;\n}\n.rs-range-secondary .rs-handle-dot {\n  background-color: #d7dbe2;\n  border-color: #919bae;\n}\n.rs-range-secondary .rs-handle-dot:after {\n  background-color: #919bae;\n}\n.rs-range-secondary.rs-circle-border .rs-handle {\n  background-color: #919bae;\n}\n.rs-range-secondary.outer-border-dot .rs-border.rs-outer {\n  border-color: #d7dbe2;\n}\n\n.rs-range-success .rs-range-color {\n  background-color: #6fd088;\n}\n.rs-range-success .rs-handle-dot {\n  background-color: #cbeed4;\n  border-color: #6fd088;\n}\n.rs-range-success .rs-handle-dot:after {\n  background-color: #6fd088;\n}\n.rs-range-success.rs-circle-border .rs-handle {\n  background-color: #6fd088;\n}\n.rs-range-success.outer-border-dot .rs-border.rs-outer {\n  border-color: #cbeed4;\n}\n\n.rs-range-info .rs-range-color {\n  background-color: #0097a7;\n}\n.rs-range-info .rs-handle-dot {\n  background-color: #22eaff;\n  border-color: #0097a7;\n}\n.rs-range-info .rs-handle-dot:after {\n  background-color: #0097a7;\n}\n.rs-range-info.rs-circle-border .rs-handle {\n  background-color: #0097a7;\n}\n.rs-range-info.outer-border-dot .rs-border.rs-outer {\n  border-color: #22eaff;\n}\n\n.rs-range-warning .rs-range-color {\n  background-color: #ffbb44;\n}\n.rs-range-warning .rs-handle-dot {\n  background-color: #ffe8be;\n  border-color: #ffbb44;\n}\n.rs-range-warning .rs-handle-dot:after {\n  background-color: #ffbb44;\n}\n.rs-range-warning.rs-circle-border .rs-handle {\n  background-color: #ffbb44;\n}\n.rs-range-warning.outer-border-dot .rs-border.rs-outer {\n  border-color: #ffe8be;\n}\n\n.rs-range-danger .rs-range-color {\n  background-color: #f32f53;\n}\n.rs-range-danger .rs-handle-dot {\n  background-color: #faa3b3;\n  border-color: #f32f53;\n}\n.rs-range-danger .rs-handle-dot:after {\n  background-color: #f32f53;\n}\n.rs-range-danger.rs-circle-border .rs-handle {\n  background-color: #f32f53;\n}\n.rs-range-danger.outer-border-dot .rs-border.rs-outer {\n  border-color: #faa3b3;\n}\n\n.rs-range-pink .rs-range-color {\n  background-color: #e83e8c;\n}\n.rs-range-pink .rs-handle-dot {\n  background-color: #f5abcd;\n  border-color: #e83e8c;\n}\n.rs-range-pink .rs-handle-dot:after {\n  background-color: #e83e8c;\n}\n.rs-range-pink.rs-circle-border .rs-handle {\n  background-color: #e83e8c;\n}\n.rs-range-pink.outer-border-dot .rs-border.rs-outer {\n  border-color: #f5abcd;\n}\n\n.rs-range-light .rs-range-color {\n  background-color: #2d3448;\n}\n.rs-range-light .rs-handle-dot {\n  background-color: #5c6a93;\n  border-color: #2d3448;\n}\n.rs-range-light .rs-handle-dot:after {\n  background-color: #2d3448;\n}\n.rs-range-light.rs-circle-border .rs-handle {\n  background-color: #2d3448;\n}\n.rs-range-light.outer-border-dot .rs-border.rs-outer {\n  border-color: #5c6a93;\n}\n\n.rs-range-dark .rs-range-color {\n  background-color: #eff2f7;\n}\n.rs-range-dark .rs-handle-dot {\n  background-color: white;\n  border-color: #eff2f7;\n}\n.rs-range-dark .rs-handle-dot:after {\n  background-color: #eff2f7;\n}\n.rs-range-dark.rs-circle-border .rs-handle {\n  background-color: #eff2f7;\n}\n.rs-range-dark.outer-border-dot .rs-border.rs-outer {\n  border-color: white;\n}\n\n.rs-handle-arrow .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #f6f6f6;\n  margin: -6px 0px 0px 14px !important;\n  border-width: 6px 104px 6px 4px;\n}\n.rs-handle-arrow .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 22px;\n  width: 22px;\n  background: #f6f6f6;\n  right: -11px;\n  bottom: -11px;\n  border-radius: 100px;\n}\n\n.rs-handle-arrow-dash .rs-handle {\n  background-color: transparent;\n  border: 8px solid transparent;\n  border-right-color: #f6f6f6;\n  margin: -8px 0 0 14px !important;\n}\n.rs-handle-arrow-dash .rs-handle:before {\n  display: block;\n  content: \" \";\n  position: absolute;\n  height: 12px;\n  width: 12px;\n  background: #f6f6f6;\n  right: -6px;\n  bottom: -6px;\n  border-radius: 100%;\n}\n.rs-handle-arrow-dash .rs-handle:after {\n  display: block;\n  content: \" \";\n  width: 80px;\n  position: absolute;\n  top: -1px;\n  right: 0px;\n  border-top: 2px dotted #f6f6f6;\n}\n\n.irs {\n  font-family: var(--bs-font-sans-serif);\n}\n\n.irs--round .irs-bar,\n.irs--round .irs-to,\n.irs--round .irs-from,\n.irs--round .irs-single {\n  background: #0f9cf3 !important;\n  font-size: 11px;\n}\n.irs--round .irs-to:before,\n.irs--round .irs-from:before,\n.irs--round .irs-single:before {\n  display: none;\n}\n.irs--round .irs-line {\n  background: #2d3448;\n  border-color: #2d3448;\n}\n.irs--round .irs-grid-text {\n  font-size: 11px;\n  color: #8590a5;\n}\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #8590a5;\n  background: #2d3448;\n  font-size: 11px;\n}\n.irs--round .irs-handle {\n  border: 2px solid #0f9cf3;\n  width: 16px;\n  height: 16px;\n  top: 29px;\n  background-color: #252b3b !important;\n}\n\n.swal2-container .swal2-title {\n  font-size: 24px;\n  font-weight: 500;\n}\n\n.swal2-content {\n  font-size: 16px;\n}\n\n.swal2-icon.swal2-question {\n  border-color: #0097a7;\n  color: #0097a7;\n}\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #6fd088;\n}\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(111, 208, 136, 0.3);\n}\n.swal2-icon.swal2-warning {\n  border-color: #ffbb44;\n  color: #ffbb44;\n}\n\n.swal2-styled:focus {\n  box-shadow: none;\n}\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #0f9cf3;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n  background: #0f9cf3;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n  background: rgba(15, 156, 243, 0.3);\n}\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #0f9cf3;\n}\n\n.swal2-loader {\n  border-color: #0f9cf3 transparent #0f9cf3 transparent;\n}\n\n.symbol {\n  border-color: #252b3b;\n}\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px;\n}\n\n.rating-symbol-foreground {\n  top: 0px;\n}\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle;\n}\n.rating-star > span.badge {\n  margin-left: 4px;\n}\n\n/* =============\n   Notification\n============= */\n#toast-container > div {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 1;\n}\n#toast-container > div:hover {\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  opacity: 0.9;\n}\n#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {\n  min-width: 96%;\n  margin: 4px auto;\n}\n\n.toast-primary {\n  border: 2px solid #0f9cf3 !important;\n  background-color: rgba(15, 156, 243, 0.8) !important;\n}\n\n.toast-secondary {\n  border: 2px solid #919bae !important;\n  background-color: rgba(145, 155, 174, 0.8) !important;\n}\n\n.toast-success {\n  border: 2px solid #6fd088 !important;\n  background-color: rgba(111, 208, 136, 0.8) !important;\n}\n\n.toast-info {\n  border: 2px solid #0097a7 !important;\n  background-color: rgba(0, 151, 167, 0.8) !important;\n}\n\n.toast-warning {\n  border: 2px solid #ffbb44 !important;\n  background-color: rgba(255, 187, 68, 0.8) !important;\n}\n\n.toast-danger {\n  border: 2px solid #f32f53 !important;\n  background-color: rgba(243, 47, 83, 0.8) !important;\n}\n\n.toast-pink {\n  border: 2px solid #e83e8c !important;\n  background-color: rgba(232, 62, 140, 0.8) !important;\n}\n\n.toast-light {\n  border: 2px solid #2d3448 !important;\n  background-color: rgba(45, 52, 72, 0.8) !important;\n}\n\n.toast-dark {\n  border: 2px solid #eff2f7 !important;\n  background-color: rgba(239, 242, 247, 0.8) !important;\n}\n\n.toast-error {\n  background-color: rgba(243, 47, 83, 0.8);\n  border: 2px solid #f32f53;\n}\n\n.toastr-options {\n  padding: 24px;\n  background-color: #293041;\n  margin-bottom: 0;\n  border: 1px solid #2d3448;\n}\n\n.error {\n  color: #f32f53;\n}\n\n.parsley-error {\n  border-color: #f32f53;\n}\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0;\n}\n.parsley-errors-list.filled {\n  display: block;\n}\n.parsley-errors-list > li {\n  font-size: 12px;\n  list-style: none;\n  color: #f32f53;\n  margin-top: 5px;\n}\n\n.select2-container {\n  display: block;\n}\n.select2-container .select2-selection--single {\n  background-color: #292f3f;\n  border: 1px solid #2d3448;\n  height: 38px;\n}\n.select2-container .select2-selection--single:focus {\n  outline: none;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  line-height: 36px;\n  padding-left: 12px;\n  color: #8590a5;\n}\n.select2-container .select2-selection--single .select2-selection__arrow {\n  height: 34px;\n  width: 34px;\n  right: 3px;\n}\n.select2-container .select2-selection--single .select2-selection__arrow b {\n  border-color: #8590a5 transparent transparent transparent;\n  border-width: 6px 6px 0 6px;\n}\n.select2-container .select2-selection--single .select2-selection__placeholder {\n  color: #79859c;\n}\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #8590a5 transparent !important;\n  border-width: 0 6px 6px 6px !important;\n}\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #252b3b;\n}\n.select2-container--default .select2-search--dropdown .select2-search__field {\n  border: 1px solid #2d3448;\n  background-color: #292f3f;\n  color: #919bae;\n  outline: none;\n}\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #0f9cf3;\n}\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #2d3448;\n  color: #e9ecef;\n}\n.select2-container--default .select2-results__option[aria-selected=true]:hover {\n  background-color: #0f9cf3;\n  color: #fff;\n}\n\n.select2-results__option {\n  padding: 6px 12px;\n}\n\n.select2-dropdown {\n  border: 1px solid #2d3448;\n  background-color: #252b3b;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.select2-search input {\n  border: 1px solid #2d3448;\n}\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #292f3f;\n  border: 1px solid #2d3448 !important;\n}\n.select2-container .select2-selection--multiple .select2-selection__rendered {\n  padding: 2px 10px;\n}\n.select2-container .select2-selection--multiple .select2-search__field {\n  border: 0;\n  color: #8590a5;\n}\n.select2-container .select2-selection--multiple .select2-search__field::placeholder {\n  color: #8590a5;\n}\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  background-color: #252b3b;\n  border: 1px solid #2d3448;\n  border-radius: 1px;\n  padding: 0 7px;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #79859c;\n}\n.select2-container--default .select2-results__group {\n  font-weight: 600;\n}\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px;\n}\n.select2-result-repository__avatar img {\n  width: 100%;\n  height: auto;\n  border-radius: 2px;\n}\n\n.select2-result-repository__statistics {\n  margin-top: 7px;\n}\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #8590a5;\n}\n.select2-result-repository__forks .fa,\n.select2-result-repository__stargazers .fa,\n.select2-result-repository__watchers .fa {\n  margin-right: 4px;\n}\n.select2-result-repository__forks .fa.fa-flash::before,\n.select2-result-repository__stargazers .fa.fa-flash::before,\n.select2-result-repository__watchers .fa.fa-flash::before {\n  content: \"\\f0e7\";\n  font-family: \"Font Awesome 5 Free\";\n}\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.select2-result-repository__meta {\n  overflow: hidden;\n}\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px;\n}\n\n/* CSS Switch */\ninput[switch] {\n  display: none;\n}\ninput[switch] + label {\n  font-size: 1em;\n  line-height: 1;\n  width: 56px;\n  height: 24px;\n  background-color: #79859c;\n  background-image: none;\n  border-radius: 2rem;\n  padding: 0.16667rem;\n  cursor: pointer;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n  font-weight: 500;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:before {\n  color: #eff2f7;\n  content: attr(data-off-label);\n  display: block;\n  font-family: inherit;\n  font-weight: 500;\n  font-size: 12px;\n  line-height: 21px;\n  position: absolute;\n  right: 1px;\n  margin: 3px;\n  top: -2px;\n  text-align: center;\n  min-width: 1.66667rem;\n  overflow: hidden;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:after {\n  content: \"\";\n  position: absolute;\n  left: 3px;\n  background-color: #252b3b;\n  box-shadow: none;\n  border-radius: 2rem;\n  height: 20px;\n  width: 20px;\n  top: 2px;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch]:checked + label {\n  background-color: #0f9cf3;\n}\n\ninput[switch]:checked + label {\n  background-color: #0f9cf3;\n}\ninput[switch]:checked + label:before {\n  color: #fff;\n  content: attr(data-on-label);\n  right: auto;\n  left: 3px;\n}\ninput[switch]:checked + label:after {\n  left: 33px;\n  background-color: #252b3b;\n}\n\ninput[switch=bool] + label {\n  background-color: #f32f53;\n}\n\ninput[switch=bool] + label:before, input[switch=bool]:checked + label:before,\ninput[switch=default]:checked + label:before {\n  color: #fff;\n}\n\ninput[switch=bool]:checked + label {\n  background-color: #6fd088;\n}\n\ninput[switch=default]:checked + label {\n  background-color: #a2a2a2;\n}\n\ninput[switch=primary]:checked + label {\n  background-color: #0f9cf3;\n}\n\ninput[switch=success]:checked + label {\n  background-color: #6fd088;\n}\n\ninput[switch=info]:checked + label {\n  background-color: #0097a7;\n}\n\ninput[switch=warning]:checked + label {\n  background-color: #ffbb44;\n}\n\ninput[switch=danger]:checked + label {\n  background-color: #f32f53;\n}\n\ninput[switch=dark]:checked + label {\n  background-color: #eff2f7;\n}\n\n.square-switch {\n  margin-right: 7px;\n}\n.square-switch input[switch] + label, .square-switch input[switch] + label:after {\n  border-radius: 4px;\n}\n\n.datepicker {\n  border: 1px solid #212529;\n  padding: 8px;\n  z-index: 999 !important;\n}\n.datepicker table tr th {\n  font-weight: 500;\n}\n.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n  background-color: #0f9cf3 !important;\n  background-image: none;\n  box-shadow: none;\n  color: #fff !important;\n}\n.datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n.datepicker table tr td span.focused,\n.datepicker table tr td span:hover {\n  background: #252b3b;\n}\n.datepicker table tr td.new, .datepicker table tr td.old,\n.datepicker table tr td span.new,\n.datepicker table tr td span.old {\n  color: #8590a5;\n  opacity: 0.6;\n}\n.datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n  background-color: #2d3448;\n}\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px;\n}\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.dataTables_wrapper.container-fluid {\n  padding: 0;\n}\n\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right;\n}\n@media (max-width: 767px) {\n  div.dataTables_wrapper div.dataTables_filter {\n    text-align: center;\n  }\n}\ndiv.dataTables_wrapper div.dataTables_filter input {\n  margin-left: 0.5em;\n  margin-right: 0;\n}\n\n.datatable td:focus {\n  outline: none;\n}\n\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {\n  padding-left: 0;\n}\ndiv.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {\n  padding-right: 0;\n}\n\ntable.dataTable {\n  border-collapse: collapse !important;\n  margin-bottom: 15px !important;\n}\ntable.dataTable thead .sorting:before,\ntable.dataTable thead .sorting_asc:before,\ntable.dataTable thead .sorting_desc:before,\ntable.dataTable thead .sorting_asc_disabled:before,\ntable.dataTable thead .sorting_desc_disabled:before {\n  left: auto;\n  right: 0.5rem;\n  content: \"\\f0360\";\n  font-family: \"Material Design Icons\";\n  font-size: 1rem;\n  top: 9px;\n}\ntable.dataTable thead .sorting:after,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc:after,\ntable.dataTable thead .sorting_asc_disabled:after,\ntable.dataTable thead .sorting_desc_disabled:after {\n  left: auto;\n  right: 0.5em;\n  content: \"\\f035d\";\n  font-family: \"Material Design Icons\";\n  top: 15px;\n  font-size: 1rem;\n}\ntable.dataTable thead tr th.sorting_asc, table.dataTable thead tr th.sorting_desc, table.dataTable thead tr th.sorting,\ntable.dataTable thead tr td.sorting_asc,\ntable.dataTable thead tr td.sorting_desc,\ntable.dataTable thead tr td.sorting {\n  padding-left: 12px;\n  padding-right: 30px;\n}\ntable.dataTable tbody > tr.selected, table.dataTable tbody > tr > .selected {\n  background-color: rgba(15, 156, 243, 0.2);\n}\ntable.dataTable tbody > tr.selected td, table.dataTable tbody > tr > .selected td {\n  border-color: rgba(15, 156, 243, 0.2);\n  color: #0f9cf3;\n}\ntable.dataTable tbody td:focus {\n  outline: none !important;\n}\ntable.dataTable tbody th.focus, table.dataTable tbody td.focus {\n  outline: 2px solid #0f9cf3 !important;\n  outline-offset: -1px;\n  background-color: rgba(15, 156, 243, 0.15);\n}\n\n.dataTables_info {\n  font-weight: 600;\n}\n\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr[role=row] > th:first-child:before {\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  background-color: #6fd088;\n  bottom: auto;\n}\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {\n  background-color: #f32f53;\n}\n\ndiv.dt-button-info {\n  background-color: #0f9cf3;\n  border: none;\n  color: #fff;\n  box-shadow: none;\n  border-radius: 3px;\n  text-align: center;\n  z-index: 21;\n}\ndiv.dt-button-info h2 {\n  border-bottom: none;\n  background-color: rgba(255, 255, 255, 0.2);\n  color: #fff;\n}\n\n@media (max-width: 767.98px) {\n  li.paginate_button.previous,\nli.paginate_button.next {\n    display: inline-block;\n    font-size: 1.5rem;\n  }\n\n  li.paginate_button {\n    display: none;\n  }\n\n  .dataTables_paginate ul {\n    text-align: center;\n    display: block;\n    margin: 1rem 0 0 !important;\n  }\n\n  div.dt-buttons {\n    display: inline-table;\n    margin-bottom: 1rem;\n  }\n}\n.activate-select .sorting_1 {\n  background-color: #212529;\n}\n\n.table-bordered {\n  border: 1px solid #2d3448;\n}\n\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td {\n  position: relative;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control {\n  padding-left: 30px;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {\n  top: 64%;\n  left: 5px;\n  height: 14px;\n  width: 14px;\n  margin-top: -14px;\n  display: block;\n  position: absolute;\n  color: #fff;\n  border: 2px solid #fff;\n  border-radius: 14px;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  line-height: 12px;\n  content: \"+\";\n  background-color: #0f9cf3;\n}\n\n.tox-tinymce {\n  border: 2px solid #2d3448 !important;\n}\n\n.tox .tox-statusbar {\n  border-top: 1px solid #2d3448 !important;\n}\n.tox .tox-menubar,\n.tox .tox-edit-area__iframe,\n.tox .tox-statusbar {\n  background-color: #252b3b !important;\n  background: none !important;\n}\n.tox .tox-mbtn {\n  color: #f6f6f6 !important;\n}\n.tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n  background-color: #2d3448 !important;\n}\n.tox .tox-tbtn:hover {\n  background-color: #2d3448 !important;\n}\n.tox .tox-toolbar__primary {\n  border-color: #2d3448 !important;\n}\n.tox .tox-toolbar,\n.tox .tox-toolbar__overflow,\n.tox .tox-toolbar__primary {\n  background: #2d3448 !important;\n}\n.tox .tox-tbtn {\n  color: #f6f6f6 !important;\n}\n.tox .tox-tbtn svg {\n  fill: #f6f6f6 !important;\n}\n.tox .tox-edit-area__iframe {\n  background-color: #252b3b !important;\n}\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: #f6f6f6 !important;\n}\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #232938 !important;\n}\n\n.tox-tinymce-aux {\n  z-index: 1050 !important;\n}\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed #79859c;\n  background: #252b3b;\n  border-radius: 6px;\n}\n.dropzone .dz-message {\n  font-size: 24px;\n  width: 100%;\n}\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  position: relative;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav:before {\n  content: \"\";\n  width: 100%;\n  height: 2px;\n  background-color: #2d3448;\n  position: absolute;\n  left: 0;\n  top: 26px;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n  display: inline-block;\n  width: 38px;\n  height: 38px;\n  line-height: 34px;\n  border: 2px solid #0f9cf3;\n  color: #0f9cf3;\n  text-align: center;\n  border-radius: 50%;\n  position: relative;\n  background-color: #252b3b;\n}\n@media (max-width: 991.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: block;\n    margin: 0 auto 8px !important;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n  display: block;\n  margin-top: 8px;\n  font-weight: 600;\n}\n@media (max-width: 575.98px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n    display: none;\n  }\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n  background-color: transparent;\n  color: #f6f6f6;\n}\n.twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n  background-color: #0f9cf3;\n  color: #fff;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n  display: inline-block;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n  display: inline-block;\n  padding: 0.47rem 0.75rem;\n  background-color: #0f9cf3;\n  color: #fff;\n  border-radius: 0.25rem;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n  cursor: not-allowed;\n  background-color: #36acf5;\n}\n.twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n  float: right;\n}\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px;\n}\n\n.table-rep-plugin .btn-toolbar {\n  display: block;\n}\n.table-rep-plugin .table-responsive {\n  border: none !important;\n}\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #2d3448;\n  color: #eff2f7;\n  border: 1px solid #292f42;\n}\n.table-rep-plugin .btn-group .btn-default.btn-primary {\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n  color: #fff;\n}\n.table-rep-plugin .btn-group.pull-right {\n  float: right;\n}\n.table-rep-plugin .btn-group.pull-right .dropdown-menu {\n  right: 0;\n  transform: none !important;\n  top: 100% !important;\n}\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal;\n}\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: #79859c !important;\n}\n.table-rep-plugin .checkbox-row:hover {\n  background-color: #293041 !important;\n}\n.table-rep-plugin .checkbox-row label {\n  display: inline-block;\n  padding-left: 5px;\n  position: relative;\n}\n.table-rep-plugin .checkbox-row label::before {\n  -o-transition: 0.3s ease-in-out;\n  -webkit-transition: 0.3s ease-in-out;\n  background-color: #fff;\n  border-radius: 3px;\n  border: 1px solid #2d3448;\n  content: \"\";\n  display: inline-block;\n  height: 17px;\n  left: 0;\n  margin-left: -20px;\n  position: absolute;\n  transition: 0.3s ease-in-out;\n  width: 17px;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row label::after {\n  color: #252b3b;\n  display: inline-block;\n  font-size: 11px;\n  height: 16px;\n  left: 0;\n  margin-left: -20px;\n  padding-left: 3px;\n  padding-top: 1px;\n  position: absolute;\n  top: -1px;\n  width: 16px;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label {\n  opacity: 0.65;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:focus + label::before {\n  outline-offset: -2px;\n  outline: none;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  content: \"\\f00c\";\n  font-family: \"Font Awesome 5 Free\";\n  font-weight: 900;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label::before {\n  background-color: #212529;\n  cursor: not-allowed;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::before {\n  background-color: #0f9cf3;\n  border-color: #0f9cf3;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  color: #fff;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #0f9cf3;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header table {\n  color: #fff;\n}\n\n@media (min-width: 992px) {\n  body[data-layout=horizontal] .fixed-solution .sticky-table-header {\n    top: 120px !important;\n  }\n}\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #2d3448;\n  background-color: #292f3f;\n  color: #8590a5;\n  border-radius: 0.25rem;\n}\n.table-edits input:focus, .table-edits select:focus {\n  outline: none;\n  border-color: #31384c;\n}\n\n.apex-charts {\n  min-height: 10px !important;\n}\n.apex-charts text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #8590a5;\n}\n.apex-charts .apexcharts-canvas {\n  margin: 0 auto;\n}\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important;\n}\n\n.apexcharts-legend-series {\n  font-weight: 500;\n}\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #32394e;\n}\n\n.apexcharts-legend-text {\n  color: #919bae !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important;\n}\n\n.apexcharts-pie-label {\n  fill: #fff !important;\n}\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #8590a5;\n}\n\n/* Flot chart */\n.flot-charts-height {\n  height: 320px;\n}\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(239, 242, 247, 0.9);\n  z-index: 100;\n  color: #212529;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);\n  border-radius: 4px;\n}\n\n.legendLabel {\n  color: #8590a5;\n}\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #eff2f7 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #f8f9fa !important;\n}\n\n.jqsfield {\n  color: #252b3b !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important;\n}\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #212529;\n  border-radius: 3px;\n}\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #0f9cf3;\n  border-radius: 4px;\n  padding: 10px 20px;\n}\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute;\n}\n.gmaps-overlay_arrow.above {\n  bottom: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-top: 16px solid #0f9cf3;\n}\n.gmaps-overlay_arrow.below {\n  top: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-bottom: 16px solid #0f9cf3;\n}\n\n.jvectormap-label {\n  border: none;\n  background: #eff2f7;\n  color: #212529;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.9rem;\n  padding: 5px 8px;\n}\n\n.editable-input .form-control {\n  display: inline-block;\n}\n\n.editable-buttons {\n  margin-left: 7px;\n}\n.editable-buttons .editable-cancel {\n  margin-left: 7px;\n}\n\n/* ==============\n  Email\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px;\n}\n\n.email-rightbar {\n  margin-left: 260px;\n}\n\n.chat-user-box p.user-title {\n  color: #eff2f7;\n  font-weight: 600;\n}\n.chat-user-box p {\n  font-size: 12px;\n}\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%;\n  }\n\n  .email-rightbar {\n    margin: 0;\n  }\n}\n.mail-list a {\n  display: block;\n  color: #919bae;\n  line-height: 24px;\n  padding: 8px 5px;\n}\n.mail-list a.active {\n  color: #f32f53;\n  font-weight: 500;\n}\n\n.message-list {\n  display: block;\n  padding-left: 0;\n}\n.message-list li {\n  position: relative;\n  display: block;\n  height: 50px;\n  line-height: 50px;\n  cursor: default;\n  transition-duration: 0.3s;\n}\n.message-list li a {\n  color: #919bae;\n}\n.message-list li:hover {\n  background: #2d3448;\n  transition-duration: 0.05s;\n}\n.message-list li .col-mail {\n  float: left;\n  position: relative;\n}\n.message-list li .col-mail-1 {\n  width: 320px;\n}\n.message-list li .col-mail-1 .star-toggle,\n.message-list li .col-mail-1 .checkbox-wrapper-mail,\n.message-list li .col-mail-1 .dot {\n  display: block;\n  float: left;\n}\n.message-list li .col-mail-1 .dot {\n  border: 4px solid transparent;\n  border-radius: 100px;\n  margin: 22px 26px 0;\n  height: 0;\n  width: 0;\n  line-height: 0;\n  font-size: 0;\n}\n.message-list li .col-mail-1 .checkbox-wrapper-mail {\n  margin: 15px 10px 0 20px;\n}\n.message-list li .col-mail-1 .star-toggle {\n  margin-top: 18px;\n  margin-left: 5px;\n}\n.message-list li .col-mail-1 .title {\n  position: absolute;\n  top: 0;\n  left: 110px;\n  right: 0;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  margin-bottom: 0;\n}\n.message-list li .col-mail-2 {\n  position: absolute;\n  top: 0;\n  left: 320px;\n  right: 0;\n  bottom: 0;\n}\n.message-list li .col-mail-2 .subject,\n.message-list li .col-mail-2 .date {\n  position: absolute;\n  top: 0;\n}\n.message-list li .col-mail-2 .subject {\n  left: 0;\n  right: 200px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n.message-list li .col-mail-2 .date {\n  right: 0;\n  width: 170px;\n  padding-left: 80px;\n}\n.message-list li.active, .message-list li.active:hover {\n  box-shadow: inset 3px 0 0 #0f9cf3;\n}\n.message-list li.unread {\n  background-color: #2d3448;\n  font-weight: 500;\n  color: #dee4ef;\n}\n.message-list li.unread a {\n  color: #dee4ef;\n  font-weight: 500;\n}\n.message-list .checkbox-wrapper-mail {\n  cursor: pointer;\n  height: 20px;\n  width: 20px;\n  position: relative;\n  display: inline-block;\n  box-shadow: inset 0 0 0 1px #79859c;\n  border-radius: 1px;\n}\n.message-list .checkbox-wrapper-mail input {\n  opacity: 0;\n  cursor: pointer;\n}\n.message-list .checkbox-wrapper-mail input:checked ~ label {\n  opacity: 1;\n}\n.message-list .checkbox-wrapper-mail label {\n  position: absolute;\n  height: 20px;\n  width: 20px;\n  left: 0;\n  cursor: pointer;\n  opacity: 0;\n  margin-bottom: 0;\n  transition-duration: 0.05s;\n  top: 0;\n}\n.message-list .checkbox-wrapper-mail label:before {\n  content: \"\\f012c\";\n  font-family: \"Material Design Icons\";\n  top: 0;\n  height: 20px;\n  color: #dee4ef;\n  width: 20px;\n  position: absolute;\n  margin-top: -16px;\n  left: 4px;\n  font-size: 13px;\n}\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px;\n  }\n}\n/* ==============\n  Timeline\n===================*/\n.cd-container {\n  width: 90%;\n  max-width: 1170px;\n  margin: 0 auto;\n}\n\n.cd-container::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n#cd-timeline {\n  margin-bottom: 2em;\n  margin-top: 2em;\n  padding: 2em 0;\n  position: relative;\n}\n#cd-timeline::before {\n  border-left: 3px solid #2d3448;\n  content: \"\";\n  height: 100%;\n  left: 18px;\n  position: absolute;\n  top: 0;\n  width: 3px;\n}\n\n@media only screen and (min-width: 1170px) {\n  #cd-timeline {\n    margin-bottom: 3em;\n    margin-top: 3em;\n  }\n  #cd-timeline::before {\n    left: 50%;\n    margin-left: -2px;\n  }\n}\n.cd-timeline-block {\n  margin: 2em 0;\n  position: relative;\n}\n.cd-timeline-block:after {\n  clear: both;\n  content: \"\";\n  display: table;\n}\n\n.cd-timeline-block:first-child {\n  margin-top: 0;\n}\n\n.cd-timeline-block:last-child {\n  margin-bottom: 0;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-block {\n    margin: 4em 0;\n  }\n\n  .cd-timeline-block:first-child {\n    margin-top: 0;\n  }\n\n  .cd-timeline-block:last-child {\n    margin-bottom: 0;\n  }\n}\n.cd-timeline-img {\n  position: absolute;\n  top: 20px;\n  left: 0;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  text-align: center;\n  line-height: 30px;\n  font-size: 20px;\n  color: #fff;\n  background-color: #0f9cf3;\n  border: 5px solid #fff;\n}\n.cd-timeline-img i {\n  margin-left: 1px;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-img {\n    width: 40px;\n    height: 40px;\n    line-height: 30px;\n    left: 50%;\n    margin-left: -20px;\n  }\n\n  .cssanimations .cd-timeline-img.is-hidden {\n    visibility: hidden;\n  }\n\n  .cssanimations .cd-timeline-img.bounce-in {\n    visibility: visible;\n    -webkit-animation: cd-bounce-1 0.6s;\n    -moz-animation: cd-bounce-1 0.6s;\n    animation: cd-bounce-1 0.6s;\n  }\n}\n.cd-timeline-content {\n  border-radius: 5px;\n  border: 1px solid #2d3448;\n  margin-left: 60px;\n  padding: 1em;\n  position: relative;\n}\n.cd-timeline-content:after {\n  clear: both;\n  content: \"\";\n  display: table;\n}\n.cd-timeline-content h2 {\n  margin-top: 0;\n}\n.cd-timeline-content .cd-read-more {\n  background: #0f9cf3;\n  border-radius: 0.25em;\n  color: white;\n  display: inline-block;\n  float: right;\n  font-size: 14px;\n  padding: 0.8em 1em;\n}\n.cd-timeline-content .cd-date {\n  display: inline-block;\n  font-size: 14px;\n}\n.cd-timeline-content h3 {\n  font-size: 18px;\n  margin: 0 0 15px 0;\n}\n\n.no-touch .cd-timeline-content .cd-read-more:hover {\n  background-color: #bac4cb;\n}\n\n.cd-timeline-content .cd-date {\n  float: left;\n  padding: 0.8em 0;\n  opacity: 0.7;\n}\n\n.cd-timeline-content::before {\n  content: \"\";\n  position: absolute;\n  top: 16px;\n  right: 100%;\n  height: 0;\n  width: 0;\n  border: 12px solid transparent;\n  border-right: 12px solid #2d3448;\n}\n\n@media only screen and (min-width: 1170px) {\n  .cd-timeline-content {\n    margin-left: 0;\n    padding: 1.6em;\n    width: 45%;\n  }\n\n  .cd-timeline-content::before {\n    top: 24px;\n    left: 100%;\n    border-color: transparent;\n    border-left-color: #2d3448;\n  }\n\n  .cd-timeline-content .cd-read-more {\n    float: left;\n  }\n\n  .cd-timeline-content .cd-date {\n    position: absolute;\n    width: 100%;\n    left: 122%;\n    top: 20px;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content {\n    float: right;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content::before {\n    top: 24px;\n    left: auto;\n    right: 100%;\n    border-color: transparent;\n    border-right-color: #2d3448;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-read-more {\n    float: right;\n  }\n\n  .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-date {\n    left: auto;\n    right: 122%;\n    text-align: right;\n  }\n\n  .cssanimations .cd-timeline-content.is-hidden {\n    visibility: hidden;\n  }\n\n  .cssanimations .cd-timeline-content.bounce-in {\n    visibility: visible;\n    -webkit-animation: cd-bounce-2 0.6s;\n    -moz-animation: cd-bounce-2 0.6s;\n    animation: cd-bounce-2 0.6s;\n  }\n}\n@media only screen and (min-width: 1170px) {\n  .cssanimations .cd-timeline-block:nth-child(even) .cd-timeline-content.bounce-in {\n    -webkit-animation: cd-bounce-2-inverse 0.6s;\n    -moz-animation: cd-bounce-2-inverse 0.6s;\n    animation: cd-bounce-2-inverse 0.6s;\n  }\n}\n.social-links li a {\n  background: #353d55;\n  border-radius: 50%;\n  color: white;\n  display: inline-block;\n  height: 30px;\n  line-height: 30px;\n  text-align: center;\n  width: 30px;\n}\n\n.auth-body-bg {\n  background-image: url(../images/auth-bg.jpg);\n  background-repeat: no-repeat;\n  background-size: cover;\n  background-position: center;\n}\n.auth-body-bg .bg-overlay {\n  background: rgba(51, 51, 51, 0.97);\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0px;\n  top: 0px;\n}\n\n.wrapper-page {\n  margin: 7.5% auto;\n  max-width: 460px;\n  position: relative;\n}\n.wrapper-page .auth-logo {\n  font-size: 28px;\n  line-height: 70px;\n}\n.wrapper-page .auth-logo.logo-light {\n  display: block;\n}\n.wrapper-page .auth-logo.logo-dark {\n  display: none;\n}\n\n.ex-page-content h1 {\n  font-size: 98px;\n  font-weight: 700;\n  line-height: 150px;\n  text-shadow: rgba(61, 61, 61, 0.3) 1px 1px, rgba(61, 61, 61, 0.2) 2px 2px, rgba(61, 61, 61, 0.3) 3px 3px;\n}", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n    padding-bottom: $grid-gutter-width;\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        font-size: 15px;\r\n        text-transform: uppercase;\r\n        font-weight: 600;\r\n    }\r\n}\r\n", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    color: $footer-color;\r\n    left: $sidebar-width;\r\n    height: $footer-height;\r\n    box-shadow: $box-shadow;\r\n    background-color: $footer-bg;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 10px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(-180deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0140\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.5rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 13.3px;\r\n                transition: all .4s;\r\n                font-family: $font-family-secondary;\r\n                font-weight: 500;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.5rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 1.1rem;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                    opacity: .75;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 4px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 3.2rem;\r\n                        font-size: 13px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4.2rem;\r\n                                font-size: 13.5px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n    font-family: $font-family-secondary;\r\n    opacity: .5;\r\n}\r\n\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 20px;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            color: $primary;\r\n                            background-color: darken($sidebar-bg, 4%);\r\n                            transition: none;\r\n\r\n                            i{\r\n                                color: $primary;\r\n                            }\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 12px -4px rgba(18, 19, 21, 0.1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n\r\n    .user-profile{\r\n        h4{\r\n            color: $white;\r\n        }\r\n        span{\r\n            color: $gray-400 !important;\r\n        }\r\n    }\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1400px;\r\n\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: lighten($card-bg, 1%);\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n\r\n        @media (max-width: 992px) {\r\n            width: auto;\r\n        }\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n        @media (max-width: 991px){\r\n            left: 0;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            &.menu-title{\r\n                background-color: lighten($sidebar-dark-bg, 2%);\r\n            }\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li {\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        li {\r\n                            a{\r\n                                padding-left: 1.5rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n// Colored Sidebar\r\n\r\nbody[data-sidebar=\"colored\"] {\r\n    .user-profile{\r\n        h4{\r\n            color: $white;\r\n        }\r\n        span{\r\n            color: $gray-400 !important;\r\n        }\r\n    }\r\n\r\n    .vertical-menu {\r\n        background: $primary;\r\n    }\r\n    .navbar-brand-box{\r\n        background-color: $primary;\r\n        .logo-dark{\r\n            display: none;\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                &.menu-title{\r\n                    color: rgba($white, 0.6);\r\n                }\r\n  \r\n                a{\r\n                    color: rgba($white, 0.6);\r\n                    i{\r\n                        color: rgba($white, 0.6);\r\n                    }\r\n                    &.waves-effect {\r\n                        .waves-ripple {\r\n                          background: rgba($white, 0.1);\r\n                        }\r\n                    }\r\n                }\r\n  \r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            color: rgba($white,.5);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                >ul{\r\n                    >li{\r\n                        &:hover>a{\r\n                            background-color: lighten($primary, 2%);\r\n                            color: $white;\r\n                            i{\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                       \r\n                    }\r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $white !important;\r\n        > a{\r\n            color: $white !important;\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $white !important;\r\n        }\r\n        .active {\r\n            color: $white !important;\r\n\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $white !important;\r\n    }\r\n}\r\n\r\n.vertical-collpsed {\r\n    .user-profile{\r\n        display: none;\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: $topnav-bg;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 15px;\r\n            position: relative;\r\n            padding: 1rem 1.3rem;\r\n            color: $menu-item-color;\r\n            font-family: $font-family-secondary;\r\n\r\n            i{\r\n                font-size: 15px;\r\n                vertical-align: middle;\r\n                display: inline-block;\r\n            }\r\n            &:focus, &:hover{\r\n                color: $menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: $menu-item-color;\r\n            &.active, &:hover{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: $menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: $menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: $display-block;\r\n            span.logo-sm{\r\n                display: $display-block;\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 15px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n        \r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n\r\n        .topnav{\r\n            background-color: $header-dark-bg;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: $boxed-body-bg;\r\n    #layout-wrapper {\r\n        background-color: $body-bg;\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: $primary;\n  color: $white;\n  display: flex;\n  font-weight: $font-weight-medium;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n\n// avatar group\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n  .avatar-group-item {\n    margin-left: -12px;\n    border: 2px solid $card-bg;\n    border-radius: 50%;\n    transition: all 0.2s;\n    &:hover{\n      position: relative;\n      transform: translateY(-2px);\n    }\n  }\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .card-header{\r\n        border-radius: 7px;\r\n    }\r\n}\r\n\r\n.custom-accordion-arrow{\r\n    .card{\r\n        border: 1px solid $border-color;\r\n        box-shadow: none;\r\n    }\r\n    .card-header{\r\n        padding-left: 45px;\r\n        position: relative;\r\n\r\n        .accor-arrow-icon{\r\n            position: absolute;\r\n            display: inline-block;\r\n            width: 24px;\r\n            height: 24px;\r\n            line-height: 24px;\r\n            font-size: 16px;\r\n            background-color: $primary;\r\n            color: $white;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            left: 10px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-arrow-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n// Font Family\n.font-family-secondary {\n    font-family: $font-family-secondary;\n}\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 2px);\n    display: block;\n    border: 1px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n\n.w-xs {\n    min-width: 80px;\n}\n\n.w-sm {\n    min-width: 95px;\n}\n\n.w-md {\n    min-width: 110px;\n}\n\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n// overlay\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    opacity: 0.7;\n    background-color: $black;\n}\n\n// flex-1\n\n.flex-1{\n    flex: 1;\n}\n\n\n\n// alert\n\n.alert-dismissible {\n    .btn-close {\n        font-size: 10px;\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\n        background: transparent escape-svg($btn-close-bg-dark) center / $btn-close-width auto no-repeat;\n    }\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner{\r\n    .spin-icon {\r\n        font-size: 56px;\r\n        color: $primary;\r\n        position: relative;\r\n        display: inline-block;\r\n        animation: spin 1.6s infinite linear;\r\n    }\r\n}\r\n\r\n@keyframes spin {\r\n    0% {\r\n      transform: rotate(0deg);\r\n    }\r\n    100% {\r\n      transform: rotate(359deg);\r\n    }\r\n}\r\n  ", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;;\r\n  .form-check-input{\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n  .form-check-label{\r\n    display: block;\r\n  }\r\n}\r\n\r\n.form-check{\r\n  position: relative;\r\n  text-align: left /*rtl: right*/;\r\n}\r\n\r\n\r\n.form-check-label{\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n\r\n\r\n// activity widget\r\n\r\n.activity-wid{\r\n    margin-top: 8px;\r\n    margin-left: 16px;\r\n\r\n    .activity-list{\r\n        position: relative;\r\n        padding: 0 0 40px 30px;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            border-left: 2px dashed rgba($primary,0.25);\r\n            position: absolute;\r\n            left: 0;\r\n            bottom: 0;\r\n            top: 32px\r\n        }\r\n        .activity-icon{\r\n            position: absolute;\r\n            left: -15px;\r\n            top: 0;\r\n            z-index: 9;\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n}", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n  color: $gray-500;\r\n\r\n  i{\r\n    display: inline-block;\r\n    width: 40px;\r\n    height: 40px;\r\n    line-height: 36px;\r\n    font-size: 22px;\r\n    color: $gray-600;\r\n    border: 2px solid $gray-300;\r\n    border-radius: 4px;\r\n    transition: all 0.4s;\r\n    text-align: center;\r\n    margin-right: 16px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .col-lg-4 {\r\n    margin-top: 24px;\r\n\r\n    &:hover {\r\n      i {\r\n        color: $white;\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  display: block;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.fc-toolbar {\r\n  h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $light;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n      font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n.fc-unthemed{\r\n  .fc-content, \r\n  .fc-divider, \r\n  .fc-list-heading td, \r\n  .fc-list-view, \r\n  .fc-popover, \r\n  .fc-row, \r\n  tbody, \r\n  td, \r\n  th, \r\n  thead{\r\n      border-color: $light;\r\n  }\r\n  td.fc-today {\r\n      background: lighten($gray-200, 4%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $card-bg;\r\n  border-color: $border-color;\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n#external-events .external-event {\r\n  text-align: left!important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.fc-event, .fc-event-dot{\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content{\r\n  color: $white;\r\n}\r\n\r\n.fc {\r\n  .table-bordered {\r\n    td, th {\r\n      border-color: $table-group-separator-color;\r\n    }\r\n  }\r\n  \r\n  .fc-toolbar {\r\n    @media (max-width: 575.98px) {\r\n      display: block;\r\n    }\r\n    \r\n      h2 {\r\n          font-size: 16px;\r\n          line-height: 30px;\r\n          text-transform: uppercase;\r\n      }\r\n\r\n      @media (max-width: 767.98px) {\r\n\r\n          .fc-left,\r\n          .fc-right,\r\n          .fc-center {\r\n              float: none;\r\n              display: block;\r\n              text-align: center;\r\n              clear: both;\r\n              margin: 10px 0;\r\n          }\r\n\r\n          >*>* {\r\n              float: none;\r\n          }\r\n\r\n          .fc-today-button {\r\n              display: none;\r\n          }\r\n      }\r\n      \r\n      .btn {\r\n          text-transform: capitalize;\r\n      }\r\n\r\n  }\r\n}\r\n.fc-bootstrap .fc-today.alert-info{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  background-color: $black !important;\r\n}\r\n\r\n// RTL\r\n[dir=\"rtl\"] .fc-header-toolbar {\r\n  direction: ltr !important;\r\n}\r\n\r\n[dir=\"rtl\"] .fc-toolbar>*>:not(:first-child) {\r\n  margin-left: .75em;\r\n}\r\n\r\n\r\n\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid $border-color;\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: rtl;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "\r\n//\r\n// Round slider\r\n//\r\n\r\n.rs-control{\r\n  margin: 0px auto;\r\n}\r\n\r\n.rs-path-color{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.rs-bg-color{\r\n  background-color: $card-bg;\r\n}\r\n\r\n.rs-border{\r\n  border-color: transparent;\r\n}\r\n\r\n.rs-handle{\r\n  background-color: $gray-700;\r\n}\r\n\r\n.rs-circle-border{\r\n  .rs-border{\r\n    border: 8px solid $gray-300;\r\n  }\r\n}\r\n\r\n.rs-disabled{\r\n  opacity: 1;\r\n}\r\n\r\n// Outer border\r\n\r\n.outer-border {\r\n  .rs-border{\r\n    border-width: 0px;\r\n    &.rs-outer  {\r\n      border: 14px solid $gray-300;\r\n  }\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n  .rs-path-color{\r\n    background-color: transparent;\r\n  }\r\n}\r\n\r\n// Outer border dot\r\n\r\n.outer-border-dot {\r\n  .rs-border.rs-outer  {\r\n    border: 16px dotted;\r\n  }\r\n  .rs-handle{\r\n    margin-left: 0 !important;\r\n  }\r\n}\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n    .rs-range-#{$color} {\r\n        .rs-range-color{\r\n          background-color: $value;\r\n        }\r\n\r\n        .rs-handle-dot{\r\n          background-color: lighten(($value), 24%);\r\n          border-color: $value;\r\n          &:after{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.rs-circle-border{\r\n          .rs-handle{\r\n            background-color: $value;\r\n          }\r\n        }\r\n\r\n        &.outer-border-dot {\r\n          .rs-border.rs-outer  {\r\n            border-color: lighten(($value), 24%);\r\n          }\r\n        }\r\n    }\r\n}\r\n\r\n// rs-handle-arrow\r\n\r\n.rs-handle-arrow{\r\n    .rs-handle  {\r\n      background-color: transparent;\r\n      border: 8px solid transparent;\r\n      border-right-color:$gray-700;\r\n      margin: -6px 0px 0px 14px !important;\r\n      border-width: 6px 104px 6px 4px;\r\n      &:before  {\r\n        display: block;\r\n        content: \" \";\r\n        position: absolute;\r\n        height: 22px;\r\n        width: 22px;\r\n        background:$gray-700;\r\n        right: -11px;\r\n        bottom: -11px;\r\n        border-radius: 100px;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.rs-handle-arrow-dash{\r\n  .rs-handle  {\r\n    background-color: transparent;\r\n    border: 8px solid transparent;\r\n    border-right-color: $gray-700;\r\n    margin: -8px 0 0 14px !important;\r\n    &:before  {\r\n      display: block;\r\n      content: \" \";\r\n      position: absolute;\r\n      height: 12px;\r\n      width: 12px;\r\n      background: $gray-700;\r\n      right: -6px;\r\n      bottom: -6px;\r\n      border-radius: 100%;\r\n  }\r\n  &:after{\r\n    display: block;\r\n    content: \" \";\r\n    width: 80px;\r\n    position: absolute;\r\n    top: -1px;\r\n    right: 0px;\r\n    border-top: 2px dotted $gray-700\r\n  }\r\n}\r\n\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.irs--round {\r\n\r\n    .irs-bar,\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        background: $primary !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        &:before {\r\n            display: none;\r\n        }\r\n    }\r\n\r\n    .irs-line {\r\n        background: $gray-300;\r\n        border-color: $gray-300;\r\n    }\r\n\r\n    .irs-grid-text {\r\n        font-size: 11px;\r\n        color: $gray-500;\r\n    }\r\n\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-500;\r\n        background: $gray-300;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-handle {\r\n        border: 2px solid $primary;\r\n        width: 16px;\r\n        height: 16px;\r\n        top: 29px;\r\n        background-color: $card-bg !important;\r\n    }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 24px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-content{\r\n  font-size: 16px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n\r\n    &.toast-top-full-width, &.toast-bottom-full-width{\r\n        > div{\r\n          min-width: 96%;\r\n          margin: 4px auto;\r\n        }\r\n      }\r\n\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}\r\n\r\n.toastr-options{\r\n    padding: 24px;\r\n    background-color: lighten($gray-200, 2%);\r\n    margin-bottom: 0;\r\n    border: 1px solid $border-color;\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n  display: block;\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: $body-color;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid $gray-300;\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: $gray-200;\r\n      border: 1px solid $gray-300;\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: $gray-400;\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $gray-100;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}", "\r\n//\r\n// Bootstrap touchspin\r\n//\r\n\r\n\r\n.bootstrap-touchspin{\r\n    &.input-group{\r\n      &>.input-group-prepend{\r\n        &>.btn, &>.input-group-text{\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  \r\n    &.input-group{\r\n      &>.input-group-append{\r\n        &>.btn, &>.input-group-text{\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }", "//\r\n// datatable.scss\r\n\r\n\r\n.dataTables_wrapper {\r\n  &.container-fluid {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter {\r\n    text-align: right;\r\n\r\n    @media (max-width: 767px) {\r\n      text-align: center;\r\n    }\r\n\r\n\r\n    input {\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.datatable {\r\n  td {\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n  }\r\n}\r\n\r\ndiv.table-responsive>div.dataTables_wrapper>div.row>div[class^=\"col-\"] {\r\n  &:first-child {\r\n    padding-left: 0;\r\n  }\r\n\r\n  &:last-child {\r\n    padding-right: 0;\r\n  }\r\n}\r\n\r\n\r\ntable.dataTable {\r\n  border-collapse: collapse !important;\r\n  margin-bottom: 15px !important;\r\n\r\n  // Change icons view\r\n  thead {\r\n\r\n    .sorting,\r\n    .sorting_asc,\r\n    .sorting_desc,\r\n    .sorting_asc_disabled,\r\n    .sorting_desc_disabled {\r\n      &:before {\r\n        left: auto;\r\n        right: 0.5rem;\r\n        content: \"\\F0360\";\r\n        font-family: \"Material Design Icons\";\r\n        font-size: 1rem;\r\n        top: 9px;\r\n\r\n\r\n      }\r\n\r\n      &:after {\r\n        left: auto;\r\n        right: 0.5em;\r\n        content: \"\\F035D\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 15px;\r\n        font-size: 1rem;\r\n      }\r\n    }\r\n\r\n    tr {\r\n\r\n      th,\r\n      td {\r\n\r\n        &.sorting_asc,\r\n        &.sorting_desc,\r\n        &.sorting {\r\n          padding-left: 12px;\r\n          padding-right: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  tbody {\r\n    // Multi select table\r\n\r\n    > tr.selected, >tr>.selected {\r\n        background-color: rgba($primary,.2);\r\n        \r\n        td {\r\n            border-color: rgba($primary,.2);\r\n            color: $primary;\r\n        }\r\n    }\r\n    td {\r\n        &:focus {\r\n            outline: none !important;\r\n        }\r\n    }\r\n    // Key Tables\r\n    th.focus,td.focus{\r\n        outline: 2px solid $primary !important;\r\n        outline-offset: -1px;\r\n        background-color: rgba($primary, 0.15);\r\n    }\r\n}\r\n}\r\n\r\n.dataTables_info {\r\n  font-weight: $font-weight-semibold;\r\n}\r\n\r\n\r\n// Responsive data table\r\ntable.dataTable.dtr-inline.collapsed {\r\n  >tbody {\r\n    >tr[role=row] {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            box-shadow: $box-shadow-lg;\r\n            background-color: $success;\r\n            bottom: auto;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    >tr.parent {\r\n\r\n      >td,\r\n      >th {\r\n        &:first-child {\r\n          &:before {\r\n            background-color: $danger;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Data Table copy button\r\ndiv.dt-button-info {\r\n  background-color: $primary;\r\n  border: none;\r\n  color: $white;\r\n  box-shadow: none;\r\n  border-radius: 3px;\r\n  text-align: center;\r\n  z-index: 21;\r\n\r\n  h2 {\r\n    border-bottom: none;\r\n    background-color: rgba($white, 0.2);\r\n    color: $white;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n\r\n  li.paginate_button.previous,\r\n  li.paginate_button.next {\r\n    display: inline-block;\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  li.paginate_button {\r\n    display: none;\r\n  }\r\n\r\n  .dataTables_paginate {\r\n    ul {\r\n      text-align: center;\r\n      display: block;\r\n      margin: $spacer 0 0 !important;\r\n    }\r\n  }\r\n\r\n  div.dt-buttons {\r\n    display: inline-table;\r\n    margin-bottom: $spacer;\r\n  }\r\n}\r\n\r\n// Active status\r\n.activate-select {\r\n  .sorting_1 {\r\n    background-color: $gray-100;\r\n  }\r\n}\r\n\r\n\r\n\r\n// datatable\r\n\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\n\r\n.table,\r\ntable {\r\n  &.dataTable {\r\n    &.dtr-inline.collapsed>tbody>tr>td {\r\n      position: relative;\r\n\r\n      &.dtr-control {\r\n        padding-left: 30px;\r\n\r\n        &:before {\r\n          top: 64%;\r\n          left: 5px;\r\n          height: 14px;\r\n          width: 14px;\r\n          margin-top: -14px;\r\n          display: block;\r\n          position: absolute;\r\n          color: $white;\r\n          border: 2px solid $white;\r\n          border-radius: 14px;\r\n          box-sizing: content-box;\r\n          text-align: center;\r\n          text-indent: 0 !important;\r\n          line-height: 12px;\r\n          content: '+';\r\n          background-color: $primary;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce \r\n\r\n.tox-tinymce {\r\n    border: 2px solid $gray-300 !important;\r\n}\r\n\r\n.tox {\r\n    .tox-statusbar {\r\n        border-top: 1px solid $gray-300 !important;\r\n    }\r\n\r\n    .tox-menubar,\r\n    .tox-edit-area__iframe,\r\n    .tox-statusbar {\r\n        background-color: $card-bg !important;\r\n        background: none !important;\r\n    }\r\n\r\n    .tox-mbtn {\r\n        color: $gray-700 !important;\r\n\r\n        &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-tbtn {\r\n        &:hover {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar__primary {\r\n        border-color: $gray-300 !important;\r\n    }\r\n\r\n    .tox-toolbar,\r\n    .tox-toolbar__overflow,\r\n    .tox-toolbar__primary {\r\n        background: $gray-300 !important;\r\n    }\r\n\r\n    .tox-tbtn {\r\n        color: $gray-700 !important;\r\n\r\n        svg {\r\n            fill: $gray-700 !important;\r\n        }\r\n    }\r\n\r\n    .tox-edit-area__iframe {\r\n        background-color: $card-bg !important;\r\n    }\r\n\r\n    .tox-statusbar a,\r\n    .tox-statusbar__path-item,\r\n    .tox-statusbar__wordcount {\r\n        color: $gray-700 !important;\r\n    }\r\n\r\n    &:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\r\n        border-right: 1px solid darken($gray-300, 5%) !important;\r\n    }\r\n}\r\n.tox-tinymce-aux {\r\n    z-index: 1050 !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed $gray-400;\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard {\r\n\r\n    .twitter-bs-wizard-nav {\r\n        position: relative;\r\n\r\n        &:before {\r\n            content: \"\";\r\n            width: 100%;\r\n            height: 2px;\r\n            background-color: $gray-300;\r\n            position: absolute;\r\n            left: 0;\r\n            top: 26px;\r\n        }\r\n\r\n        .step-number {\r\n            display: inline-block;\r\n            width: 38px;\r\n            height: 38px;\r\n            line-height: 34px;\r\n            border: 2px solid $primary;\r\n            color: $primary;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n            position: relative;\r\n            background-color: $card-bg;\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n                margin: 0 auto 8px !important;\r\n            }\r\n        }\r\n\r\n        .nav-link {\r\n            .step-title {\r\n                display: block;\r\n                margin-top: 8px;\r\n                font-weight: $font-weight-bold;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: $gray-700;\r\n\r\n                .step-number {\r\n                    background-color: $primary;\r\n                    color: $white;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        padding-top: 24px;\r\n        padding-left: 0;\r\n        list-style: none;\r\n        margin-bottom: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n\r\n            a {\r\n                display: inline-block;\r\n                padding: .47rem .75rem;\r\n                background-color: $primary;\r\n                color: $white;\r\n                border-radius: .25rem;\r\n            }\r\n\r\n            &.disabled {\r\n                a {\r\n                    cursor: not-allowed;\r\n                    background-color: lighten($primary, 8%);\r\n                }\r\n            }\r\n\r\n            &.next {\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content {\r\n    padding-top: 24px;\r\n    min-height: 262px;\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $light;\r\n      color: $dark;\r\n      border: 1px solid darken($light, 2%);\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 50px !important;;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}", "\r\n\r\n/* Flot chart */\r\n.flot-charts-height {\r\n  height: 320px;\r\n}\r\n\r\n.flotTip {\r\n  padding: 8px 12px;\r\n  background-color: rgba($dark, 0.9);\r\n  z-index: 100;\r\n  color: $gray-100;\r\n  box-shadow: $box-shadow;\r\n  border-radius: 4px;\r\n}\r\n\r\n.legendLabel{\r\n  color: $gray-500;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// x editable.scss\r\n//\r\n\r\n.editable-input{\r\n    .form-control{\r\n      display: inline-block;\r\n    }\r\n  }\r\n  \r\n  .editable-buttons{\r\n    margin-left: 7px;\r\n    .editable-cancel{\r\n      margin-left: 7px;\r\n    }\r\n  }", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: $dark;\r\n    font-weight: 600;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: $gray-300;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: $gray-300;\r\n    font-weight: 500;\r\n    color: darken($dark,5%);\r\n      a{\r\n        color: darken($dark,5%);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "/* ==============\r\n  Timeline\r\n===================*/\r\n\r\n.cd-container {\r\n    width: 90%;\r\n    max-width: 1170px;\r\n    margin: 0 auto;\r\n  }\r\n  .cd-container::after {\r\n    content: '';\r\n    display: table;\r\n    clear: both;\r\n  }\r\n  #cd-timeline {\r\n    margin-bottom: 2em;\r\n    margin-top: 2em;\r\n    padding: 2em 0;\r\n    position: relative;\r\n    &::before {\r\n      border-left: 3px solid $light;\r\n      content: '';\r\n      height: 100%;\r\n      left: 18px;\r\n      position: absolute;\r\n      top: 0;\r\n      width: 3px;\r\n    }\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    #cd-timeline {\r\n      margin-bottom: 3em;\r\n      margin-top: 3em;\r\n      &::before {\r\n        left: 50%;\r\n        margin-left: -2px;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .cd-timeline-block {\r\n    margin: 2em 0;\r\n    position: relative;\r\n    &:after {\r\n      clear: both;\r\n      content: \"\";\r\n      display: table;\r\n    }\r\n  }\r\n  .cd-timeline-block:first-child {\r\n    margin-top: 0;\r\n  }\r\n  .cd-timeline-block:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-block {\r\n      margin: 4em 0;\r\n    }\r\n    .cd-timeline-block:first-child {\r\n      margin-top: 0;\r\n    }\r\n    .cd-timeline-block:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  .cd-timeline-img {\r\n    position: absolute;\r\n    top: 20px;\r\n    left: 0;\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    font-size: 20px;\r\n    color: $white;\r\n    background-color: $primary;\r\n    border: 5px solid $white;\r\n  \r\n    i {\r\n      margin-left: 1px;\r\n    }\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-img {\r\n      width: 40px;\r\n      height: 40px;\r\n      line-height: 30px;\r\n      left: 50%;\r\n      margin-left: -20px;\r\n    }\r\n    .cssanimations .cd-timeline-img.is-hidden {\r\n      visibility: hidden;\r\n    }\r\n    .cssanimations .cd-timeline-img.bounce-in {\r\n      visibility: visible;\r\n      -webkit-animation: cd-bounce-1 0.6s;\r\n      -moz-animation: cd-bounce-1 0.6s;\r\n      animation: cd-bounce-1 0.6s;\r\n    }\r\n  }\r\n  \r\n  .cd-timeline-content {\r\n    border-radius: 5px;\r\n    border: 1px solid $light;\r\n    margin-left: 60px;\r\n    padding: 1em;\r\n    position: relative;\r\n  \r\n    &:after {\r\n      clear: both;\r\n      content: \"\";\r\n      display: table;\r\n    }\r\n    h2 {\r\n      margin-top: 0;\r\n    }\r\n    .cd-read-more {\r\n      background: $primary;\r\n      border-radius: 0.25em;\r\n      color: white;\r\n      display: inline-block;\r\n      float: right;\r\n      font-size: 14px;\r\n      padding: .8em 1em;\r\n    }\r\n    .cd-date {\r\n      display: inline-block;\r\n      font-size: 14px;\r\n    }\r\n    h3 {\r\n      font-size: 18px;\r\n      margin: 0 0 15px 0;\r\n    }\r\n  }\r\n  \r\n  .no-touch .cd-timeline-content .cd-read-more:hover {\r\n    background-color: #bac4cb;\r\n  }\r\n  .cd-timeline-content .cd-date {\r\n    float: left;\r\n    padding: .8em 0;\r\n    opacity: .7;\r\n  }\r\n  .cd-timeline-content::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 16px;\r\n    right: 100%;\r\n    height: 0;\r\n    width: 0;\r\n    border: 12px solid transparent;\r\n    border-right: 12px solid $light;\r\n  }\r\n  @media only screen and (min-width: 1170px) {\r\n    .cd-timeline-content {\r\n      margin-left: 0;\r\n      padding: 1.6em;\r\n      width: 45%;\r\n    }\r\n    .cd-timeline-content::before {\r\n      top: 24px;\r\n      left: 100%;\r\n      border-color: transparent;\r\n      border-left-color: $light;\r\n    }\r\n    .cd-timeline-content .cd-read-more {\r\n      float: left;\r\n    }\r\n    .cd-timeline-content .cd-date {\r\n      position: absolute;\r\n      width: 100%;\r\n      left: 122%;\r\n      top: 20px;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content {\r\n      float: right;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content::before {\r\n      top: 24px;\r\n      left: auto;\r\n      right: 100%;\r\n      border-color: transparent;\r\n      border-right-color: $light;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-read-more {\r\n      float: right;\r\n    }\r\n    .cd-timeline-block:nth-child(even) .cd-timeline-content .cd-date {\r\n      left: auto;\r\n      right: 122%;\r\n      text-align: right;\r\n    }\r\n    .cssanimations .cd-timeline-content.is-hidden {\r\n      visibility: hidden;\r\n    }\r\n    .cssanimations .cd-timeline-content.bounce-in {\r\n      visibility: visible;\r\n      -webkit-animation: cd-bounce-2 0.6s;\r\n      -moz-animation: cd-bounce-2 0.6s;\r\n      animation: cd-bounce-2 0.6s;\r\n    }\r\n  }\r\n  \r\n  @media only screen and (min-width: 1170px) {\r\n    .cssanimations .cd-timeline-block:nth-child(even) .cd-timeline-content.bounce-in {\r\n      -webkit-animation: cd-bounce-2-inverse 0.6s;\r\n      -moz-animation: cd-bounce-2-inverse 0.6s;\r\n      animation: cd-bounce-2-inverse 0.6s;\r\n    }\r\n  }", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// Directory page\r\n\r\n.social-links {\r\n    li {\r\n      a {\r\n        background: lighten($light,4%);\r\n        border-radius: 50%;\r\n        color: lighten($dark,15%);\r\n        display: inline-block;\r\n        height: 30px;\r\n        line-height: 30px;\r\n        text-align: center;\r\n        width: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Authentication\r\n  .auth-body-bg{\r\n    background-image: url(../images/auth-bg.jpg);\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    .bg-overlay {\r\n      background: rgba(51, 51, 51, 0.97);\r\n      width: 100%;\r\n      height: 100%;\r\n      position: absolute;\r\n      right: 0;\r\n      bottom: 0;\r\n      left: 0px;\r\n      top: 0px;\r\n    }\r\n  }\r\n\r\n  \r\n\r\n  .wrapper-page {\r\n    margin: 7.5% auto;\r\n    max-width: 460px;\r\n    position: relative;\r\n  \r\n    // .logo-admin {\r\n    //   font-size: 28px;\r\n    //   line-height: 70px;\r\n    // }\r\n    .auth-logo{\r\n      font-size: 28px;\r\n      line-height: 70px;\r\n      &.logo-light{\r\n        display: $display-none;\r\n      }\r\n      &.logo-dark {\r\n        display: $display-block;\r\n      }\r\n    }\r\n  }\r\n  // Error Page\r\n\r\n  .ex-page-content {\r\n    h1 {\r\n      font-size: 98px;\r\n      font-weight: 700;\r\n      line-height: 150px;\r\n      text-shadow: rgba(61, 61, 61, 0.3) 1px 1px, rgba(61, 61, 61, 0.2) 2px 2px, rgba(61, 61, 61, 0.3) 3px 3px;\r\n    }\r\n  }\r\n  \r\n"]}