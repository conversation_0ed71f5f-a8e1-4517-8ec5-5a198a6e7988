<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('offers', function (Blueprint $table) {
            

        //     $table->unsignedBigInteger('offer_maker_id')->nullable();
        //     $table->foreign('offer_maker_id')->references('id')->on('users')->onDelete('cascade');
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::table('offer', function (Blueprint $table) {
        //     //
        // });
    }
};
