

import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:boutigak/data/models/status.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';

class StatusService {
  static Future<List<Status>?> fetchMyItems() async {
    try {
      var response = await WebService.get(AvailableServices.myownitem);
   print(' ${response.body}');
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);


        

        log('items ${body.toList()}');
        
        List<Status> status = body.map((dynamic status) => Status.fromJson(status)).toList();
        return status;
      } else {
        // Get.snakbar("Error", "Failed to load items");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching items ${e.toString()}");
      return null;
    }
  }
  

  
}


