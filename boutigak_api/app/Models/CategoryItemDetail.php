<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CategoryItemDetail extends Model
{
    protected $table = 'category_item_detail';

    protected $fillable = [
        'label_en',
        'label_ar',
        'label_fr',
        'value',
        'item_id',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public function item()
    {
        return $this->belongsTo(Item::class);
    }
}
