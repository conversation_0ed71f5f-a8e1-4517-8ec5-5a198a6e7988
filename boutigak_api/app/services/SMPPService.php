<?php

namespace App\services;

use SmppClient;
use SmppAddress;
use SocketTransport;

class SMPPService
{
    protected SmppClient $client;

    public function __construct()
    {
        // Hard-code the SMPP host and port
        $host = '************';  // Replace with your SMPP host
        $port = 2775;            // Replace with your SMPP port

        // Create transport with hard-coded values
        $transport = new SocketTransport([$host], $port);
        $transport->open();

        $this->client = new SmppClient($transport);

        // Try binding with the Transmitter mode
        $bindResponse = $this->client->bindTransmitter('becod', 'b1c2d');  // Replace with your SMPP ID and password

        if ($bindResponse !== true) {
            // If Transmitter bind fails, try the Receiver bind mode
            $bindResponse = $this->client->bindReceiver('becod', 'b1c2d');  // Try Receiver mode as fallback
        }

        if ($bindResponse !== true) {
            throw new \Exception('Failed to bind to SMPP server');
        }
    }

    public function sendSms($to, $message)
    {
        try {
            // Sender and receiver addresses
            $from = new SmppAddress('ALIAS');  // Replace with your SMPP alias
            $toAddress = new SmppAddress($to);

            // Send SMS using default encoding (0 for ASCII)
            $sendResponse = $this->client->sendSMS($from, $toAddress, $message, 0);

            // Check if the sendSMS response is valid
            if ($sendResponse === false) {
                throw new \Exception('Failed to send SMS');
            }

            return 'SMS sent successfully';
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    public function __destruct()
    {
        $this->client->close();
    }
}
