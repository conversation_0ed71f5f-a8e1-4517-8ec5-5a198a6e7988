<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    protected $table = 'order_item';
    
    protected $fillable = [
        'order_id',
        'item_id',
        'name',
        'description',
        'price',
        'quantity',
        'image_url',
        'total'
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function originalItem()
    {
        return $this->belongsTo(Item::class, 'item_id');
    }
}
