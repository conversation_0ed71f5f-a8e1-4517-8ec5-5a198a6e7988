<?php

namespace App\Http\Controllers;


use Illuminate\Support\Facades\Log;
use App\Models\DeviceToken;
use Illuminate\Http\Request;

class DeviceTokenController extends Controller
{
    /**
     * Store a new device token.
     *
     * @OA\Post(
     *     path="/api/device-tokens",
     *     tags={"DeviceToken"},
     *     summary="Store a new device token",
     *     description="Stores a new device token for the authenticated user.",
     *     operationId="storeDeviceToken",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="token", type="string", example="your_device_token")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Token saved successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Token saved successfully")
     *         )
     *     ),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
{
    $this->validate($request, [
        'token' => 'required|string'
    ]);

    try {
        $user = auth()->user();
        
        // Delete all existing tokens for this user first
        DeviceToken::where('user_id', $user->id)->delete();


        DeviceToken::where('token', $request->token)->delete();
        
        // Create new device token
        $deviceToken = DeviceToken::create([
            'token' => $request->token,
            'user_id' => $user->id
        ]);
        
        Log::info('Device token updated', [
            'user_id' => $user->id,
            'token' => $request->token
        ]);

        return response()->json([
            'message' => 'Token saved successfully'
        ], 201);

    } catch (\Exception $e) {
        Log::error('Error saving device token:', [
            'error' => $e->getMessage(),
            'user_id' => auth()->id(),
            'token' => $request->token
        ]);

        return response()->json([
            'error' => 'Failed to save device token'
        ], 500);
    }
}

    /**
     * Get the device token for the authenticated user.
     *
     * @OA\Get(
     *     path="/api/device-tokens/current",
     *     tags={"DeviceToken"},
     *     summary="Get the device token for the authenticated user",
     *     description="Returns the device token for the authenticated user.",
     *     operationId="getTokenByCurrentUser",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="token", type="string", example="your_device_token")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Token not found")
     * )
     */
    public function getTokenByCurrentUser(): \Illuminate\Http\JsonResponse
    {
        $deviceToken = DeviceToken::query()->where('user_id', auth()->id())->first();
        return response()->json(['token' => $deviceToken->token], 200);
    }

    /**
     * Delete the device token for the authenticated user.
     *
     * @OA\Delete(
     *     path="/api/device-tokens",
     *     tags={"DeviceToken"},
     *     summary="Delete the device token for the authenticated user",
     *     description="Deletes the device token for the authenticated user.",
     *     operationId="destroy",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Token deleted successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="message", type="string", example="Token deleted successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Token not found")
     * )
     */
    public function destroy(): \Illuminate\Http\JsonResponse
    {
        $deviceToken = DeviceToken::query()->where('user_id', auth()->id())->first();
        $deviceToken->delete();
        return response()->json(['message' => 'Token deleted successfully'], 200);
    }
}
