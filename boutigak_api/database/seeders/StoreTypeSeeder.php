<?php

namespace Database\Seeders;

use App\Models\StoreType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class StoreTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        StoreType::query()->truncate();

        $storeTypes = [
            ['name_fr' => 'Parfumerie', 'name_en' => 'Perfumery', 'name_ar' => 'العطور'],
            ['name_fr' => 'Quincaillerie', 'name_en' => 'Hardware', 'name_ar' => 'الأجهزة'],
            ['name_fr' => 'Cosmétique', 'name_en' => 'Cosmetics', 'name_ar' => 'مستحضرات التجميل'],
            ['name_fr' => 'Sport', 'name_en' => 'Sport', 'name_ar' => 'الرياضة'],
            ['name_fr' => 'Meubles', 'name_en' => 'Furniture', 'name_ar' => 'الأثاث'],
            ['name_fr' => 'Électronique', 'name_en' => 'Electronics', 'name_ar' => 'الإلكترونيات'],
            ['name_fr' => 'Vêtements', 'name_en' => 'Clothing', 'name_ar' => 'الملابس'],
            ['name_fr' => 'Jouets', 'name_en' => 'Toys', 'name_ar' => 'الألعاب'],
            ['name_fr' => 'Librairie', 'name_en' => 'Bookstore', 'name_ar' => 'المكتبة'],
            ['name_fr' => 'Boulangerie', 'name_en' => 'Bakery', 'name_ar' => 'المخبز'],
        ];

        foreach ($storeTypes as $storeType) {
            StoreType::query()->create($storeType);
        }

        Log::info('Store types seeded successfully');
    }
}
