class SearchHistory {
  final int id;
  final int userId;
  final String query;
  final DateTime searchedAt;

  SearchHistory({
    required this.id,
    required this.userId,
    required this.query,
    required this.searchedAt,
  });

  factory SearchHistory.fromJson(Map<String, dynamic> json) {
    return SearchHistory(
      id: json['id'],
      userId: json['user_id'],
      query: json['query'],
      searchedAt: DateTime.parse(json['searched_at']),
    );
  }
}