#!/bin/bash

# Server Monitoring Dashboard Installation Script
# This script helps set up the monitoring dashboard

echo "🚀 Server Monitoring Dashboard Installation"
echo "==========================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "❌ This script should not be run as root for security reasons"
   exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check PHP
if ! command_exists php; then
    echo "❌ PHP is not installed. Please install PHP 7.4 or higher."
    exit 1
else
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    echo "✅ PHP $PHP_VERSION found"
fi

# Check required PHP extensions
echo "🔍 Checking PHP extensions..."

REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "json" "curl")
MISSING_EXTENSIONS=()

for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo "✅ $ext extension found"
    else
        echo "❌ $ext extension missing"
        MISSING_EXTENSIONS+=("$ext")
    fi
done

# Check SSH2 extension (optional but recommended)
if php -m | grep -q "^ssh2$"; then
    echo "✅ ssh2 extension found (recommended for remote monitoring)"
else
    echo "⚠️  ssh2 extension not found (will use mock data)"
    echo "   Install with: sudo apt-get install php-ssh2"
fi

if [ ${#MISSING_EXTENSIONS[@]} -ne 0 ]; then
    echo "❌ Missing required PHP extensions: ${MISSING_EXTENSIONS[*]}"
    echo "   Install with: sudo apt-get install php-${MISSING_EXTENSIONS[*]// / php-}"
    exit 1
fi

# Check web server
echo "🌐 Checking web server..."
if command_exists nginx; then
    echo "✅ Nginx found"
    WEB_SERVER="nginx"
elif command_exists apache2; then
    echo "✅ Apache found"
    WEB_SERVER="apache"
elif command_exists httpd; then
    echo "✅ Apache (httpd) found"
    WEB_SERVER="apache"
else
    echo "⚠️  No web server detected. Please install Nginx or Apache."
fi

# Get current directory
CURRENT_DIR=$(pwd)
echo "📁 Current directory: $CURRENT_DIR"

# Set permissions
echo "🔒 Setting file permissions..."
chmod 755 "$CURRENT_DIR"
chmod 644 "$CURRENT_DIR/config/servers.php" 2>/dev/null || echo "⚠️  config/servers.php not found"
chmod 644 "$CURRENT_DIR/index.html" 2>/dev/null || echo "⚠️  index.html not found"
chmod 755 "$CURRENT_DIR/api/"*.php 2>/dev/null || echo "⚠️  API files not found"

# Configuration setup
echo "⚙️  Configuration setup..."
echo ""
echo "Please update the following configuration files with your server details:"
echo "1. config/servers.php - Main server configuration"
echo "2. api/overview.php - Database connection settings"
echo "3. api/database.php - Database server SSH credentials"
echo "4. api/app-server-1.php - App Server 1 details"
echo "5. api/app-server-2.php - App Server 2 details"
echo ""

# Test API endpoints
echo "🧪 Testing API endpoints..."

if command_exists curl; then
    echo "Testing overview API..."
    if curl -s -f "http://localhost$(pwd | sed 's|/var/www/html||')/api/overview.php" > /dev/null; then
        echo "✅ Overview API accessible"
    else
        echo "⚠️  Overview API not accessible (this is normal if web server isn't configured yet)"
    fi
else
    echo "⚠️  curl not found, skipping API tests"
fi

# Web server configuration suggestions
echo ""
echo "🌐 Web Server Configuration:"
echo "================================"

if [ "$WEB_SERVER" = "nginx" ]; then
    echo "For Nginx, add this server block to your configuration:"
    echo ""
    cat << 'EOF'
server {
    listen 80;
    server_name monitoring.yourdomain.com;
    root /var/www/html/server-monitoring;
    index index.html;
    
    location /api/ {
        try_files $uri $uri/ =404;
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }
    }
}
EOF
elif [ "$WEB_SERVER" = "apache" ]; then
    echo "For Apache, add this virtual host:"
    echo ""
    cat << 'EOF'
<VirtualHost *:80>
    ServerName monitoring.yourdomain.com
    DocumentRoot /var/www/html/server-monitoring
    
    <Directory /var/www/html/server-monitoring>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
EOF
fi

echo ""
echo "🔐 Security Recommendations:"
echo "============================"
echo "1. Use SSH key authentication instead of passwords"
echo "2. Restrict access to the monitoring dashboard by IP"
echo "3. Enable HTTPS/SSL for secure access"
echo "4. Regularly update server credentials"
echo "5. Monitor access logs for suspicious activity"

echo ""
echo "📝 Next Steps:"
echo "=============="
echo "1. Update configuration files with your server details"
echo "2. Configure your web server (see suggestions above)"
echo "3. Test the dashboard: http://your-server/server-monitoring/"
echo "4. Set up SSL/HTTPS for production use"
echo "5. Configure monitoring alerts (optional)"

echo ""
echo "✅ Installation preparation complete!"
echo "📖 See README.md for detailed configuration instructions"

# Create a simple test script
cat > test-connection.php << 'EOF'
<?php
// Simple connection test script
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Extensions loaded:\n";
$extensions = ['pdo', 'pdo_mysql', 'ssh2', 'json', 'curl'];
foreach ($extensions as $ext) {
    echo "- $ext: " . (extension_loaded($ext) ? "✅ Loaded" : "❌ Not loaded") . "\n";
}

// Test database connection (update credentials)
try {
    $pdo = new PDO('mysql:host=************;dbname=test', 'username', 'password');
    echo "Database connection: ✅ Success\n";
} catch (Exception $e) {
    echo "Database connection: ❌ Failed - " . $e->getMessage() . "\n";
}
?>
EOF

echo ""
echo "🧪 A test script 'test-connection.php' has been created."
echo "   Run: php test-connection.php (after updating credentials)"
echo ""
