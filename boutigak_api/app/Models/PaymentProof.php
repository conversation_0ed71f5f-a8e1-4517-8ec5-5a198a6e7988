<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PaymentProof extends Model
{
    use HasFactory;

    protected $fillable = [
        'to_store',
        'user_id',
        'item_id',
        'store_id',
        'order_id',
        'provider_id',
        'screenshot',
        'amount',
        'reference_number',
        'status',
        'rejection_reason',
        'verified_by',
        'verified_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'verified_at' => 'datetime',
        'to_store' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function provider()
    {
        return $this->belongsTo(EPaymentProvider::class, 'provider_id');
    }

    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function getScreenshotUrlAttribute()
    {
        $sftpBaseUrl = config('assets.sftp.base_url', 'https://storage.boutigak.com ');
        return $sftpBaseUrl . '/' . ltrim($this->screenshot, '/');
    }
}