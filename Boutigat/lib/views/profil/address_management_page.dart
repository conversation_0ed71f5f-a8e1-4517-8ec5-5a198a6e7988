import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';


class AddressManagementPage extends StatefulWidget {
  @override
  State<AddressManagementPage> createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<AddressManagementPage> {
  final OrderController orderController = Get.find<OrderController>();

  Future<void> _getCurrentLocation() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return;
      }
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      orderController.updateLocation(
        LatLng(position.latitude, position.longitude),
      );
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  

    @override
  void initState() {
    super.initState();
    orderController.fetchUserLocations();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('my_addresses'.tr),
        backgroundColor: AppColors.primary,
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              return ListView.builder(
                itemCount: orderController.userLocations.length,
                itemBuilder: (context, index) {
                  Location location = orderController.userLocations[index];
                  return Card(
                    margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: Icon(Icons.location_on),
                      title: Text(location.name ?? ''),
                      subtitle: Text(location.address ?? ''),
                      trailing: IconButton(
                        icon: Icon(Icons.delete),
                        onPressed: () => _showDeleteDialog(context, location),
                      ),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await _getCurrentLocation();
          // On ouvre la nouvelle page en plein écran:
          Get.to(() => AddAddressPage());
         
        },
        child: Icon(Icons.add),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Location location) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('delete_address'.tr),
          content: Text('Are you sure you want to delete this address?'),
          actions: [
            TextButton(
              child: Text('cancel'.tr),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: Text('delete'.tr),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () async {
                Navigator.of(context).pop(); // Close dialog first
                
                // Show loading indicator
                Get.dialog(
                  Center(child: CircularProgressIndicator()),
                  barrierDismissible: false,
                );
                
                // Delete location
                bool success = await orderController.deleteLocation(location.id!);
                
                // Close loading indicator
                Get.back();
                
                if (success) {
                  // Refresh the locations list
                  await orderController.fetchUserLocations();
                }
              },
            ),
          ],
        );
      },
    );
  }
}
class AddAddressPage extends StatelessWidget {
  final OrderController orderController = Get.find<OrderController>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();

  AddAddressPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      
      body: Stack(
        children: [
         
          Obx(
            () => GoogleMap(
              initialCameraPosition: CameraPosition(
                target: orderController.currentPosition.value,
                zoom: 15,
              ),
              onTap: (LatLng position) {
                orderController.updateLocation(position);
              },
              markers: {
                Marker(
                  markerId: const MarkerId('selected_location'),
                  position: orderController.currentPosition.value,
                ),
              },
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
            ),
          ),

          
          Positioned(
            top: 40, // ajustez selon vos besoins
            left: 16,
            child: ClipOval(
              child: Container(
                color: AppColors.onBackground,
                child: IconButton(
                  iconSize: 20,
                  icon: Icon(
                    FontAwesomeIcons.x,
                    color: AppColors.background,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ),
          ),

         
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 243, 243, 243),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  
                  TextField(
                    controller: nameController,
                    decoration: InputDecoration(
                      hintText: 'location_name'.tr,
                      suffixIcon: const Icon(Icons.edit),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                 
                  TextField(
                    controller: addressController,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (_) => FocusScope.of(context).unfocus(),
                    decoration: InputDecoration(
                      hintText: 'address'.tr,
                      suffixIcon: const Icon(Icons.location_on),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),

                 
                 ElevatedButton(
  style: ElevatedButton.styleFrom(
   
    minimumSize: const Size(double.infinity, 48), 
    
    backgroundColor: Colors.black,
   
    foregroundColor: Colors.white,
    
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  ),
  onPressed: () async {
    if (nameController.text.isNotEmpty && addressController.text.isNotEmpty) {
      orderController.newLocationName.value = nameController.text;
      orderController.newLocationAddress.value = addressController.text;

      
      await orderController.saveNewLocation();

      Navigator.of(context).pop();
    } else {

    }
  },
  child:  Text('save'.tr),
),

                  const SizedBox(height: 30),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
