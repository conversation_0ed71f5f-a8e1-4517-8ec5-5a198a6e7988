class Order {
  // ... existing fields ...
  final String? paymentScreenshot;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? deliveryAddress;

  Order({
    // ... existing parameters ...
    this.paymentScreenshot,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.deliveryAddress,
  });

  // Update fromJson method
  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      // ... existing mappings ...
      paymentScreenshot: json['payment_screenshot'],
      deliveryLatitude: json['delivery_latitude']?.toDouble(),
      deliveryLongitude: json['delivery_longitude']?.toDouble(),
      deliveryAddress: json['delivery_address'],
    );
  }
}