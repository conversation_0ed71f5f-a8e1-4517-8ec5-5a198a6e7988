import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:boutigak/controllers/auth_controller.dart';


class AuthGuard extends GetMiddleware {
  final AuthController authController = Get.find<AuthController>();

  @override
  RouteSettings? redirect(String? route) {
    // Vérifie si l'utilisateur est authentifié
    if (!authController.isAuthenticated.value) {
      return const RouteSettings(name: '/login');
    }
    return null;
  }
}