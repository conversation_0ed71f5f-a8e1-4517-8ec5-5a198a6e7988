<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->integer('promotion_position')->nullable()->after('is_promoted');
        });
    }

    public function down()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->dropColumn('promotion_position');
        });
    }
};
