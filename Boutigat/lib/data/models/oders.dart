





class OrderItem {
  final int itemId;
  final int quantity;
  final double? price;
  final double? total;

  OrderItem({
    required this.itemId,
    required this.quantity,
     this.price,
     this.total,
  });

 factory OrderItem.fromJson(Map<String, dynamic> json) {
  return OrderItem(
    itemId: json['item_id'] ?? 0, // Default to 0 if null
    quantity: json['quantity'] ?? 0, // Default to 0 if null
    price: json['price'] != null ? double.tryParse(json['price'].toString()) : null, // Safely handle price as nullable double
    total: json['total'] != null ? double.tryParse(json['total'].toString()) : null, // Safely handle total as nullable double
  );
}

  Map<String, dynamic> toJson() {
    return {
      'item_id': itemId,
      'quantity': quantity,
      
    };
  }
}




class Order {
  final int? orderId;
  final int storeId;
  final List<OrderItem> items;
  final String? status;
  final String? deliveryCharge;
  final int? deliveryChargeId;
  final int? locationId;
  final bool isCashOnDelivery;
  final bool isPaid;
  final double? totalOrders;
  final int? userId;
  final String? userFirstName;
  final String? userLastName;
  final String? userPhone;
  final String? paymentScreenshot;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final String? deliveryAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool modifiedByStore;
  final String? storeName;
  final String? storeImage;

  Order({
    this.orderId,
    required this.storeId,
    required this.items,
    this.status,
    this.deliveryCharge,
    this.deliveryChargeId,
    this.locationId,
    this.isCashOnDelivery = false,
    this.isPaid = false,
    this.totalOrders,
    this.userId,
    this.userFirstName,
    this.userLastName,
    this.userPhone,
    this.paymentScreenshot,
    this.deliveryLatitude,
    this.deliveryLongitude,
    this.deliveryAddress,
    this.createdAt,
    this.updatedAt,
    this.modifiedByStore = false,
    this.storeName,
    this.storeImage,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      orderId: json['id'],
      storeId: json['store_id'] ?? 0,
      items: json['items'] != null 
          ? (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList()
          : [],
      status: json['status'] ?? '',
      deliveryCharge: json['delivery_charge'],
      deliveryChargeId: json['delivery_charge_id'],
      locationId: json['location_id'],
      isCashOnDelivery: json['is_cash_on_delivery'] ?? false,
      isPaid: json['is_paid'] ?? false,
      totalOrders: json['totalorders']?.toDouble(),
      userFirstName: json['userfirstname'],
      userLastName: json['userlastname'],
      paymentScreenshot: json['payment_screenshot'],
      deliveryLatitude: json['delivery_latitude'] != null 
          ? double.tryParse(json['delivery_latitude'].toString())
          : null,
      deliveryLongitude: json['delivery_longitude'] != null 
          ? double.tryParse(json['delivery_longitude'].toString())
          : null,
      deliveryAddress: json['delivery_address'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
      modifiedByStore: json['modified_by_store'] ?? false,
      storeName: json['store_name'],
      storeImage: json['store_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'store_id': storeId,
      'items': items.map((item) => item.toJson()).toList(),
      'delivery_charge_id': deliveryChargeId,
      'location_id': locationId,
      'is_cash_on_delivery': isCashOnDelivery,
      'is_paid': isPaid,
    };
  }
}























class DeliveryCharge {
  final int id;
  final String type;
  final String amount;
  

  DeliveryCharge({
    required this.id,
    required this.amount,
    required this.type,
  });

  
  factory DeliveryCharge.fromJson(Map<String, dynamic> json) {
    return DeliveryCharge(
      id: json['id'],
      amount: json['amount'],
      type: json['type']
    );
  }

  
}
