<?php

namespace App\Imports;

use App\Models\Brand;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;

class BrandImport implements ToModel
{
    public function model(array $row): Model|Brand|null
    {

        if (strtolower($row[0]) === 'name' || strtolower($row[1]) === 'category_id') {
            return null;
        }

        if (Brand::query()->where('name', $row[0])->exists()) {
            return null;
        }

        return new Brand([
            'name' => $row[0],
            'category_id' => $row[1],
        ]);
    }
}

