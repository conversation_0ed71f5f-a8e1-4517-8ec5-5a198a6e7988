import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:http_parser/http_parser.dart';

enum PossiblesHosts { test, prode, IOS , STORAGE , temp , S}

const Map<PossiblesHosts, String> hostURLs = {
  PossiblesHosts.test: "http://********:8000/",
  PossiblesHosts.prode: "https://www.boutigak.com/",
  PossiblesHosts.IOS: "http://***********:8000/",
  PossiblesHosts.STORAGE : "https://storage.boutigak.com/",
  PossiblesHosts.S : "https://www.boutigak.com",
  PossiblesHosts.temp : "https://storage.boutigak.com/",

};

String getCurrentBackendURL() {
  return hostURLs[PossiblesHosts.prode]!;
}

class AvailableServices {



  static const String publicSearch = 'api/items-public-search';
  static const String getUser = "api/auth/user";
  static const String popularCategories = "api/popular-categories";
  static const String register = "api/auth/register";
  static const String login = "api/auth/login";
  static const String logout = "api/auth/logout";
  static const String requestPasswordReset = "api/password/request-reset";
  static const String resetPassword = "api/password/reset";
  static const String deleteUser = "api/auth/delete-account";
  static const String recommended = "api/items/recommended";
  static const String itembyId = "api/items/{id}";
  static const String generalrecommended = "api/items/general-recommended";
  static const String items = "api/items";
  static const String itemsUpdate = "api/items-update";
  static const String deleteItem = "api/items/{id}/delete";
  static const String myownitem = "api/items/user-items-status";
  static const String likeditems = "api/liked-items";
  static const String brands = "api/brands";
  static const String categories = "api/categories";
  static const String search = "api/items/search";
  static const String searchHistory = "api/search-history";
  static const String changePassword = "api/change-password";

  static const String offers = "api/offers";
  static const String discussions = "api/user/discussions";
  static const String discussionsItem = "api/discussions";
  static const String updateProfile = "api/user/profile";
  static const String updateLanguage = "api/user/update-language";
  // New routes for stores
  static const String recommendedStores = "api/stores/recommended";
  static const String promotedStores = "api/stores/promoted-recommended";
  static const String generalrecomandedStores = "api/stores/general-recommended";
  static const String stores = "api/stores";
  static const String storeOrders = "api/order-store";
  static const String storeLocation = "api/stores/{id}/location";
  static const String myStoreLocation = "api/stores/my-store/location";
  static const String myStoreFavoritesCategories = "api/stores/favorites-categories";
  static const String storeAddFavoritesCategory = "api/stores/add-favorites-category";
  static const String postStoreItem = "api/store-items";
  static const String getMyStoreItem = "api/stores/get-my-store-items";
  static const String storeTypes = "api/store-types";
  static const String storeFavoritesCategories = "api/stores/{storeId}/favorites-categories";
  static const String storeItems = "api/stores/{storeId}/items";
  static const String likedItemsForStore = "api/stores/{storeId}/liked-items";
  static const String followUnfollowStore = "api/stores/follow/{storeId}";
  static const String updateStore = 'api/update-store';
  static const String updateStoreItems = 'api/stores/my-store/items';
  static const String deleteStoreItem = "api/stores/my-store/items/{itemId}";

  static const String myStore = "api/stores/info";

  static const String deleteStoreFavoriteCategory = "api/stores/favorite-category/{categoryId}";
  static const String followedStores = "api/followed-stores";
  static const String fcmToken = "api/device-tokens";
  // new routes for Promotion
  static const String setPromotionForItemInMyStore = "api/store/item/{itemId}/promotion";
  static const String addPromotionForMyStore = "api/stores/my-store/promotions";
  static const String removePromotionForMyStore = "api/stores/my-store/promotion";
  static const String removePromotionForItemInMyStore = "api/store/item/{itemId}/promotion";



  static const String promoCodeVerify = "api/promo-codes/calculate-item-price";







// new routes for order

static const String putOrderStatus = "api/orders-update-status";
static const String createOrder = "api/orders";
static const String payOrder = "api/payment-order";

static const String getmyOrders = "api/orders/mine";
static const String getMyStoreOrders = "api/order-store";
static const String getdeliveryCharge= "api/delivery-charges";

static const String validateOrder = 'api/orders/validate';
static const String updateOrderStatus = 'api/orders-update-status';

// new routes for payment 
static const String activePaymentProviders = 'api/payment-providers';
static const String paymentProviders= "api/payment-providers";
static const String addpaymentProviders= "api/stores/payment-providers/add";
static const String getpaymentProviders= "api/stores/payment-providers";
static const String paymentProof= "api/payment-proof";
// new routes for notifications 
static const String getlistnotifications = "api/notifications";

// new routes for otp
static const String sendOtp= "api/otp/generate";
static const String verifyOtp= "api/auth/verify-otp";
static const String storePaymentProviders = "api/stores";
static const String locations = "api/users/locations";
static const String bpayProcess = 'api/payments/bpay/process';
}



class WebService {
  
   static Future<String> getToken() async {

    
    AuthController authController = Get.find<AuthController>();
    
    // // Wait until the token is not empty
    // while (authController.token.value.isEmpty) {
    //   await Future.delayed(Duration(milliseconds: 100));
    // }
    
    return "Bearer ${authController.token.value}";
  }

  static Future<http.Response> get(
    String url, {
    Map<String, dynamic>? params,
    bool withToken = true,
  }) async {


   // log('in get methode ..');
    String token = withToken ? await getToken() : "";


    print('token in get ${token}');

    print('url in get ${getCurrentBackendURL() + url}');

    var result = await http.get(
      Uri.parse(getCurrentBackendURL() + url).replace(queryParameters: params),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept': 'application/json; charset=UTF-8',
        if (withToken) 'Authorization': token,
      },
    );
    // print('Response status code: ${result.headers}');

    // print('Response status code: ${result.statusCode}');
    // print('Response body: ${result.body}');

    return result;
  }


  static Future<http.Response> post(String url,
      {Object? body,
      bool? toBeEncrypted,
      bool withToken = true,
      Map<String, dynamic>? params}) async {
    String u = getCurrentBackendURL() + url;
    String requestBody = json.encode(body);

        String token = withToken ? await getToken() : "";



     log('uri ${u}');
    var result = await http.post(
      Uri.parse(u).replace(queryParameters: params),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept': 'application/json; charset=UTF-8',
        if (withToken) 'Authorization': token,
      },
      body: requestBody,
    );

    return result;
  }

static Future<http.Response> postOtp(String url,
      {Object? body,
      bool? toBeEncrypted,
      bool withToken = true,
      Map<String, dynamic>? params}) async {
    String u = "http://localhost:8080/$url";
    String requestBody = json.encode(body);

        String token = withToken ? await getToken() : "";

    var result = await http.post(
      Uri.parse(u).replace(queryParameters: params),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept': 'application/json; charset=UTF-8',
        if (withToken) 'Authorization': token,
      },
      body: requestBody,
    );

    return result;
  }




  static Future<http.Response> put(String url,
      {Map<String, dynamic>? body,
      bool? toBeEncrypted,
      bool withToken = true,
      Map<String, dynamic>? params}) async {
    String u = getCurrentBackendURL() + url;
    String requestBody = json.encode(body);

        String token = withToken ? await getToken() : "";

    var result = await http.put(
      Uri.parse(u).replace(queryParameters: params),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept': 'application/json; charset=UTF-8',
        if (withToken) 'Authorization': token,
      }, 
      body: requestBody,
    );
    return result;
  }






static Future<http.StreamedResponse> postMultipart(
  String url, {
  required Map<String, String> fields,
  required List<File> files,
  bool withToken = true,
  Function(int, int)? onSendProgress, // Add onSendProgress callback parameter
}) async {
  var uri = Uri.parse(getCurrentBackendURL() + url);
  var request = http.MultipartRequest('POST', uri);

  String token = withToken ? await getToken() : "";

  // Adding fields
  fields.forEach((key, value) {
    request.fields[key] = value;
  });

  // Adding files with progress tracking
  for (var file in files) {
    var length = await file.length();
    int totalBytesSent = 0;

    // Create a ByteStream with progress tracking
    var byteStream = http.ByteStream(
      file.openRead().transform(
        StreamTransformer.fromHandlers(
          handleData: (data, sink) {
            sink.add(data);
            totalBytesSent += data.length;
            if (onSendProgress != null) {
              onSendProgress(totalBytesSent, length); // Update progress
            }
          },
        ),
      ),
    );

    var multipartFile = http.MultipartFile(
      'images[]', // Key name, adjust if needed
      byteStream,
      length,
      filename: basename(file.path),
    );

    request.files.add(multipartFile);
  }

  if (withToken) {
    request.headers['Authorization'] = token;
  }

  request.headers['Accept'] = 'application/json';

  var response = await request.send();
  return response;
}



   static Future<http.StreamedResponse> poststoreMultipart(String url,
    {required Map<String, String> fields,
    required List<File> files,
    bool withToken = true}) async {
  try {
    var uri = Uri.parse(getCurrentBackendURL() + url);
    var request = http.MultipartRequest('POST', uri);

    fields.forEach((key, value) {
      request.fields[key] = value;
    });

    for (var i = 0; i < files.length; i++) {
      var file = files[i];
      var stream = http.ByteStream(file.openRead());
      var length = await file.length();
      var multipartFile = http.MultipartFile(
        'images[$i][image]',  
        stream,
        length,
        filename: basename(file.path),
      );
      request.files.add(multipartFile);
    }

      String token = withToken ? await getToken() : "";


    // Add Authorization header if needed
    if (withToken) {
      request.headers['Authorization'] = token;
    }

    // Add additional headers
    request.headers['Accept'] = 'application/json';

    // Send request and return response
    var response = await request.send();
    return response;

  } catch (e) {
    print('Error in poststoreMultipart: ${e.toString()}');
    rethrow;
  }
}
  static Future<http.StreamedResponse> putMultipart(
  String url, {
  required Map<String, String> fields,
  List<File>? files,
}) async {


    var uri = Uri.parse(getCurrentBackendURL() + url);

  var request = http.MultipartRequest('PUT', uri);
  
  // Add authorization header
  String? token = await getToken();
  if (token != null) {
    request.headers['Authorization'] = 'Bearer $token';
  }
  
  // Add fields
  request.fields.addAll(fields);
  
  // Add files if provided
  if (files != null && files.isNotEmpty) {
    for (int i = 0; i < files.length; i++) {
      var file = files[i];
      var stream = http.ByteStream(file.openRead());
      var length = await file.length();
      var multipartFile = http.MultipartFile(
        'images[$i][image]',
        stream,
        length,
        filename: file.path.split('/').last,
        contentType: MediaType('image', 'jpeg'),
      );
      request.files.add(multipartFile);
      
      // Add dimension field for each image (using a default value if not specified)
      request.fields['images[$i][dimmension]'] = '1024x1024';
    }
  }
  
  return await request.send();
}


static Future<http.Response> delete(String url, {bool withToken = true}) async {
    String token = withToken ? await getToken() : "";



    log('full url ${   Uri.parse(getCurrentBackendURL() + url)}');
    var result = await http.delete(
      Uri.parse(getCurrentBackendURL() + url),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Accept': 'application/json; charset=UTF-8',
        if (withToken) 'Authorization': token,
      },
    );

    // print('Response status code (DELETE): ${result.statusCode}');
    // print('Response body (DELETE): ${result.body}');

    return result;
  }
  static Future<http.Response> deleteWithBody(
  String url, {
  bool withToken = true,
  Map<String, dynamic>? body, // <-- le paramètre s’appelle "body"
}) async {
  String token = withToken ? await getToken() : "";

  var request = http.Request(
    'DELETE',
    Uri.parse(getCurrentBackendURL() + url),
  );

  request.headers['Content-Type'] = 'application/json; charset=UTF-8';
  request.headers['Accept'] = 'application/json; charset=UTF-8';
  if (withToken) {
    request.headers['Authorization'] = token;
  }

  // Si on a un body, on l’encode
  if (body != null) {
    request.body = jsonEncode(body);
  }

  // Envoyer la requête et construire la réponse
  final streamedResponse = await request.send();
  final response = await http.Response.fromStream(streamedResponse);

  return response;
}

// Add this method to the WebServices class
static Future<http.Response> getAds() {
  return http.get(Uri.parse(getCurrentBackendURL() + 'api/ads/active'));
}



}
