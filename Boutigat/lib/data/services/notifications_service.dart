import 'dart:convert';
import 'package:boutigak/data/models/notifications.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:http/http.dart' as http;

class NotificationService {
  static Future<NotificationResponse?> fetchNotifications({int page = 1}) async {
    try {


                var token = await WebService.getToken();

      final response = await http.get(
        Uri.parse('${hostURLs[PossiblesHosts.prode]}api/notifications?page=$page'),

          headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json; charset=UTF-8',
          'Authorization': token,
        },
      );

      if (response.statusCode == 200) {
        return NotificationResponse.fromJson(json.decode(response.body));
      } else {
        return null;
      }
    } catch (e) {
      print('Error fetching notifications: $e');
      return null;
    }
  }

  static Future<bool> markAsRead(int notificationId) async {
    try {


        var token = await WebService.getToken();

      final response = await http.post(
        Uri.parse('${hostURLs[PossiblesHosts.prode]}api/notifications/$notificationId/read'),


      headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          'Accept': 'application/json; charset=UTF-8',
          'Authorization': token,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error marking notification as read: $e');
      return false;
    }
  }
}
