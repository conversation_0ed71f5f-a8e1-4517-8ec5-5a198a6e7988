import 'dart:convert';
import 'package:boutigak/controllers/auth_controller.dart';
import 'package:boutigak/data/models/user.dart';
import 'package:boutigak/data/services/session_service.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/controllers/reset_password_controlleur.dart';
import 'package:boutigak/views/registerandlogin/login_page.dart';
import 'package:boutigak/views/widgets/otp_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';

class AuthService{

  static Future<bool> register(User user) async {

    var response =  await WebService.post(AvailableServices.register , body: user.toJson());
  print(" ============ response status: ${response.statusCode} ==========");
      print('Response Body: ${response.body}');
    if(response.statusCode == 200){
      return true;
    }else if(response.statusCode == 409){

   //   // Get.snakbar("Error", "Phone number already exists",backgroundColor:Colors.red);

    }else{
    //  // Get.snakbar("Error", "",backgroundColor:Colors.red);
    }
    return false;
  }

    static Future<Map<String,dynamic>?> login({required String phone, required String password}) async {

    var response =  await WebService.post(AvailableServices.login, body: {
      'phone':phone,
      'password':password
    });
      print(" login ============response status: ${response.statusCode}==========");
 // print(' ${response.body}');
    if(response.statusCode == 200){
      Map<String,dynamic> body = jsonDecode(response.body);
    return body;
    
    }else{
      if (response.statusCode == 403){
            Get.to(() => OtpPage(phoneNumber: phone));


      }
      // 405 user is deleted
      else if (response.statusCode == 405){
        Get.snackbar("Error", "Your account has been deleted", backgroundColor: Colors.red);


        await SessionService.clearPrefs();

        Get.to(() => LoginPage());

        
        return null;
      }
    }


    return null;
  }

  static Future<Map<String,dynamic>?> resetPassword({required String phone, required String password,required String passwordConfirmation,required String otp }) async {

    var response =  await WebService.post(AvailableServices.resetPassword, body: {
      'phone':phone,
      'otp': otp,
      'password':password,
      'password_confirmation': passwordConfirmation

    });
      print(" login ============response status: ${response.statusCode}==========");
 // print(' ${response.body}');
    if(response.statusCode == 200){
      Map<String,dynamic> body = jsonDecode(response.body);
    return body;
    
    }else{
      if (response.statusCode == 403){
            Get.to(() => OtpPage(phoneNumber: phone));


      }

      else {

      // TODO: Error message

   //   // Get.snakbar("Error", "",backgroundColor:Colors.red);

      }
    }


    return null;
  }









static Future<Map<String, dynamic>?> sendOtp({required String phoneNumber}) async {
  if (!phoneNumber.startsWith('00222')) {
      phoneNumber = '00222$phoneNumber';
    }
    try {
      var response = await WebService.post(AvailableServices.sendOtp, body: {
        'phoneNumber': phoneNumber,
      });

      print("sendOtp ============ response status: ${response.statusCode} ==========");
      // print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body); // Retourne les données de réponse (ex : un message de confirmation)
      } else {
        // Gestion des erreurs
     //   // Get.snakbar("Error", "Failed to send OTP", backgroundColor: Colors.red);
      }
    } catch (e) {
     // // Get.snakbar("Error", "An error occurred while sending OTP: ${e}", backgroundColor: Colors.red);
      print('Error details: ${e}');
    }
    return null;
  }

static Future<Map<String, dynamic>?> requestPasswordReset({required String phoneNumber}) async {
  
    try {
      var response = await WebService.post(AvailableServices.requestPasswordReset, body: {
        'phone': phoneNumber,
      });

      print("sendOtp ============ response status: ${response.statusCode} ==========");
       print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body); // Retourne les données de réponse (ex : un message de confirmation)
      } else {
        // Gestion des erreurs
     //   // Get.snakbar("Error", "Failed to send OTP", backgroundColor: Colors.red);
      }
    } catch (e) {
     // // Get.snakbar("Error", "An error occurred while sending OTP: ${e}", backgroundColor: Colors.red);
      print('Error details: ${e}');
    }
    return null;
  }


















  static Future<Map<String, dynamic>?> verifyOtp({required String phoneNumber, required String otp}) async {
  // if (!phoneNumber.startsWith('00222')) {
  //   phoneNumber = '00222$phoneNumber';
  // }




    print('phoneNumber Body: ${phoneNumber}');

  try {
    var response = await WebService.post(AvailableServices.verifyOtp, body: {
      'phone': phoneNumber,
      'otp': otp,
    });

    print("verifyOtp ============ response status: ${response.statusCode} ==========");
    print('Response Body: ${response.body}');

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
   //   // Get.snakbar("Error", "Failed to verify OTP", backgroundColor: Colors.red);
    }
  } catch (e) {
 //   // Get.snakbar("Error", "An error occurred while verifying OTP: $e", backgroundColor: Colors.red);
    print('Error details: $e');
  }
  return null;
}

static Future<Map<String, dynamic>?> updateLanguage({required String lang}) async {
  try {
    var response = await WebService.put(AvailableServices.updateLanguage, body: {
      'lang': lang,
    });

    if (response != null && response.statusCode == 200) {
      Map<String, dynamic> body = jsonDecode(response.body);
      return body;
        
    } else {
    //  // Get.snakbar("Error", "Failed to update language", backgroundColor: Colors.red);
      return null;
    }
  } catch (e) {
  //  // Get.snakbar("Error", "An unexpected error occurred", backgroundColor: Colors.red);
    return null;
  }
}
    static Future<void> logout() async {

      await WebService.post(AvailableServices.logout );
    
  }

  static Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    var response = await WebService.post(AvailableServices.changePassword, body: {
      'current_password': currentPassword,
      'new_password': newPassword,
      'new_password_confirmation': newPasswordConfirmation,
    });
       print(' ${response.body}');
    if (response.statusCode == 200) {
      return true;
    } else {
      String errorMessage = 'Failed to change password';
      try {
        var responseBody = jsonDecode(response.body);
        if (responseBody['error'] != null) {
          errorMessage = responseBody['error'];
        }
      } catch (e) {
        errorMessage = 'An error occurred';
        print("Error : $e");
      }
     // // Get.snakbar("Error", errorMessage, backgroundColor: Colors.red);
      return false;
    }
  }

  
   static Future<bool> updateProfile({
    required String firstName,
    required String lastName,
  }) async {
    try {
      var response = await WebService.put(
        AvailableServices.updateProfile,
        body: {
          'firstname': firstName,
          'lastname': lastName,
        },
      );
      print(" upadte profile ============response status: ${response.statusCode}==========");
  print(' ${response.body}');
      if (response.statusCode == 200) {
        return true;
      } else {
        String errorMessage = 'Failed to update profile';
        try {
          var responseBody = jsonDecode(response.body);
          if (responseBody['error'] != null) {
            errorMessage = responseBody['error'];
          }
        } catch (e) {
          errorMessage = 'An error occurred';
          print("Error : $e");
        }
   //     // Get.snakbar("Error", errorMessage, backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
   //   // Get.snakbar("Error", "An error occurred", backgroundColor: Colors.red);
      print("Exception: $e");
      return false;
    }
  }

static Future<bool> deleteAccount({
  required String password,
  required String deleteReason,
}) async {
  try {
    // On poste sur l’URL "api/auth/delete-account"
    var response = await WebService.deleteWithBody(
      AvailableServices.deleteUser,
      body: {
        'password': password,
        'delete_reason': deleteReason,
      },
    );

    print("deleteAccount response status: ${response.statusCode}");
    print("deleteAccount response body: ${response.body}");

    if (response.statusCode == 200) {
      // Succès
      return true;
    } else {
      // Gérer les cas d’erreur (400, 401, etc.)
      // Vous pouvez parser le body pour extraire le message d’erreur :
      try {
        var errorBody = jsonDecode(response.body);
        // Par exemple :
        Get.snackbar("Erreur", errorBody['error'] ?? "Une erreur est survenue");
      } catch (e) {
        Get.snackbar("Erreur", "Une erreur est survenue");
      }
    }
  } catch (e) {
    print("deleteAccount Exception: $e");
    // Gérer l'exception
  }
  return false;
}


// Fonction pour envoyer le token FCM à l'API
  static Future<bool> sendTokenToAPI(String tokenFCM) async {
    var response = await WebService.post(
      AvailableServices.fcmToken,  
      body: {"token": tokenFCM},
    );
     
      print('@@@@@@@@@@@ @@@@@@@ @@@@@  @@@response sent token to api ${response.body} !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
    if (response.statusCode == 200) {
      return true;
    } else if (response.statusCode == 422) {

    } else {

    }
    return false;
  }

  static Future<Map<String, dynamic>?> getUser() async {
    try {

      var response =  await WebService.get(AvailableServices.getUser);

      print('response get user ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }

      // handle 403 user is deleted
      if (response.statusCode == 403) {
        // delete auth controller token and user
        var authController = Get.find<AuthController>();
        authController.setToken('');
        authController.setUser(null);
        authController.isAuthenticated.value = false;

        // clear all auth controller stuff 
        

        
        await SessionService.clearPrefs();


        // Get.offAll(() => LoginPage());

        Get.snackbar("Error", "Your account has been deleted", backgroundColor: Colors.red);
        return null;
      }
      return null;
    } catch (e) {
      print('Error in getUser: $e');
      return null;
    }
  }

}
