<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ad extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'image_url',
        'target_type',
        'target_id',
        'target_url',
        'is_active',
        'start_date',
        'end_date',
        'store_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function item()
    {
        return $this->belongsTo(Item::class, 'target_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'target_id');
    }

    public function targetStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
    
    public function createdByStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }
}
