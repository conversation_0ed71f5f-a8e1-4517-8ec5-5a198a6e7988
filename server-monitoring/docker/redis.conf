# Redis configuration for Server Monitoring

# Basic settings
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence (for development)
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile ""

# Security (basic)
# requirepass your_redis_password  # Uncomment and set password for production

# Performance
tcp-backlog 511
databases 16

# Append only file (AOF) - disabled for development
appendonly no

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128
