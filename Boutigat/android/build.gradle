buildscript {
    ext.kotlin_version = '2.0.0'

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // ✅ Fix automatique pour les plugins Flutter sans `namespace` (AGP 8+)
    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (!project.hasProperty("namespace") && namespace == null) {
                        // Génère un namespace par défaut basé sur le nom du module
                        namespace project.name.replace("-", "_")
                    }
                }
            }
        }
    }
}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
