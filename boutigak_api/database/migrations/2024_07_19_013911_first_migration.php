<?php

use App\Enums\eItemStatus;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
    
    
        // Create brand table
        Schema::create('brand', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->timestamps();
        });

        // Create category table
        Schema::create('category', function (Blueprint $table) {
            $table->id();
            $table->string('title_en')->nullable();
            $table->string('title_ar')->nullable();
            $table->string('title_fr')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            
            $table->timestamps();

            $table->foreign('parent_id')->references('id')->on('category')->onDelete('cascade');
        });

        // Create location table
        Schema::create('location', function (Blueprint $table) {
            $table->id();
            $table->string('address')->nullable();
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            
            $table->timestamps();
        });

        // Create media table
        Schema::create('media', function (Blueprint $table) {
            $table->id();
            $table->string('type')->nullable();
            $table->string('url')->nullable();
            $table->double('aspect_ratio')->nullable();
            
            $table->timestamps();
        });

        // Create payment_type table
        Schema::create('payment_type', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            
            $table->timestamps();
        });

        // Create store_type table
        Schema::create('store_type', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            
            $table->timestamps();
        });

        // Create subscription_pack table
        Schema::create('subscription_pack', function (Blueprint $table) {
            $table->id();
            $table->integer('preiod_in_days')->nullable();
            
            $table->timestamps();
        });

        // Create users table
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('password');
            $table->string('phone')->unique();
            $table->string('firstname')->nullable();
            $table->string('lastname')->nullable();
            $table->string('invitationcode')->nullable();
            $table->string('gender')->nullable(); // male or female
            
            $table->rememberToken();
            $table->timestamps();
        });


        // Create category_detail table
        Schema::create('category_detail', function (Blueprint $table) {
            $table->id();
            $table->string('label_en')->nullable();
            $table->string('label_ar')->nullable();
            $table->string('label_fr')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            // 
            $table->timestamps();

            $table->foreign('category_id')->references('id')->on('category')->onDelete('cascade');
        });

        // Create store table
        Schema::create('store', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('type_id')->nullable();
            $table->unsignedBigInteger('location_id')->nullable();
            $table->timestamps();

            $table->foreign('location_id')->references('id')->on('location')->onDelete('cascade');
            $table->foreign('type_id')->references('id')->on('store_type')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Create user_score table
        Schema::create('user_score', function (Blueprint $table) {
            $table->id();
            $table->integer('score')->nullable()->default(0);
            $table->unsignedBigInteger('user_id')->nullable();
            
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });



        // stop here 
        // Create item table
        Schema::create('item', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->double('price')->nullable();
            $table->boolean('sold_out')->nullable();
            $table->string('condition')->nullable();

            $table->enum('status', eItemStatus::getAll(eItemStatus::class))->default(eItemStatus::CREATED);
            $table->text('rejection_reason')->nullable();
            $table->integer('quantity')->nullable();
            $table->unsignedBigInteger('brand_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            
            $table->timestamps();

            $table->foreign('brand_id')->references('id')->on('brand')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('category')->onDelete('cascade');
            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Create order table
        Schema::create('order', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->string('status')->nullable();
            $table->double('delivery_charge')->nullable();
            $table->unsignedBigInteger('location_id')->nullable();
            
            
            
            $table->foreign('location_id')->references('id')->on('location')->onDelete('cascade');
            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
            
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->timestamps();
        });

        // Create store_media table
        Schema::create('store_media', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('media_id')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            
            $table->timestamps();

            $table->foreign('media_id')->references('id')->on('media')->onDelete('cascade');
            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
        });

        // Create store_payment_code table
        Schema::create('store_payment_code', function (Blueprint $table) {
            $table->id();
            $table->string('paymentCode')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->unsignedBigInteger('payment_type_id')->nullable();
            
            $table->timestamps();

            $table->foreign('payment_type_id')->references('id')->on('payment_type')->onDelete('cascade');
            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
        });

        // Create store_promotion table
        Schema::create('store_promotion', function (Blueprint $table) {
            $table->id();
            $table->double('percentage')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            
            $table->timestamps();

            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
        });

        // Create store_subscription table
        Schema::create('store_subscription', function (Blueprint $table) {
            $table->id();
            $table->integer('preiod_in_days')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->unsignedBigInteger('subscription_pack_id')->nullable();
            $table->timestamp('activation_date')->nullable();
            $table->timestamp('starting_date')->nullable();
            $table->timestamps();

            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
            $table->foreign('subscription_pack_id')->references('id')->on('subscription_pack')->onDelete('cascade');
        });

        // Create category_item_detail table
        Schema::create('category_item_detail', function (Blueprint $table) {
            $table->id();
            $table->string('label_en')->nullable();
            $table->string('label_ar')->nullable();
            $table->string('label_fr')->nullable();
            $table->string('value')->nullable();
            $table->unsignedBigInteger('item_id')->nullable();
            
            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');


            $table->timestamps();
        });


        Schema::create('discution', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('item_id')->nullable();
            $table->unsignedBigInteger('buyer_id')->nullable();
            $table->unsignedBigInteger('seller_id')->nullable();
            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
            $table->foreign('buyer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('seller_id')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
        });

        Schema::create('discution_message', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('discution_id')->nullable();
            $table->unsignedBigInteger('sender_id')->nullable();
            $table->text('content')->nullable();
            $table->boolean('is_an_offer')->default(false);
            $table->double('price')->nullable();
            $table->foreign('discution_id')->references('id')->on('discution')->onDelete('cascade');
            $table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            $table->unsignedBigInteger('reply_discution_id')->nullable();
            $table->foreign('reply_discution_id')->references('id')->on('discution_message')->onDelete('cascade');
            $table->timestamps();
        });

        // Schema::create('offers', function (Blueprint $table) {
        //     $table->id();
        //     $table->unsignedBigInteger('item_id');
        //     $table->double('price')->nullable();
        //     $table->timestamps();
        //     $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
        // });


        // Create offer_discution table
     

        // Create item_media table
        Schema::create('item_media', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('media_id')->nullable();
            $table->unsignedBigInteger('item_id')->nullable();
            
            $table->timestamps();

            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
            $table->foreign('media_id')->references('id')->on('media')->onDelete('cascade');
        });

        // Create item_promotion table
        Schema::create('item_promotion', function (Blueprint $table) {
            $table->id();
            $table->double('percentage')->nullable();
            $table->unsignedBigInteger('item_id')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            
            $table->timestamps();

            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
        });

        // Create liked_item table
        Schema::create('liked_item', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('item_id')->nullable();
            
            $table->timestamps();

            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Create order_item table
        Schema::create('order_item', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('item_id')->nullable();
            $table->integer('quantity')->nullable();
            
            $table->timestamps();

            $table->foreign('item_id')->references('id')->on('item')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('order')->onDelete('cascade');
        });



        // Create payment table
        Schema::create('payment', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('type_id')->nullable();
            $table->double('amount')->nullable();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('transaction_id')->nullable();
            $table->integer('passcode')->nullable();
            $table->string('payer_phone_number')->nullable();
            
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('order')->onDelete('cascade');
            $table->foreign('type_id')->references('id')->on('payment_type')->onDelete('cascade');
        });

        // Create share_order table
        Schema::create('share_order', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('order_id')->nullable();
            
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('order')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });




        Schema::create('user_store_follows', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('store_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('store_id')->references('id')->on('store')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('discution_message');
        Schema::dropIfExists('share_order');
        Schema::dropIfExists('payment');
        Schema::dropIfExists('order_item');
        Schema::dropIfExists('liked_item');
        Schema::dropIfExists('item_promotion');
        Schema::dropIfExists('item_media');
        Schema::dropIfExists('discution');
        Schema::dropIfExists('category_item_detail');
        Schema::dropIfExists('store_subscription');
        Schema::dropIfExists('store_promotion');
        Schema::dropIfExists('store_payment_code');
        Schema::dropIfExists('store_media');
        Schema::dropIfExists('order');
        Schema::dropIfExists('item');
        Schema::dropIfExists('user_score');
        Schema::dropIfExists('store');
        Schema::dropIfExists('category_detail');
        Schema::dropIfExists('users');
        Schema::dropIfExists('subscription_pack');
        Schema::dropIfExists('store_type');
        Schema::dropIfExists('payment_type');
        Schema::dropIfExists('media');
        Schema::dropIfExists('location');
        Schema::dropIfExists('category');
        Schema::dropIfExists('brand');
    }
};


