<?php
/**
 * Server Monitoring Configuration
 *
 * Update these settings with your actual server details
 */

return [
    'database_server' => [
        'host' => '************', // Your database server IP
        'port' => 22, // SSH port
        'username' => 'root', // SSH username
        'password' => 'Boutigak@36666688@38407840', // SSH password
        'db_host' => '************',
        'db_port' => 5432, // PostgreSQL port
        'db_name' => 'boutigak_db',
        'db_username' => 'postgress',
        'db_password' => 'adminboutigak',
        'name' => 'PostgreSQL Database & Storage Server'
    ],

    'app_server_1' => [
        'host' => 'your_app_server_1_ip',
        'port' => 22,
        'username' => 'root',
        'password' => 'Boutigak@36666688@38407840',
        'laravel_path' => '/var/www/html',
        'name' => 'Laravel App Server 1'
    ],

    'app_server_2' => [
        'host' => 'your_app_server_2_ip',
        'port' => 22,
        'username' => 'root',
        'password' => 'Bo<PERSON>gak@36666688@38407840',
        'laravel_path' => '/var/www/html',
        'name' => 'Laravel App Server 2'
    ],

    // Monitoring settings
    'monitoring' => [
        'refresh_interval' => 30, // seconds
        'data_retention' => 24, // hours
        'alert_thresholds' => [
            'cpu_warning' => 70,
            'cpu_critical' => 85,
            'ram_warning' => 80,
            'ram_critical' => 90,
            'disk_warning' => 80,
            'disk_critical' => 90,
            'response_time_warning' => 1000, // ms
            'response_time_critical' => 2000, // ms
        ]
    ],

    // Email alerts (optional)
    'alerts' => [
        'enabled' => false,
        'email_to' => '<EMAIL>',
        'email_from' => '<EMAIL>',
        'smtp_host' => 'smtp.yourdomain.com',
        'smtp_port' => 587,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => 'your_smtp_password'
    ]
];
?>
