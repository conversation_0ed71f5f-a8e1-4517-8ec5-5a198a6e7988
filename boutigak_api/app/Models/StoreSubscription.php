<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreSubscription extends Model
{
    protected $table = 'store_subscription';

    protected $fillable = [
        'preiod_in_days',
        'store_id',
        'subscription_pack_id',
        'activation_date',
        'starting_date',
    ];

    protected $dates = [
        'activation_date',
        'starting_date',
        'created_at',
        'updated_at',
    ];

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function subscriptionPack()
    {
        return $this->belongsTo(SubscriptionPack::class);
    }
}
