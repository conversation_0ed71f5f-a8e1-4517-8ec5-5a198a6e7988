<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\CategoryItemDetail;
use App\Models\ItemMedia;
use App\Models\Media;
use Illuminate\Database\Seeder;
use App\Models\Item;
use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Define some sample data
        $itemsData = [
            [
                'title' => 'Item 1',
                'description' => 'Description for Item 1',
                'price' => 100.00,
                'condition' => 'New',
                'quantity' => 10,
                'sold_out' => false,
                'status' => 'active',
                'is_promoted' => true,
                'brand_id' => Brand::query()->first()->id,  // Ensure you have at least one brand in the DB
                'category_id' => Category::query()->first()->id,  // Ensure you have at least one category in the DB
                'user_id' => User::query()->first()->id,  // Ensure you have at least one user in the DB
                'store_id' => 1, // Assume store_id 1 is available, replace with actual logic
                'rejection_reason' => null,
                'category_item_details' => [
                    ['id' => null, 'value' => 'Detail 1 Value'],
                    ['id' => null, 'value' => 'Detail 2 Value'],
                ],
                'images' => [
                    'image_1.jpeg',
                    'image_2.jpeg'
                ]
            ],
            [
                'title' => 'Item 2',
                'description' => 'Description for Item 2',
                'price' => 150.00,
                'condition' => 'Used',
                'quantity' => 5,
                'sold_out' => false,
                'status' => 'pending',
                'is_promoted' => false,
                'brand_id' => Brand::query()->first()->id,
                'category_id' => Category::query()->first()->id,
                'user_id' => User::query()->first()->id,
                'store_id' => 1,
                'rejection_reason' => 'Not enough quality',
                'category_item_details' => [
                    ['id' => null, 'value' => 'Detail 1 Value'],
                ],
                'images' => [
                    'image_1.jpeg',
                    'image_2.jpeg'
                ]
            ]
        ];

        // Iterate through each item
        foreach ($itemsData as $itemData) {
            // Start transaction
            DB::beginTransaction();

            try {
                // Create the item
                $item = Item::query()->create([
                    'title' => $itemData['title'],
                    'description' => $itemData['description'],
                    'price' => $itemData['price'],
                    'condition' => $itemData['condition'],
                    'quantity' => $itemData['quantity'],
                    'sold_out' => $itemData['sold_out'],
                    'status' => $itemData['status'],
                    'is_promoted' => $itemData['is_promoted'],
                    'brand_id' => $itemData['brand_id'],
                    'category_id' => $itemData['category_id'],
                    'user_id' => $itemData['user_id'],
                    'store_id' => $itemData['store_id'],
                    'rejection_reason' => $itemData['rejection_reason'],
                ]);

                // Associate category item details
                foreach ($itemData['category_item_details'] as $detail) {
                    CategoryItemDetail::query()->create([
                        'item_id' => $item->id,
                        'value' => $detail['value'],
                    ]);
                }

                // Handle image uploads (assuming images are stored locally or URLs are valid)
                foreach ($itemData['images'] as $imageUrl) {
                    // Simulate fetching image from local or external URL
                    $imageContent = file_get_contents(storage_path('app/public/item_images/' . $imageUrl)); // Fetch image from storage
                    $imageName = basename($imageUrl); // Extract image name
                    $filePath = 'public/item_images/' . $imageName;

                    // Store the image in local storage
                    Storage::put($filePath, $imageContent);

                    // Create a media record for the image
                    $media = Media::query()->create([
                        'type' => 'image/jpeg',
                        'url' => Storage::url($filePath),
                    ]);

                    // Associate the media with the item
                    ItemMedia::query()->create([
                        'media_id' => $media->id,
                        'item_id' => $item->id,
                    ]);
                }

                // Commit transaction
                DB::commit();

            } catch (\Exception $e) {
                // Rollback transaction if an error occurs
                DB::rollback();
                Log::error('Error seeding item: ' . $e->getMessage());
            }
        }
    }
}
