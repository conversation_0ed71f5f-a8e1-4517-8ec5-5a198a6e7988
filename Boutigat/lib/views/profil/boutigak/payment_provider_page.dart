
import 'package:boutigak/controllers/payment_controller.dart';
import 'package:boutigak/data/models/payment_provider.dart';
import 'package:boutigak/data/services/payment_provider_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PaymentProvidersPage extends StatelessWidget {
  final PaymentController paymentController = Get.put(PaymentController());

  @override
  Widget build(BuildContext context) {
    // Charger les providers au démarrage de la page
    paymentController.fetchPaymentProviders();
    paymentController.fetchStorePaymentProviders();

    return Scaffold(
      appBar: AppBar(
        title: Text('Payment Providers'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () {
              // Action pour ajouter un nouveau fournisseur (peut-être ouvrir un autre écran)
              showDialog(
                context: context,
                builder: (context) => AddProviderDialog(paymentController: paymentController),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(() {
          if (paymentController.isLoading.isTrue) {
            return Center(child: CircularProgressIndicator());
          } else if (paymentController.isError.isTrue) {
            return Center(child: Text('Erreur lors du chargement des méthodes de paiement'));
          } else {
            String? selectedProviderValue = paymentController.selectedProvider.value.isEmpty
                ? null
                : paymentController.selectedProvider.value;

            if (paymentController.paymentProviders.isEmpty && paymentController.storePaymentProviders.isEmpty) {
              selectedProviderValue = null;
            }

            return Column(
  crossAxisAlignment: CrossAxisAlignment.start,
  children: [
    if (paymentController.storePaymentProviders.isNotEmpty) ...[
      Text(
        'Fournisseurs de paiement du magasin',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      SizedBox(height: 10),
      ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: paymentController.storePaymentProviders.length,
        itemBuilder: (context, index) {
          final storeProvider = paymentController.storePaymentProviders[index];

          print('provider logo ${storeProvider.providerLogo}');
          return ListTile(
           leading:  Image.network(storeProvider.providerLogo!, width: 40, height: 40),
           title: Text(storeProvider.providerName!),
          subtitle: Text('Code: ${storeProvider.paymentCode}'),
          trailing: Text('Phone number: ${storeProvider.phoneNumber}'),
          );
        },
      ),
      Divider(),
      SizedBox(height: 20),
    ] else ...[
      Text(
        'Pas encore de payment provider magasin',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      SizedBox(height: 10),
    ],
  


               
              ],
            );
          }
        }),
      ),
    );
  }
}
class PaymentForm extends StatelessWidget {
  final PaymentController paymentController;
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController paymentCodeController = TextEditingController();

  PaymentForm({required this.paymentController});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min, // Make the dialog more compact
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Obx(() => DropdownButtonFormField<String>(
          value: paymentController.selectedProvider.value.isEmpty
              ? null
              : paymentController.selectedProvider.value,
          hint: Text('Select Payment Method'),
          onChanged: (String? newValue) {
            if (newValue != null) {
              paymentController.selectedProvider.value = newValue;
            }
          },
          items: paymentController.paymentProviders.map((PaymentProvider provider) {
            return DropdownMenuItem<String>(
              value: provider.id.toString(),
              child: Row(
                children: [
                  if (provider.logoUrl != null && provider.logoUrl!.isNotEmpty)
                    Image.network(
                      provider.logoUrl!,
                      width: 24,
                      height: 24,
                      errorBuilder: (context, error, stackTrace) =>
                          Icon(Icons.payment, size: 24),
                    ),
                  SizedBox(width: 8),
                  Text(provider.name),
                ],
              ),
            );
          }).toList(),
          decoration: InputDecoration(
            labelText: 'Payment Method',
            border: OutlineInputBorder(),
          ),
        )),
        SizedBox(height: 16),
        TextFormField(
          controller: phoneController,
          decoration: InputDecoration(
            labelText: 'Phone Number',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.phone,
        ),
        SizedBox(height: 16),
        TextFormField(
          controller: paymentCodeController,
          decoration: InputDecoration(
            labelText: 'Payment Code',
            border: OutlineInputBorder(),
          ),
        ),
        SizedBox(height: 24),
        ElevatedButton(
          onPressed: () async {
            if (paymentController.selectedProvider.value.isEmpty ||
                (phoneController.text.isEmpty && paymentCodeController.text.isEmpty)) {
              Get.snackbar(
                'Error',
                'Please select a provider and provide either phone number or payment code',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red,
                colorText: Colors.white,
              );
              return;
            }

            // Create request body
            Map<String, dynamic> requestBody = {
              'provider_id': int.parse(paymentController.selectedProvider.value),
              'payment_code': paymentCodeController.text,
              'phone_number': phoneController.text,
            };

            // Add payment provider
            bool success = await PaymentService.addPaymentProvider(requestBody);

            if (success) {
              // Refresh the providers list
              await paymentController.fetchStorePaymentProviders();
              
              // Show success message
              Get.snackbar(
                'Success',
                'Payment provider added successfully',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );

              // Close the dialog
              Get.back();
            } else {
              Get.snackbar(
                'Error',
                'Failed to add payment provider',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red,
                colorText: Colors.white,
              );
            }
          },
          child: Text('Save'),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }
}


// Dialog pour l'ajout d'un nouveau provider (optionnel)
class AddProviderDialog extends StatelessWidget {
  final PaymentController paymentController;

  AddProviderDialog({required this.paymentController});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: PaymentForm(paymentController: paymentController),
      ),
    );
  }
}
