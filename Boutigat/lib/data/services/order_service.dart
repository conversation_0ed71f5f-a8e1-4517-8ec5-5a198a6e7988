
import 'dart:convert';
import 'dart:developer';

import 'package:boutigak/controllers/orders_controller.dart';
import 'package:boutigak/data/models/location.dart';
import 'package:boutigak/data/models/oders.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:get/get.dart';

class OrderService {
  // Create a new order
  static Future<bool> createOrder(Order order) async {
    try {
      var response = await WebService.post(
        AvailableServices.createOrder,
        body: order.toJson(),
      );

       print('Response create order body: ${response.body }');

       print('Response create order code: ${response.statusCode}');
      //  print('response body ++=++++++++++ ${response.body}+++++++++++++');
      if (response.statusCode == 201) {
        // Get.snakbar("Success", "Order successfully created");
        return true;
      } else {
        // Get.snakbar("Error", "Failed to create order. Status: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while creating the order: ${e.toString()}");
      return false;
    }
  }

  // Get the orders for the current user
  static Future<List<Order>?> fetchMyOrders() async {
    try {
      var response = await WebService.get(AvailableServices.getmyOrders);

       print('Response create order code: ${response.statusCode}');
       print('response body ++=++++++++++ ${response.body}+++++++++++++');
       
       
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<Order> orders = body.map((dynamic order) => Order.fromJson(order)).toList();
        return orders;
      } else {
        // Get.snakbar("Error", "Failed to load orders");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching your orders: ${e.toString()}");
       print('errrrrrrreuuuuur ++=+=-=-=-=-=${e.toString()}+++++++++++++');
      return null;
    }
  }

  // Get the orders for the current store
  static Future<List<Order>?> fetchMyStoreOrders(Map<String, dynamic> queryParams) async {

    print('query params $queryParams');
    
    try {
   

      var response = await WebService.get(AvailableServices.getMyStoreOrders , params: queryParams);
      
      print('Response  order code: ${response.statusCode}');

      print('Response  body order: ${response.body}');


      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        return body.map((dynamic order) => Order.fromJson(order)).toList();
      } else {
        return null;
      }
    } catch (e) {
      print('Error fetching store orders: ${e.toString()}');
      return null;
    }
  }

  

static Future<List<DeliveryCharge>?> fetchDeliveryCharge() async {
    try {
      var response = await WebService.get(AvailableServices.getdeliveryCharge);
          log('Response create order code: ${response.statusCode}');
       log('response body ++=++++++++++ ${response.body}+++++++++++++');
     
      if (response.statusCode == 200) {
        List<dynamic> body = jsonDecode(response.body);
        List<DeliveryCharge> deliveryCharge = body.map((dynamic deliveryCharge) => DeliveryCharge.fromJson(deliveryCharge)).toList();
        return deliveryCharge;
      } else {
        // Get.snakbar("Error", "Failed to load store types");
        return null;
      }
    } catch (e) {
      // Get.snakbar("Error", "An error occurred while fetching store types: ${e.toString()}");
      return null;
    }
  }

static Future<bool> updateOrderStatus(int orderId, String status) async {
  try {
    var response = await WebService.put(
      AvailableServices.putOrderStatus,
      body: {
        'id': orderId.toString(),
        'status': status,
      },
    );
    

    print('response updateOrderStatus  ${response.body}');

    print('response updateOrderStatus  status code ${response.statusCode}');

    if (response.statusCode == 200 || response.statusCode == 204) {
      // Get.snakbar("Success", "Order status updated to $status");
      return true;
    } else {
      // Get.snakbar("Error", "Failed to update order status. Status: ${response.statusCode}");
      return false;
    }
  } catch (e) {
    // Get.snakbar("Error", "An error occurred while updating the order status: ${e.toString()}");
    return false;
  }
}

static Future<bool> validateOrderWithModifications(
  int orderId,
  List<Map<String, dynamic>> modifiedItems,
) async {
  try {
    var response = await WebService.put(
      AvailableServices.updateOrderStatus,
      body: {
        'id': orderId.toString(),
        'status': 'ACCEPTED',
        'items': modifiedItems,
      },
    );

    print('Response validate order code: ${response.statusCode}');
    print('Response body: ${response.body}');

    if (response.statusCode == 200) {
      Get.snackbar("Success", "Order validated successfully");
      return true;
    } else {
      Get.snackbar("Error", "Failed to validate order. Status: ${response.statusCode}");
      return false;
    }
  } catch (e) {
    Get.snackbar("Error", "An error occurred while validating the order: ${e.toString()}");
    return false;
  }
}

  static Future<bool> declineOrder(int orderId) async {
    try {
      var response = await WebService.put(
        AvailableServices.updateOrderStatus,
        body: {
          'id': orderId.toString(),
        'status': 'REJECTED',
        },
      );

      print('Response decline order code: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        Get.snackbar("Success", "Order declined successfully");
        return true;
      } else {
        Get.snackbar("Error", "Failed to decline order. Status: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      Get.snackbar("Error", "An error occurred while declining the order: ${e.toString()}");
      return false;
    }
  }

  static Future<List<Location>?> fetchUserLocations() async {
    try {
      var response = await WebService.get(AvailableServices.locations);
      


      print('fetch response user locations ${response.body}');
      if (response.statusCode == 200) {
        List<dynamic> locationsData = jsonDecode(response.body);
        return locationsData.map((data) => Location.fromJson(data)).toList();
      } else {
        return null;
      }
    } catch (e) {
      print('Error fetching locations: $e');
      return null;
    }
  }

  static Future<Map<String, dynamic>?> storeUserLocation(Map<String, dynamic> locationData) async {
    try {
      var response = await WebService.post(
        AvailableServices.locations,
        body: locationData,
      );

      print('response store location ${response.body}');
      print('response store location ${response.statusCode}');
      

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        return null;
      }
    } catch (e) {
      print('Error storing location: $e');
      return null;
    }
  }

  static Future<bool> deleteUserLocation(int locationId) async {
    try {
      var response = await WebService.delete(
        '${AvailableServices.locations}/$locationId',
      );

      print('Delete location response: ${response.statusCode}');
      print('Delete location response body: ${response.body}');

      return response.statusCode == 200;
    } catch (e) {
      print('Error deleting location: $e');
      return false;
    }
  }
}
